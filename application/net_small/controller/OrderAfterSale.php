<?php
/**
 * Created by PhpStorm.
 * User: zxtdcyy
 * Date: 2021/06/11
 * Time: 3:11 PM
 */

namespace app\net_small\controller;


use app\common\model\bu\BuOrder;
use app\common\model\bu\BuOrderCommodity;
use app\common\model\bu\BuOrderGift;
use app\common\model\bu\BuOrderSettlement;
use app\common\model\db\DbAfterSaleOrders;
use app\common\model\db\DbOrderRefundReason;
use app\common\model\db\DbSystemValue;
use app\common\net_service\NetGiftOrder;
use app\common\net_service\NetOrder;
use app\common\net_service\NetUser;
use app\common\net_service\SendMailer;
use app\common\validate\OrderAfterSaleValidate;
use ForkModules\Traits\ResponseTrait;
use think\Exception;
use think\exception\HttpResponseException;
use think\Queue;
use tool\Logger;


/**
 * @title 订单中心-售后
 * @description 接口说明
 */
class OrderAfterSale extends Common
{
    use ResponseTrait;

    /**
     * @var BuOrder
     */
    private $order_model;
    /**
     * @var BuOrderCommodity
     */
    private $order_commodity_model;

    /**
     * OrderAfterSale constructor.
     */
    public function __construct()
    {
        parent::__construct();
        $this->order_model           = new BuOrder();
        $this->order_commodity_model = new BuOrderCommodity();
        if (!$this->user_id) {
            $response = $this->setResponseError('请登录!', 401)->send();
            throw new HttpResponseException($response);
        }
    }

    /**check
     * @title 退款&退货&换货v2
     * @description 售后类型不同,所必传的参数以及数据有差别,请遵照原型提交
     *
     * @param name:order_id type:int require:1 default:0 other: desc:订单ID
     * @param name:afs_reason_id type:int require:1 default:0 other: desc:售后原因id
     * @param name:afs_type type:int require:1 default:0 other: desc:服务类型1:仅退款,2:退货,3:换货
     * @param name:afs_reason_detail type:string require:0 default:'' other: desc:申请售后原因
     * @param name:afs_reason_pictures type:array require:0 default:'' other: desc:售后图片,地址数组拼接
     * @param name:user_info type:object require:0 default:'' other: desc:用户收货信息key为,name,phone,address
     *
     * @return 200: 成功
     * @return msg:提示信息
     * @return data:返回数据
     *
     * <AUTHOR>
     * @url /net-small/order/after-sale
     * @method POST
     *
     */
    public function create(OrderAfterSaleValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));

        if (empty($requestData['afs_type'])) {
            return $this->setResponseError('提交失败,请稍后重试')->send();
        }

        if ($requestData['afs_type'] == 1) {
            $result = $validate->scene("create_bk_money")->check($requestData);
        } else {
            $result = $validate->scene("create")->check($requestData);
        }

        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $order = $this->order_model->getOneByPk($requestData['order_id']);

        if (empty($order) || ($order['user_id'] != $this->user_id)) {
            return $this->setResponseError('提交失败,请稍后重试', 403)->send();
        }

        if (($order['order_source'] == 2 && $order['order_status'] <> 9) && ($requestData['afs_type'] == 1)) {
            return $this->setResponseError('拼团订单不可退款', 414)->send();
        }

        if (in_array($order['order_source'], BuOrder::$can_not_afs)) {
            return $this->setResponseError('该订单类型暂不支持售后', 414)->send();
        }

        if (!empty($order['receive_time']) && (strtotime($order['receive_time']) + 7 * 86400 < time())) {
            return $this->setResponseError('已超过可售后时间', 403)->send();
        }

        $afs_model = new DbAfterSaleOrders();

        //订单=>售后单 1对多,所以需要is_enable
        $where               = ['order_id' => $requestData['order_id'], 'is_enable' => 1];
        $where['afs_status'] = ['not in', [2, 3, 8, 11]];
        $r_record            = $afs_model->getOne(['where' => $where, 'order' => "id desc"]);
        if (!empty($r_record)) {
            return $this->setResponseError('售后单已存在', 403)->send();
        }
        //申请退款(2:已支付) 和 申请售后(已完成)的订单状态是不同的,需验证
        if ($requestData['afs_type'] == DbAfterSaleOrders::ONLY_REFUND) {
            $init_status = DbAfterSaleOrders::$afss_type['only_refund'][1];
            if (!in_array($order['order_status'], [2, 20, 21, 22, 23]) && !($order['order_status'] == 7 && in_array($order['order_source'], [13, 20, 21, 24]))) {
                if(!in_array($order['order_source'],[25,42])){
                    return $this->setResponseError('订单状态异常', 414)->send();
                }
            }
        } else {
            $init_status = ($requestData['afs_type'] == DbAfterSaleOrders::BACK_PRO) ? DbAfterSaleOrders::$afss_type['back_pro'][7] :
                DbAfterSaleOrders::$afss_type['chg_pro'][10];
            if ($order['order_status'] != 9 && !($order['order_status'] == 7 && in_array($order['order_source'], [24]))) {
                return $this->setResponseError('订单状态异常', 414)->send();
            }
        }

        $net_order = new NetOrder();
        $re        = $net_order->cardAfterSale($order);
        if ($re['code'] <> 200) {
            return $this->setResponseError($re['msg'], $re['code'])->send();
        }
        $afs_reason = DbOrderRefundReason::where('id', $requestData['afs_reason_id'])->find();
        $settle     = BuOrderSettlement::where('order_code', $order['order_code'])->column('cashier_settlement_no');

        if (in_array($order['promotion_source'], [2, 3])) {
            $order_ids = BuOrder::where(['parent_order_code' => $order['parent_order_code']])->column('id,order_code,parent_order_type is_refund_order');
        } else {
            $order_ids[] = [
                'id'              => $order['id'],
                'order_code'      => $order['order_code'],
                'is_refund_order' => 3,
            ];
            // 赠品券订单
            $net_gift = new NetGiftOrder();
            $re = $net_gift->giftOrderAfterSale($order);
            if ($re['code'] <> 200) {
                return $this->setResponseError($re['msg'], $re['code'])->send();
            }

            foreach ($re['data'] as $key => $datum) {
                $arr = [
                    'id'              => $datum['id'],
                    'order_code'      => $datum['order_code'],
                    'is_refund_order' => 3,
                ];
                array_push($order_ids, $arr);
            }
        }
        foreach ($order_ids as $v) {
            if (count($order_ids) == 1) $v['is_refund_order'] = 3; // 套装存在不拆单的情况
            $data = array(
                'afs_service_id'      => $order['dlr_code'] . $requestData['afs_type'] . date('ymdHis') . mt_rand(1000, 9999),
                'settlement_ids'      => empty($settle) ? "" : implode(",", $settle),
                'order_id'            => $v['id'],
                'afs_type'            => $requestData['afs_type'],
                'afs_reason'          => $afs_reason['msg'] ?? '',
                'afs_reason_detail'   => $requestData['afs_reason_detail'] ?? '',
                'afs_reason_pictures' => json_encode_cn($requestData['afs_reason_pictures'] ?? []),
                'user_info'           => json_encode_cn($requestData['user_info'] ?? []),
                'afs_status'          => $init_status,
                'operate_list'        => json_encode_cn(
                    [
                        ['afs_status' => $init_status, 'name' => $requestData['user_info']['name'] ?? $this->user['name'],
                         'desc'       => '申请售后', 'time' => date("Y-m-d H:i:s")]
                    ]
                ),
                'is_refund_order'     => $v['is_refund_order'] == 3 ? 1 : 0,
            );

            $afs_model->insertData($data);
            $afs_id = $afs_model->getLastInsID();
            //数据对接到售后中心
            $as_data = ['afs_id'             => $afs_id, 'order_code' => $v['order_code'], 'type' => 'create', 'user_id' =>
                $this->user_id, 'created_at' => time()];
            Queue::push('app\common\queue\AfterSale', json_encode($as_data), config('queue_type.after_sale'));

            if ($requestData['order_id'] == $v['id']) {
                $this_afs_id = $afs_id;
            }
        }
        return $this->setResponseData(['message' => 'ok', 'afs_id' => $this_afs_id ?? 0])->send();
    }

    /**
     * @title 售后单详情v2
     * @description 退货,换货afs_type:2:退货3:换货
     * @param name:order_id type:int require:1 default:0 other: desc:订单id
     * @param name:afs_id type:int require:0 default:0 other: desc:售后申请id
     *
     * @return 200: 成功
     * @return msg:提示信息
     * @return data:返回数据@!
     *
     * @data afs_type:类型 afs_data:售后单数据@ order:订单数据@ order_goods:订单商品数据@ afs_reason_list:商品列表字段名data@
     * @afs_data id:afs_id afs_service_id:售后服务码 afs_type:类型 afs_reason_detail:售后原因描述 afs_reason_pictures:售后图片 afs_status:售后状态 check_remark:官方审核 refund_money:退款 refund_points:退积分 platform_info:平台收货地址 user_info:用户地址 platform_waybill_info:平台发货信息 user_waybill_info:用户退货信息 created_date:申请时间
     * @order id:ID order_code:订单编码 order_status:订单状态 name:用户名称 phone:用户手机号 receipt_address:收货地址 logistics_mode:物流方式1:自提2:快递
     * @order_goods id:ID dlr_code:专营店编码 commodity_id:商品ID price:价格 count:数量 car_info:车信息 sku_info:sku信息 commodity_name:商品名称 commodity_pic:商品主图 actual_point:实际支付积分 actual_use_money:实际支付钱
     * @afs_reason_list id:主键id msg:退款
     *
     * <AUTHOR>
     * @url /net-small/order/after-sale
     * @method GET
     *
     */
    public function show(OrderAfterSaleValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("show")->check($requestData);

        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $order = $this->order_model->getOne([
            'where' => ['id' => $requestData['order_id'], 'is_enable' => 1, 'user_id' => $this->user_id],
        ]);
        if (!$order) {
            return $this->setResponseError('提交失败,请稍后重试')->send();
        }

        if ( ($order['logistics_mode'] == 2 && !in_array($order['order_status'], [2, 4, 9, 18, 23]))  && ($order['logistics_mode'] == 1 && $order['order_status'] == 7) && !($order['order_status'] == 7 && in_array($order['order_source'], [20, 21, 24])) ) {
            return $this->setResponseError('订单状态异常')->send();
        }

        //只有已收货可以进行拼团订单售后
        if ($order['order_source'] == 2 && $order['order_status']<>9) {
            return $this->setResponseError('拼团订单不可退款')->send();
        }

        $afs_model = new DbAfterSaleOrders();
        $afs_data  = $afs_model->getOne([
                'where' => ['order_id' => $order['id']],
                'order' => "id desc",
            ]) ?? [];
        $net_order = new NetOrder();
        $can_after = $net_order->can_after($order,$afs_data);
        $afs_data['can_after']       = $can_after['can_after'];
        $afs_data['show_after_type'] = $can_after['show_after_type'];
        //退货图片
        if (!empty($afs_data['afs_reason_pictures'])) {
            $afs_data['afs_reason_pictures'] = json_decode($afs_data['afs_reason_pictures'], true);
        }
        //平台换货信息
        if (!empty($afs_data['platform_info'])) {
            $afs_data['platform_info'] = json_decode($afs_data['platform_info'], true);
        }
        //用户提交的收货信息
        if (!empty($afs_data['user_info'])) {
            $afs_data['user_info'] = json_decode($afs_data['user_info'], true);
        }
        //平台发货的信息
        if (!empty($afs_data['platform_waybill_info'])) {
            $afs_data['platform_waybill_info'] = json_decode($afs_data['platform_waybill_info'], true);
            $way_bill = getExpressInfo($afs_data['platform_waybill_info']['number'], $afs_data['platform_waybill_info']['company'], $order['phone'] ?? '');
            $afs_data['best_new_waybill'] = $way_bill['result']['list'][0] ?? [];
        }
        //用户退货/换货补填快递信息
        if (!empty($afs_data['user_waybill_info'])) {
            $afs_data['user_waybill_info'] = json_decode($afs_data['user_waybill_info'], true);
        }

        if (!empty($afs_data['refund_info'])) {
            $afs_data['refund_info'] = json_decode($afs_data['refund_info'], true);
        }
        if (!empty($afs_data['operate_list'])) {
            $afs_data['operate_list'] = array_reverse(json_decode($afs_data['operate_list'], true));
        }
        $afs_data['after_type_desc'] = '商城非定制品支持7天无理由退换货，部分定制商品由于制作的特殊性，不支持7天无理由退换货。如您收到商品后，发现商品存在质量问题，可在签收商品之日起7天内联系客服反馈处理，非质量问题产生的退换货运费，需要买家承担。';//售后类型弹窗说明
        $afs_data['after_date_desc'] = '';
        if (!empty($afs_data['afs_status'])) {
            switch ($afs_data['afs_status']) {
                case 7:
                case 10:
                case 1:
                    $afs_data['after_date_desc'] = '提交后预计1-3个工作日内审核完成，如有疑问请联系在线客服';
                    break;
                case 4:
                    $afs_data['after_date_desc'] = '审核完成后预计3个工作日内退款到账，如有疑问请联系在线客服';
                    break;
            }
        }

        $afs_jd_arr =DbAfterSaleOrders::$afss_type['after_jd'];//售后进度.....
        if(isset($afs_data['afs_status'])){
            $afs_data['after_jd']  = isset($afs_jd_arr[$afs_data['afs_status']])?$afs_jd_arr[$afs_data['afs_status']]:'0';
        }else{
            $afs_data['after_jd'] = $afs_jd_arr[1];
        }
        if($afs_data['after_jd']=='0' || $afs_data['after_jd']=='8' || ($afs_data['after_jd']==5 && in_array($afs_data['afs_type'],[1,2]))){
            $afs_data['after_jd'] = $afs_data['afs_type'].'-'.$afs_data['after_jd'];
        }

        if(in_array($afs_data['after_jd'],['2-3','3-3'])){
            if($afs_data['after_jd']=='2-3' && $afs_data['user_waybill_info']){
                $afs_data['after_jd'] = '2-4';
            }
            if($afs_data['after_jd']=='3-3' && $afs_data['user_waybill_info']){
                $afs_data['after_jd'] = '3-4';
            }
        }

        $where           = ['order_code' => $order['order_code'], 'is_enable' => 1,'mo_sub_id'=>0];
        if (in_array($order['promotion_source'], [2, 3])){
            $where = ['parent_order_code' => $order['parent_order_code'], 'is_enable' => 1,'mo_sub_id'=>0];
            //前端根据订单金额显示了退款明细，这个应该是不对的，应该是后台的退款明细
            if($order['promotion_source']==2){
                $order = $this->order_model->getOne([
                    'where' => ['order_code' => $order['parent_order_code'], 'is_enable' => 1, 'user_id' => $this->user_id],
                ]);
            }
        }
        $order_goods     = $this->order_commodity_model->getList([
            'where' => $where,
            'field' => 'id,dlr_code,commodity_id,price,count,car_info,sku_info,
            commodity_name,commodity_pic,actual_point,actual_use_money,actual_price,pay_style,price,all_dis,pre_sale_id,sku_c_json,order_mail_type'
        ]);
        $refund_model    = new DbOrderRefundReason();
        $afs_reason_list = $refund_model->getList(['where' => ['is_enable' => 1], 'field' => 'id,msg', 'order' => 'sort asc']);
        foreach ($order_goods as $k => $v) {
            //预售的actual_price 不准，修改为价格-优惠
            if ($v['pre_sale_id']) {
                $order_goods[$k]['actual_price'] = sprintf("%.2f", ($v['price'] - $v['all_dis']));
            }
            if($v['sku_c_json']){
                $order_goods[$k]['sku_c_json'] = json_decode($v['sku_c_json'],true);
            }
        }
        $com =  new \app\common\net_service\Common();
//        $order_goods_new = $com->order_goods_spilt($order_goods);

        $data = [
            "afs_data"        => $afs_data,
            "order"           => $order,
            "order_goods"     => $order_goods,
            "afs_reason_list" => $afs_reason_list,
            "refund_type"     => (in_array($order['order_source'], [20, 21, 24]) || $order['logistics_mode'] == 1) ? 1 : 0,
        ];

        return $this->setResponseHeaders(['gsecurity' => config('gsecurity')])->setResponseData($data)->send();
    }

    /**
     * @title 售后单更新v2
     * @description 退货,换货
     * @param name:order_id type:int require:1 default:0 other: desc:订单id
     * @param name:afs_id type:int require:1 default:0 other: desc:售后单id
     * @param name:update_type type:int require:1 default:0 other: desc:更新类型1:是否取消售后;2:回填物流信息;3:确认收货
     * @param name:is_cancel type:int require:0 default:0 other: desc:是否取消,1取消:0否
     * @param name:user_waybill_info type:array require:0 default:0 other: desc:更新物流信息时必填company公司编码,company_name公司名称number物流单号
     *
     * @return 200: 成功
     * @return msg:提示信息
     * @return data:返回数据
     *
     * <AUTHOR>
     * @url /net-small/order/after-sale
     * @method PUT
     *
     */
    public function update(OrderAfterSaleValidate $validate)
    {
        $update_type = $this->request->put("update_type");
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));

        $cancel   = false;
        $received = false;
        if ($update_type == 1) {
            $cancel = true;
            $result = $validate->scene("update_cancel")->check($requestData);
        } else if ($update_type == 2) {
            $result = $validate->scene("update_waybill")->check($requestData);
        } else {
            $received = true;
            $result   = $validate->scene("update_receive")->check($requestData);
        }

        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $order = $this->order_model->getOneByPk($requestData['order_id']);
        if (empty($order) || ($order['user_id'] != $this->user_id) || empty($requestData['afs_id'])) {
            return $this->setResponseError('提交失败,请稍后重试', 403)->send();
        }

        $afsObj = new DbAfterSaleOrders();
        $where  = ['id' => $requestData['afs_id'], 'order_id' => $requestData['order_id'], 'is_enable' => 1];

        $afs_ids = [$requestData['afs_id']];
        if (!empty($cancel)) {
            $where['afs_status'] = ['in', [DbAfterSaleOrders::$afss_type['only_refund'][1],
                DbAfterSaleOrders::$afss_type['back_pro'][7], DbAfterSaleOrders::$afss_type['chg_pro'][10]]];
            $afs                 = $afsObj->getOne(['where' => $where, 'order' => "id desc"]);

            if (empty($afs)) {
                return $this->setResponseError('提交失败,请稍后重试', 403)->send();
            }

            $operate_list = json_decode($afs['operate_list'], true);
            array_push($operate_list, [
                'afs_status' => DbAfterSaleOrders::$afss_type['only_refund'][2],
                'name'       => $this->user['name'] ?? '',
                'desc'       => '取消售后',
                'time'       => date("Y-m-d H:i:s")
            ]);
            //取消售后
            if (in_array($order['promotion_source'], [2, 3])){ // 服务包取消退款也要全部取消
                $order_list = $this->order_model->getList(['where' => ['parent_order_code' => $order['parent_order_code']]]);
                $afs_ids    = $afs->where(['order_id' => ['in', array_column($order_list, 'id')]])->column('id');
            }else {
                $order_list = [$order];
            }

            $afs->saveData(
                [
                    'afs_status'   => DbAfterSaleOrders::$afss_type['only_refund'][2],
//                'is_enable'    => 0,
                    'operate_list' => json_encode_cn($operate_list)
                ], ['id' => ['in', $afs_ids]]
            );
                $net_order = new NetOrder();
            foreach ($order_list as $v) {
                $net_order->cardAfterSale($v, 2);
                //申请退款邮件
                $SendMailer = new SendMailer();
                $SendMailer->send_mail(['type' => 5, 'order_code' => $v['order_code']]);
            }

            //判断是否是赠品券订单
            $net_gift_order = new NetGiftOrder();
            $net_gift_order->cancelAfterGiftOrder($order);

        } else if (!empty($received)) {
            //确认收货
            $where['afs_status'] = ['in', [DbAfterSaleOrders::$afss_type['chg_pro'][13]]];
            $afs                 = $afsObj->getOne(['where' => $where, 'order' => "id desc"]);

            if (empty($afs)) {
                return $this->setResponseError('提交失败,请稍后重试', 403)->send();
            }

            $operate_list = json_decode($afs['operate_list'], true);
            array_push($operate_list, [
                'afs_status' => DbAfterSaleOrders::$afss_type['chg_pro'][14],
                'name'       => $this->user['name'] ?? '',
                'desc'       => '确认收货',
                'time'       => date("Y-m-d H:i:s")
            ]);

            $afs->saveData([
                'afs_status'   => DbAfterSaleOrders::$afss_type['chg_pro'][14],
                'operate_list' => json_encode_cn($operate_list)
            ], $where);

        } else {
            //填写物流信息
            $where['afs_status'] = ['in', [DbAfterSaleOrders::$afss_type['back_pro'][9],
                DbAfterSaleOrders::$afss_type['chg_pro'][12]]];
            $afs                 = $afsObj->getOne(['where' => $where, 'order' => "id desc"]);

            if (empty($afs)) {
                return $this->setResponseError('提交失败,请稍后重试', 403)->send();
            }

            $operate_list = json_decode($afs['operate_list'], true);
            array_push($operate_list, [
                'afs_status' => $afs['afs_status'],
                'name'       => $this->user['name'] ?? '',
                'desc'       => '回寄物流',
                'time'       => date("Y-m-d H:i:s")
            ]);

            $requestData['user_waybill_info']['time'] = date("Y-m-d H:i:s");

            $afs->saveData([
                'user_waybill_info' => json_encode_cn($requestData['user_waybill_info']),
                'operate_list'      => json_encode_cn($operate_list)
            ], $where);
        }

        foreach ($afs_ids as $v){
            //数据对接到售后中心
            $as_data = ['afs_id' => $v, 'type' => 'update', 'created_at' => time()];
            Queue::push('app\common\queue\AfterSale', json_encode($as_data), config('queue_type.after_sale'));
        }

        return $this->setResponseData('ok')->send();
    }

    /**
     * @title 快递公司v2
     * @description 获取快递公司列表
     *
     * @return 200: 成功 参考提示msg
     * @return msg:提示信息
     * @return data:返回数据@!
     *
     * @data company:公司编码 company_name:公司名称
     *
     * <AUTHOR>
     * @url /net-small/express
     * @method GET
     *
     */
    public function express()
    {
        $DbSystemValueObj = new DbSystemValue();
        $express_list     = $DbSystemValueObj->getList([
            'where' => ['value_type' => 13],
            'field' => 'value_code as company,county_name as company_name'
        ]);
        return $this->setResponseData($express_list)->send();
    }


    /**
     * 订单退款接口-不走审核
     * @param OrderAfterSaleValidate $validate
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function refundCreate(OrderAfterSaleValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        Logger::error('qs-after-sale-refund:', ['requestData' => $requestData, 'user' => $this->user]);
        if (empty($requestData['afs_type'])) {
            return $this->setResponseError('售后类型不能为空')->send();
        }

        if ($requestData['afs_type'] == 1) {
            $result = $validate->scene("create_bk_money")->check($requestData);
        } else {
            $result = $validate->scene("create")->check($requestData);
        }

        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $order = $this->order_model->getOneByPk($requestData['order_id']);
        if (empty($order) || ($order['user_id'] != $this->user_id)) {
            return $this->setResponseError('订单信息不存在', 403)->send();
        }

        if (($order['order_source'] == 2 && $order['order_status'] <> 9) && ($requestData['afs_type'] == 1)) {
            return $this->setResponseError('拼团订单不可退款', 414)->send();
        }

        if (in_array($order['order_source'], BuOrder::$can_not_afs)) {
            return $this->setResponseError('该订单类型暂不支持售后', 414)->send();
        }

        if (!empty($order['receive_time']) && (strtotime($order['receive_time']) + 7 * 86400 < time())) {
            return $this->setResponseError('已超过可售后时间', 403)->send();
        }

        $afs_model = new DbAfterSaleOrders();

        //订单=>售后单 1对多,所以需要is_enable
        $where               = ['order_id' => $requestData['order_id'], 'is_enable' => 1];
        $where['afs_status'] = ['not in', [2, 3, 8, 11]];
        $r_record            = $afs_model->getOne(['where' => $where, 'order' => "id desc"]);
        if (!empty($r_record)) {
            return $this->setResponseError('已申请售后，请不要重复操作', 403)->send();
        }

        //申请退款(2:已支付) 和 申请售后(已完成)的订单状态是不同的,需验证
        if ($requestData['afs_type'] == DbAfterSaleOrders::ONLY_REFUND) {
            $init_status = DbAfterSaleOrders::$afss_type['only_refund'][1];
            if (!in_array($order['order_status'], [2, 20, 21, 22]) && !($order['order_status'] == 7 && in_array($order['order_source'], [20, 21, 24]))) {
                return $this->setResponseError('订单状态异常', 414)->send();
            }
        } else {
            $init_status = ($requestData['afs_type'] == DbAfterSaleOrders::BACK_PRO) ? DbAfterSaleOrders::$afss_type['back_pro'][7] :
                DbAfterSaleOrders::$afss_type['chg_pro'][10];
            if ($order['order_status'] != 9 && !($order['order_status'] == 7 && in_array($order['order_source'], [24]))) {
                return $this->setResponseError('订单状态异常', 414)->send();
            }
        }


        $afs_reason = DbOrderRefundReason::where('id', $requestData['afs_reason_id'])->find();
        $settle     = BuOrderSettlement::where('order_code', $order['order_code'])->column('cashier_settlement_no');


        $time_now = date("Y-m-d H:i:s");
        $refund_money = $order['money'];
        $refund_points = $order['integral'];

        $data = array(
            'afs_service_id'      => $order['dlr_code'] . $requestData['afs_type'] . date('ymdHis') . mt_rand(1000, 9999),
            'settlement_ids'      => empty($settle) ? "" : implode(",", $settle),
            'order_id'            => $order['id'],
            'afs_type'            => $requestData['afs_type'],
            'afs_reason'          => $afs_reason['msg'] ?? '',
            'afs_reason_detail'   => $requestData['afs_reason_detail'] ?? '',
            'afs_reason_pictures' => json_encode_cn($requestData['afs_reason_pictures'] ?? []),
            'user_info'           => json_encode_cn($requestData['user_info'] ?? []),
            'operate_list'        => json_encode_cn(
                [
                    ['afs_status' => $init_status, 'name' => '会员：'.$order['name'],
                        'desc'       => '申请售后', 'time' => date("Y-m-d H:i:s")]
                ]
            ),
            'is_refund_order'     => 1,

            //自动退款部分
            'pre_refund_money'   => 0,//预售退款
            'pre_refund_points'  => 0,//预售退积分
            'refund_money'       => $refund_money,//退款金额
            'refund_points'      => $refund_points,//退积分
            'refund_info'        => json_encode_cn(['name' => $order['name'], 'time' => $time_now]),
            'refund_delivery_at' => $time_now,
            'modifier'           => $order['name'],//
            'afs_status'         => 4,//
        );

        try {
            // 退款 退优惠券 兰俊的方法
            $net_order = new NetOrder();
            $re        = $net_order->cardAfterSale($order, 1, 1);
            if ($re['code'] <> 200) {
                return $this->setResponseError($re['msg'])->send();
            }
            // 添加售后申请
            $afs_id = $afs_model->insertGetId($data);
            //数据对接到售后中心
            $as_data = [
                'afs_id' => $afs_id,
                'order_code' => $order['order_code'],
                'type' => 'create',
                'user_id' => $order['user_id'],
                'created_at' => time()
            ];
            if(empty($afs_id)){
                return $this->setResponseError('退款失败')->send();
            }

            $net = new NetOrder();
            $res = $net->refund($order['order_code'], $order['cashier_trade_no'], true);
            if ($res['code'] != 200) {
                return $this->setResponseError($res['msg'])->send();
            }

            Queue::push('app\common\queue\AfterSale', json_encode($as_data), config('queue_type.after_sale'));

            // 解冻非直接退款的取送车券
            $net_user = new NetUser();
            $setData = [
                'use_type' => 2, // 解冻
                'vin' => $order['order_vin'], // 下单的vin
                'card_code' => $order['pick_delivery_card_code'], // 领券后的卡券号码
                'act_id'  => '', // 领券后的卡券号码
            ];
            $res      = $net_user->setCardUse($this->user, $this->channel_type, $setData);
            if ($res['code'] <> 200) {
                return $this->setResponseError($res['msg'])->send();
            }

            if ($requestData['order_id'] == $order['id']) {
                $this_afs_id = $afs_id;
            }
            return $this->setResponseData(['message' => 'ok', 'afs_id' => $this_afs_id ?? 0])->send();

        } catch (Exception $e) {
            Logger::error('refundCreate:取送车卡券退款'.$e->getMessage());
            return $this->setResponseError($e->getMessage())->send();
        }



    }

    /**
     * 订单退款接口-不走审核（新）
     * @param OrderAfterSaleValidate $validate
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function refundCreateNew(OrderAfterSaleValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        Logger::error('qs-after-sale-refund:', ['requestData' => $requestData, 'user' => $this->user]);
        if (empty($requestData['afs_type'])) {
            return $this->setResponseError('售后类型不能为空')->send();
        }

        if ($requestData['afs_type'] == 1) {
            $result = $validate->scene("create_bk_money")->check($requestData);
        } else {
            $result = $validate->scene("create")->check($requestData);
        }

        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $order = $this->order_model->getOneByPk($requestData['order_id']);
        if (empty($order) || ($order['user_id'] != $this->user_id)) {
            return $this->setResponseError('订单信息不存在', 403)->send();
        }

        if (($order['order_source'] == 2 && $order['order_status'] <> 9) && ($requestData['afs_type'] == 1)) {
            return $this->setResponseError('拼团订单不可退款', 414)->send();
        }

        // 暂时只开放给新版取送车使用
        if ($order['pick_up_order_type'] != 2) {
            return $this->setResponseError('该订单类型暂不支持售后', 414)->send();
        }

        if (!empty($order['receive_time']) && (strtotime($order['receive_time']) + 7 * 86400 < time())) {
            return $this->setResponseError('已超过可售后时间', 403)->send();
        }

        $afs_model = new DbAfterSaleOrders();

        //订单=>售后单 1对多,所以需要is_enable
        $where               = ['order_id' => $requestData['order_id'], 'is_enable' => 1];
        $where['afs_status'] = ['not in', [2, 3, 8, 11]];
        $r_record            = $afs_model->getOne(['where' => $where, 'order' => "id desc"]);
        if (!empty($r_record)) {
            return $this->setResponseError('已申请售后，请不要重复操作', 403)->send();
        }

        //申请退款(2:已支付) 和 申请售后(已完成)的订单状态是不同的,需验证
        $init_status = DbAfterSaleOrders::$afss_type['only_refund'][1];
        if (in_array($order['order_status'], [1, 3, 8, 18])) {
            return $this->setResponseError('订单状态异常', 414)->send();
        }

        $afs_reason = DbOrderRefundReason::where('id', $requestData['afs_reason_id'])->find();
        $settle     = BuOrderSettlement::where('order_code', $order['order_code'])->column('cashier_settlement_no');


        $time_now = date("Y-m-d H:i:s");
        $refund_money = $order['money'];
        $refund_points = $order['integral'];

        $data = array(
            'afs_service_id'      => $order['dlr_code'] . $requestData['afs_type'] . date('ymdHis') . mt_rand(1000, 9999),
            'settlement_ids'      => empty($settle) ? "" : implode(",", $settle),
            'order_id'            => $order['id'],
            'afs_type'            => $requestData['afs_type'],
            'afs_reason'          => $afs_reason['msg'] ?? '',
            'afs_reason_detail'   => $requestData['afs_reason_detail'] ?? '',
            'afs_reason_pictures' => json_encode_cn($requestData['afs_reason_pictures'] ?? []),
            'user_info'           => json_encode_cn($requestData['user_info'] ?? []),
            'operate_list'        => json_encode_cn(
                [
                    ['afs_status' => $init_status, 'name' => '会员：'.$order['name'],
                        'desc'       => '申请售后', 'time' => date("Y-m-d H:i:s")]
                ]
            ),
            'is_refund_order'     => 1,

            //自动退款部分
            'pre_refund_money'   => 0,//预售退款
            'pre_refund_points'  => 0,//预售退积分
            'refund_money'       => $refund_money,//退款金额
            'refund_points'      => $refund_points,//退积分
            'refund_info'        => json_encode_cn(['name' => $order['name'], 'time' => $time_now]),
            'refund_delivery_at' => $time_now,
            'modifier'           => $order['name'],//
            'afs_status'         => 4,//
        );

        try {
            // 添加售后申请
            $afs_id = $afs_model->insertGetId($data);
            //数据对接到售后中心
            $as_data = [
                'afs_id' => $afs_id,
                'order_code' => $order['order_code'],
                'type' => 'create',
                'user_id' => $order['user_id'],
                'created_at' => time()
            ];
            if(empty($afs_id)){
                return $this->setResponseError('退款失败')->send();
            }

            $net = new NetOrder();
            $res = $net->refund($order['order_code'], $order['cashier_trade_no'], true);
            if ($res['code'] != 200) {
                return $this->setResponseError($res['msg'])->send();
            }

            Queue::push('app\common\queue\AfterSale', json_encode($as_data), config('queue_type.after_sale'));

            if ($requestData['order_id'] == $order['id']) {
                $this_afs_id = $afs_id;
            }
            return $this->setResponseData(['message' => 'ok', 'afs_id' => $this_afs_id ?? 0])->send();

        } catch (Exception $e) {
            Logger::error('refundCreateNew:不走审核退款'.$e->getMessage());
            return $this->setResponseError($e->getMessage())->send();
        }



    }

}
