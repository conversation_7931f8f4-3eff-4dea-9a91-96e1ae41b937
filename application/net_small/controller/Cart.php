<?php
/**
 * Created by PhpStorm.
 * User: lzx
 * Date: 2020/12/22
 * Time: 3:11 PM
 */

namespace app\net_small\controller;


use app\common\model\act\AcByDlrCode;
use app\common\model\act\AcInsuranceSpecialist;
use app\common\model\bu\BuCardReceiveRecord;
use app\common\model\bu\BuOrder;
use app\common\model\bu\BuShoppingCart;
use app\common\model\db\DbArea;
use app\common\model\db\DbCommodity;
use app\common\model\db\DbCommodityDlrType;
use app\common\model\db\DbCommoditySet;
use app\common\model\db\DbCommoditySetSku;
use app\common\model\db\DbCommoditySku;
use app\common\model\db\DbDlr;
use app\common\model\db\DbDlrGroup;
use app\common\model\db\DbFightGroupCommodity;
use app\common\model\db\DbFullDiscount;
use app\common\model\db\DbGiftCommodity;
use app\common\model\db\DbLimitDiscount;
use app\common\model\db\DbLimitDiscountCommodity;
use app\common\model\db\DbLog;
use app\common\model\db\DbNDiscount;
use app\common\model\db\DbNDiscountInfo;
use app\common\model\db\DbSeckillCommodity;
use app\common\model\db\DbSpecValue;
use app\common\model\e3s\E3sPartCarSeries;
use app\common\net_service\Common;
use app\common\net_service\NetCart;
use app\common\validate\Cart as CartValidate;
use app\common\net_service\NetGoods;
use think\Exception;
use think\exception\HttpResponseException;
use think\Queue;
use tool\Logger;

/**
 * @title 购物车
 * @description 接口说明
 */
class Cart extends \app\net_small\controller\Common
{

    private $shop_cart_model;

    public function __construct()
    {
        parent::__construct();
        $this->shop_cart_model = new BuShoppingCart();

        if (empty($this->user_id)) {
            $response = $this->setResponseError('请登录!', 401)->send();
            throw new HttpResponseException($response);
        }
    }

    /**
     * @title 添加购物车
     * @description 接口说明
     * @param name:commodity_id type:int require:1 default:0 other: desc:商品ID
     * @param name:sku_id type:int require:1 default:0 other: desc:商品规格ID
     * @param name:count type:int require:0 default:0 other: desc:对应数量
     * @param name:act_type_id type:int require:0 default:0 other: desc:活动类型ID1限时折扣2团购3满减4全积分折扣5套装6N件N折7预售8立减
     * @param name:act_id type:int require:0 default:0 other: desc:活动类型对应ID一个商品参与一个活动
     * @param name:mail_method type:int require:1 default:0 other: desc:快递或者到店1到店2快递
     *
     * @return 200:  成功
     * @return msg:提示信息
     * @return data:返回数据
     *
     * <AUTHOR>
     * @url /net-small/cart/cart
     * @method POST
     *
     */
    public function addCart(CartValidate $validate)
    {

        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        Logger::error('shop_card-add',$requestData);
        $result      = $validate->scene("cart")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $commodity_id       = $requestData['commodity_id'];//主商品SKU
        $shop_cart_id             = $requestData['shop_cart_id'] ?? '';
        $sku_id             = $requestData['sku_id'];
        $hit_type_code      = $requestData['hit_type_code'] ?? '';
        $count              = $requestData['count'] ?? '';
        $act_type_id        = isset($requestData['act_type_id']) ? $requestData['act_type_id'] : '';
        $act_id             = isset($requestData['act_id']) ? $requestData['act_id'] : '';
        $wi_act_type_id = $requestData['wi_act_type_id'] ?? '';
        $wi_act_id = $requestData['wi_act_id'] ?? '';
        $mail_method        = $requestData['mail_method'];
        $dd_dlr_code       = isset($requestData['dd_dlr_code'])?$requestData['dd_dlr_code']:'';//到店专营店
        $work_time_json       = isset($requestData['work_time_json'])?$requestData['work_time_json']:'';//工时JSON
        $distance       = isset($requestData['distance'])?$requestData['distance']:0;//公里数
        //加入了组合商品等内容的
        $goods_set_sku_model =  new DbCommoditySetSku();
        $add_json = isset($requestData['com_json'])?$requestData['com_json']:'';//写入JSON 商品ID，子商品ID，工时work_time_number，工时work_time_code,工时单价work_time_price,mail_method

        $gift_act_id = $requestData['gift_act_id'] ?? 0;
        $gift_card_id = $requestData['gift_card_id'] ?? 0;
        $gift_c_json = $requestData['gift_c_json'] ?? '';
        $source_special = $requestData['source_special'] ?? '';
        $vehicle_order_no = $requestData['vehicle_order_no'] ?? '';

        $source             = $this->source;
        if($mail_method==2){
            $work_time_json='';
        }
        if($mail_method==1 && (empty($dd_dlr_code) || $dd_dlr_code=='V0000')){
            return $this->setResponseError('专营店必填，请返回填写')->send();//
        }
        $dis_where = ['a.id'=>['in',$sku_id]];
        $dis_res = $goods_set_sku_model->getNewOneSetSku($dis_where,"c.dd_commodity_type");
        if($dis_res['dd_commodity_type']==3 && empty($distance)){
             //当前里程数不能为0
             return $this->setResponseError('里程必填',402)->send();
         }
        $where =  [
            'user_id'      => $this->user_id,
            'commodity_id' => $commodity_id,
            'is_enable'    => 1,
            'brand'    => $this->brand,//增加一套品牌
        ];
        $sku_c_json = '';
        $sku_c_json_cn_arr = [];
        $sub_skus = [];
        $sub_sku_info_arr =[];
        if($add_json && $add_json<>'[]'){
            if(is_array($add_json)){
                $cart_js =  $add_json;
            }else{
                $cart_js = json_decode($add_json,true);
            }

            $sub_sku_arr = [];
            foreach ($cart_js as $v){
                //主商品，sku+数量都一致的情况下才是一个商品，不一致的情况下就是两个不同的购物车了
                //增加一个字段存sku+数量
                $sub_sku_arr[$v['sku']]=[$v['count']];
                $sub_skus[] =$v['sku'];
                if($mail_method==1){
                    $work_time_number=isset($v['work_time_number'])?$v['work_time_number']:0;
                    $work_time_code=isset($v['work_time_code'])?$v['work_time_code']:'';
                    $work_time_price=isset($v['work_time_price'])?$v['work_time_price']:0;
                }else{
                    $work_time_number=0;
                    $work_time_code='';
                    $work_time_price=0;
                }
                $sub_sku_info_arr[$v['sku']]=[
                    'count'=>$v['count'],
                    'work_time_number'=>$work_time_number,
                    'work_time_code'=>$work_time_code,
                    'work_time_price'=>$work_time_price
                ];


            }
            ksort($sub_sku_arr);
            $sku_c_json = json_encode($sub_sku_arr,true);
            $where['sku_c_json'] = $sku_c_json;
            $where['dd_dlr_code'] = $dd_dlr_code;
            //sku--规格
            $sp_val_list =  $goods_set_sku_model->getSkuSpec(['where'=>['a.id'=>['in',$sub_skus]],'field'=>"a.id,c.sp_value_name,d.sp_name,flat.commodity_name,b.relate_car_work_hour,flat.dd_commodity_type,flat.is_grouped"]);
//            print_json($goods_set_sku_model->getLastSql());
            if($sp_val_list){
                foreach ($sp_val_list as $v){
                    if($v['is_grouped'] && !$add_json){
                        return $this->setResponseError('子商品必选',402)->send();//

                    }
                    $sku_c_json_cn_arr[$v['id']]['title'] = $v['commodity_name'];
                    $sku_c_json_cn_arr[$v['id']]['sku'] = $v['id'];
                    $sku_c_json_cn_arr[$v['id']]['count'] = $sub_sku_info_arr[$v['id']]['count'];
                    $sku_c_json_cn_arr[$v['id']]['work_time_number'] =$sub_sku_info_arr[$v['id']]['work_time_number'];
                    $sku_c_json_cn_arr[$v['id']]['work_time_code'] = $sub_sku_info_arr[$v['id']]['work_time_code'];
                    $sku_c_json_cn_arr[$v['id']]['work_time_price'] = $sub_sku_info_arr[$v['id']]['work_time_price'];
                    $sku_c_json_cn_arr[$v['id']]['sku_cn'][]= $v['sp_value_name'];
                }
            }
        }else{
            $where['sku_id'] = $sku_id;
        }

        $net_com =  new Common();
        $user_info = $net_com->getFriendBaseInfo($this->user);

        if (!empty($gift_act_id) ) {
            // 清空相同买赠活动，购物车记录
            $cart_d = ['gift_act_id' => 0, 'gift_c_json' => ''];
            $this->shop_cart_model->saveData(['gift_act_id' => 0, 'gift_c_json' => ''], ['gift_act_id' => $gift_act_id, 'user_id' => $this->user_id]);
        }
        if ($gift_c_json && $gift_c_json != '[]') {
            $dlr_model = new DbDlr();
            $commodity_model = new DbCommodity();
            $gift_sub_commodity_ids = [];
            $gift_sub_skus = [];
            foreach ($gift_c_json as $gift_c_key => $gift_c) {
                $gift_sub_skus[] = $gift_c['set_sku_id'];
                $gift_sub_commodity_ids[] = $gift_c['commodity_id'];
                if (!empty($gift_c['com_json'])) {
                    $gift_com_sub_skus = [];
                    foreach ($gift_c['com_json'] as $gift_group_c) {
                        $gift_com_sub_skus[] = $gift_group_c['sku'];
                    }
                    // 查询组合商品，子商品的规格信息
                    $gift_com_sp_val_list = $goods_set_sku_model->getSkuSpec(['where' => ['a.id' => ['in', $gift_com_sub_skus]], 'field' => "a.id,a.price,c.sp_value_name,d.sp_name,flat.commodity_name,flat.cover_image,b.sp_value_list"]);
                    $gift_com_sp_val_attr = [];
                    if ($gift_com_sp_val_list) {
                        foreach ($gift_com_sp_val_list as $gift_com_sp_v) {
                            $gift_com_sp_val_attr[$gift_com_sp_v['id']]['id'] = $gift_com_sp_v['id'];
                            $gift_com_sp_val_attr[$gift_com_sp_v['id']]['commodity_name'] = $gift_com_sp_v['commodity_name'];
                            $gift_com_sp_val_attr[$gift_com_sp_v['id']]['sku_cn'][] = $gift_com_sp_v['sp_value_name'];
                            $gift_com_sp_val_attr[$gift_com_sp_v['id']]['sp_value_list'] = $gift_com_sp_v['sp_value_list'];
                            $gift_com_sp_val_attr[$gift_com_sp_v['id']]['price'] = $gift_com_sp_v['price'];
                        }
                    }
                    // 填充组合商品
                    $gift_c_json[$gift_c_key]['price'] = 0;
                    foreach ($gift_c['com_json'] as $gift_group_c_key => $gift_group_c) {
                        $gift_c_json[$gift_c_key]['com_json'][$gift_group_c_key]['title'] = $gift_com_sp_val_attr[$gift_group_c['sku']]['commodity_name'] ?? '';
                        $gift_c_json[$gift_c_key]['com_json'][$gift_group_c_key]['sku_cn'] = $gift_com_sp_val_attr[$gift_group_c['sku']]['sku_cn'] ?? [];
                        $gift_c_json[$gift_c_key]['com_json'][$gift_group_c_key]['sp_value_list'] = $gift_com_sp_val_attr[$gift_group_c['sku']]['sp_value_list'] ?? '';
                        $gift_c_json[$gift_c_key]['com_json'][$gift_group_c_key]['sp_name'] = implode(',', $gift_c_json[$gift_c_key]['com_json'][$gift_group_c_key]['sku_cn']);
                        $gift_c_json[$gift_c_key]['price'] += (($gift_com_sp_val_attr[$gift_group_c['sku']]['price'] ?? 0) * ($gift_group_c['count'] ?? 1));
                    }
                }

                // 下单门店
                if (!empty($gift_c['dd_dlr_code'])) {
                    $dlr = $dlr_model->getOne(['where' => ['dlr_code' => $gift_c['dd_dlr_code']]]);
                    $gift_c_json[$gift_c_key]['dd_dlr_name'] = $dlr['dlr_name'] ?? '';
                }
            }
            // 查询赠品商品的规格信息
            $gift_sp_val_list = $goods_set_sku_model->getSkuSpec(['where' => ['a.id' => ['in', $gift_sub_skus]], 'field' => "a.id,a.price,c.sp_value_name,d.sp_name,flat.commodity_name,flat.cover_image,b.sp_value_list,b.relate_car_ids"]);
            $gift_sp_val_attr = [];
            $gift_commodity_list = $commodity_model->getList_old(['where' => ['id' => ['in', $gift_sub_commodity_ids]]]);
            $gift_commodity_attr = [];
            foreach ($gift_commodity_list as $gift_commodity) {
                $gift_commodity_attr[$gift_commodity['id']] = $gift_commodity;
            }

            if ($gift_sp_val_list) {
                foreach ($gift_sp_val_list as $gift_sp_val) {
                    $gift_sp_val_attr[$gift_sp_val['id']]['id'] = $gift_sp_val['id'];
                    $gift_sp_val_attr[$gift_sp_val['id']]['price'] = $gift_sp_val['price'];
                    $gift_sp_val_attr[$gift_sp_val['id']]['cover_image'] = $gift_sp_val['cover_image'];
                    $gift_sp_val_attr[$gift_sp_val['id']]['commodity_name'] = $gift_sp_val['commodity_name'];
                    $gift_sp_val_attr[$gift_sp_val['id']]['sku_cn'][] = $gift_sp_val['sp_value_name'];
                    $gift_sp_val_attr[$gift_sp_val['id']]['sp_value_list'] = $gift_sp_val['sp_value_list'];

                    if ($gift_sp_val['relate_car_ids']) {
                        $gift_sp_val_attr[$gift_sp_val['id']]['car_18n'] = $user_info['car_config_code'];
                        $gift_sp_val_attr[$gift_sp_val['id']]['cart_car_info'] = $user_info['car_type_name'];
                    }
                }
            }

            // 填充赠品商品
            foreach ($gift_c_json as $gift_c_key => $gift_c) {
                $gift_c_json[$gift_c_key]['title'] = $gift_commodity_attr[$gift_c['commodity_id']]['commodity_name'] ?? '';
                $gift_c_json[$gift_c_key]['sku_cn'] = $gift_sp_val_attr[$gift_c['set_sku_id']]['sku_cn'] ?? [];
                if (empty($gift_c['com_json'])) {
                    $gift_c_json[$gift_c_key]['price'] = $gift_sp_val_attr[$gift_c['set_sku_id']]['price'] ?? 0;
                }
                $gift_c_json[$gift_c_key]['cover_image'] = $gift_commodity_attr[$gift_c['commodity_id']]['cover_image'] ?? '';
                $gift_c_json[$gift_c_key]['sp_value_list'] = $gift_sp_val_attr[$gift_c['set_sku_id']]['sp_value_list'] ?? '';
                $gift_c_json[$gift_c_key]['sp_name'] = implode(',', $gift_c_json[$gift_c_key]['sku_cn']);

                $gift_c_json[$gift_c_key]['car_18n'] = $gift_sp_val_attr[$gift_c['set_sku_id']]['car_18n'] ?? '';
                $gift_c_json[$gift_c_key]['cart_car_info'] = $gift_sp_val_attr[$gift_c['set_sku_id']]['cart_car_info'] ?? '';
            }
        }

        if($shop_cart_id){
            $where = ['id'=>$shop_cart_id,'user_id'=>$this->user_id];
        }

        $params['where']    = $where;
        $shopping_cart_info = $this->shop_cart_model->getOne($params);    //购物车信息
        $act_json           = '';
        if ($act_type_id) {
            $act_json = json_encode([$act_type_id => $act_id]);
        }
        $wi_act_json = '';
        if ($wi_act_type_id) {
            $wi_act_json = json_encode([$wi_act_type_id => $wi_act_id]);
        }
        $vin = $user_info['user_status']==2?$user_info['vin']:'';

        if (empty($shopping_cart_info)) {
            $data_insert['user_id']         = $this->user_id;
            $data_insert['dlr_code']        = $this->channel_type;
            $data_insert['name']            = $this->user['name'];
            $data_insert['car_series_id']   = $this->user['car_series_id'];
            $data_insert['commodity_id']    = $commodity_id;
            $data_insert['sku_id']          = $sku_id;
            $data_insert['source']          = $source;
            $data_insert['modifier']        = "a_s_c".rand(1000,9999);
            $data_insert['count']           = $count;
            $data_insert['act_json']        = $act_json;
            $data_insert['wi_act_json'] = $wi_act_json;
            $data_insert['order_mail_type'] = $mail_method;
            $data_insert['dd_dlr_code']     = $dd_dlr_code;
            $data_insert['sku_c_json']      = $sku_c_json;
            $data_insert['sku_c_json_cn']      = $sku_c_json_cn_arr?json_encode($sku_c_json_cn_arr):'';
            $data_insert['work_time_json']      = $work_time_json?json_encode($work_time_json):'';

            $data_insert['gift_act_id'] = $gift_act_id;
            $data_insert['gift_card_id'] = $gift_card_id;
            if ($gift_c_json && $gift_c_json != '[]') {
                $data_insert['gift_c_json'] = json_encode($gift_c_json);
            } else {
                $data_insert['gift_c_json'] = '';
            }
            $data_insert['brand']      = $this->brand;//增加一套品牌
            $data_insert['channel_type']      = $this->channel_type;//增加渠道
            $data_insert['car_18n']      = $user_info['car_config_code'];//18位
            $data_insert['cart_car_info']      = $user_info['car_type_name'];//车型
            $data_insert['hit_type_code'] = $hit_type_code;
            $data_insert['order_vin'] = $vin;
            $data_insert['distance'] = $distance;
            $data_insert['source_special'] = $source_special;
            $data_insert['is_empower'] = $this->user['is_empower'];
            $data_insert['vehicle_order_no'] = $vehicle_order_no;

            $res                            = $this->shop_cart_model->insertGetId($data_insert);

            $data_insert['op_type'] = 1;
            $data_insert['origin_id'] = $res;
            Queue::push('app\common\queue\ShopingCartChange', json_encode($data_insert),config('queue_type.order_change'));
            //增加购物车详情表-
            //$sc_detail_model =  new BuShoppingCartDetail();
            //$data_insert['sc_id']=$res;
            //$sc_detail_model->insertData($data_insert);
            $go_res                         = 1;
        } else {
            $data_save = array('act_json' => $act_json,'dd_dlr_code'=>$dd_dlr_code, 'order_mail_type' => $mail_method, 'cart_car_info' => $user_info['car_type_name'],'last_updated_date'=>date('Y-m-d H:i:s'),'modifier' => 'u_s_a1'.rand(1000,9999),'work_time_json'=>$work_time_json?json_encode($work_time_json):'','car_18n'=>$user_info['car_config_code'],'order_vin'=>$vin,'distance'=> $distance,'is_empower'=> $this->user['is_empower'],'car_series_id'=>$this->user['car_series_id']

            );
            if (isset($requestData['gift_act_id'])) {
                $data_save['gift_act_id'] = $gift_act_id;
            }
            if (isset($requestData['gift_card_id'])) {
                $data_save['gift_card_id'] = $gift_card_id;
            }
            if (isset($requestData['gift_c_json'])) {
                if ($gift_c_json && $gift_c_json != '[]') {
                    $data_save['gift_c_json'] = json_encode($gift_c_json);
                } else {
                    $data_save['gift_c_json'] = '';
                }
            }
            if($shopping_cart_info['car_series_id']!=$this->user['car_series_id'] && $shopping_cart_info['gift_card_id']){
                $data_save['gift_c_json'] = '';
                $data_save['gift_card_id'] = '';
            }
            if ($count) {
                $shopping_cart_info['count'] += $count;
                $data_save['count'] = $shopping_cart_info['count'];
            }
            $where  = array('id' => $shopping_cart_info['id']);

            $res    = $this->shop_cart_model->saveData($data_save, $where);

            $go_res = 0;
        }
        if ($res) {
            return $this->setResponseData('ok')->send();
        } else {
            return $this->setResponseError('添加失败')->send();
        }
    }


    /**
     * @title 更新购物车
     * @description 接口说明
     * @param name:shop_cart_ids type:string require:1 default:0 other: desc:购物车ID，从列表获取多个使用,隔开
     * @param name:counts type:string require:0 default:0 other: desc:数量多个使用,隔开更新数量而不是++++
     * @param name:sku_ids type:string require:0 default:0 other: desc:规格ID多个用,隔
     * @param name:mail_methods type:int require:1 default:0 other: desc:快递或者到店1到店2快递多个用,隔
     *
     * @return 200:  成功
     * @return msg:提示信息
     * @return data:返回数据
     *
     * <AUTHOR>
     * @url /net-small/cart/cart
     * @method PUT
     *
     */
    public function putCart(CartValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("putCart")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $shop_cart_id = $requestData['shop_cart_ids'];
        $count        = $requestData['counts'] ?? '';
        $sku_ids      = $requestData['sku_ids'] ?? '';
        $mail_method      = $requestData['mail_methods'] ?? '';
        $work_time_json       = $requestData['work_time_json']??'';//工时JSON
        if($mail_method==2){
            $work_time_json='';
        }
        $dd_dlr_code       = $requestData['dd_dlr_code']??'';//到店专营店编码
        $have_work_time       = $requestData['have_work_time'];//是否包含工时

        $id    = explode(',', $shop_cart_id);
        $count = explode(',', $count);
        if (count($id) != count($count)) {
            return $this->setResponseError('参数缺失')->send();
        }
        //更新
        foreach ($id as $k => $v) {
            if (empty($v)) {
                continue;
            }
            if ($count[$k] <= 0) {
                $save_data = array('count' => $count[$k], 'modifier' => 'u_s_c2'.rand(1000,9999));
//                $save_data = array('count' => $count[$k], 'modifier' => 'u_s_c', 'is_enable' => 0);
            } else {
                $save_data = array('count' => $count[$k], 'modifier' => 'u_s_c1'.rand(1000,9999));//备注了
            }
//            if($work_time_json){
//                $save_data['work_time_json']=$work_time_json;
//            }
            $cart_info =  $this->shop_cart_model->getOneByPk($v);
            if($dd_dlr_code){
                $save_data['dd_dlr_code']=$dd_dlr_code;
                //如果有工时费的需要去请求工时费。。。
                if($cart_info){
                    if($have_work_time==1){
                        $commodity_model  =  new DbCommodity();
                        $commodity =  $commodity_model->getOneByPk($cart_info['commodity_id']);
                        if($commodity) {
                            $ww_price_data = [
                                'dlr_code' => $dd_dlr_code,
                                'car_config_code' => $this->user['car_18n'],
                                'repair_type' => $commodity['work_hour_type'],
                            ];
                            $set_sku_model = new DbCommoditySetSku();
                            $price_ret = $set_sku_model->getE3sPrice($ww_price_data);
                        }
                        if($work_time_json){
//                        $work_time_json =  json_decode($work_time_json,true);
                            $work_time_json['work_time_price'] = $price_ret;
                            $save_data['work_time_json']=json_encode($work_time_json);
                        }else{
                            if($cart_info['work_time_json'] && $cart_info['work_time_json']<>'""'){
                                $work_time_json = json_decode($cart_info['work_time_json'],true);
                                if($work_time_json){
                                    $work_time_json['work_time_price'] = $price_ret;
                                    $save_data['work_time_json']=json_encode($work_time_json);
                                }
                            }
                        }
                        if($cart_info['sku_c_json']){
                            $sku_c_json = json_decode($cart_info['sku_c_json_cn'],true);
                            if($sku_c_json){
                                foreach ($sku_c_json as $kk=>$vv){
                                    $sku_c_json[$kk]['work_time_price'] = $price_ret;
                                }
                                $save_data['sku_c_json_cn']=json_encode($sku_c_json);
                            }
                        }

                    }




                    // 赠品的到店跟随更新
                    if ($cart_info['gift_c_json'] && $cart_info['gift_c_json'] != '[]') {
                        $dlr_model = new DbDlr();
                        $gift_commodities = json_decode($cart_info['gift_c_json'], true);
                        foreach ($gift_commodities as $gift_k => $gift_c) {
                            if (!empty($gift_c['dd_dlr_code']) && $gift_c['dd_dlr_code'] != $dd_dlr_code) {
                                $dlr = $dlr_model->getOne(['where' => ['dlr_code' => $dd_dlr_code]]);
                                $gift_commodities[$gift_k]['dd_dlr_code'] = $dd_dlr_code;
                                $gift_commodities[$gift_k]['dd_dlr_name'] = $dlr['dlr_name'];
                            }
                        }
                        $save_data['gift_c_json'] = json_encode($gift_commodities);
                    }
                }
            }
            //'last_updated_date'=>date('Y-m-d H:i:s')
//            $save_data['last_updated_date']=date('Y-m-d H:i:s');//会影响排序
            if ($sku_ids) {
                $sku_ids             = explode(',', $sku_ids);

                $save_data['sku_id'] = $sku_ids[$k];
                //只在改规格的时候需要传工时信息以及用户18n
                $shop_cart =  $this->shop_cart_model->getOne(['where'=>['user_id' => $this->user_id,'id'=>['<>',$id[$k]], 'sku_id' => $sku_ids[$k], 'is_enable' => 1]]);
                if($shop_cart){
                    return $this->setResponseError('已经有相同规格商品')->send();
                }
                $save_data['work_time_json']=json_encode($work_time_json);
//                dd($save_data);
//                $save_data['car_18n']=$this->user['car_18n'];
            }
            if($mail_method){
                $mail_method=trim($mail_method);
                $save_data['order_mail_type'] = $mail_method[$k];
                if($mail_method[$k]==2){
                    $save_data['work_time_json']='';
                    $save_data['dd_dlr_code']='';
                }
            }
            $where = array('user_id' => $this->user_id, 'id' => $id[$k], 'is_enable' => 1);

            if($have_work_time==2){
                $save_data['work_time_json']='';
            }
            $net_com =  new Common();
            $user_info = $net_com->getFriendBaseInfo($this->user);
            $vin = $user_info['user_status']==2?$user_info['vin']:'';

            $save_data['cart_car_info']      = $user_info['car_type_name'];//车型
            $save_data['car_18n']      = $user_info['car_config_code'];//18N
            $save_data['order_vin']      = $vin;//vin

            if (isset($requestData['use_discount'])) {
                $save_data['use_discount'] = $requestData['use_discount'];
            }
            if (isset($requestData['distance'])) {
                $save_data['distance'] = $requestData['distance'];
            }
            if (isset($requestData['gift_act_id'])) {
                $save_data['gift_act_id'] = $requestData['gift_act_id'];
            }
            if (isset($requestData['gift_card_id'])) {
                $save_data['gift_card_id'] = $requestData['gift_card_id'];
            }
            if (isset($requestData['gift_c_json'])) {
                $save_data['gift_c_json'] = $requestData['gift_c_json'] ?? '';
                if ($save_data['gift_c_json'] && $save_data['gift_c_json'] != '[]') {
                    $save_data['gift_c_json'] = json_encode($save_data['gift_c_json']);
                } else {
                    $save_data['gift_c_json'] = '';
                }
            }

            if (!empty($cart_info)) {
                if($cart_info['car_series_id']!=$this->user['car_series_id'] && $cart_info['gift_card_id']){
                    $save_data['gift_c_json'] = '';
                    $save_data['gift_card_id'] = '';
                }
            }
//            print_json($save_data);
            $log_data = $this->shop_cart_model->getOneByPk($id[$k]);
            $res   = $this->shop_cart_model->where($where)->update($save_data);//savedata会自动更新lastupdateddate
            if (!$res) {
                return $this->setResponseError('更新失败')->send();
            }else{
                $log_data['op_type'] = 2;
                $log_data['origin_id'] = $id[$k];
                Queue::push('app\common\queue\ShopingCartChange', json_encode($log_data),config('queue_type.order_change'));
            }
        }
        return $this->setResponseData('ok')->send();
    }

    /**
     * @title 删除购物车商品
     * @description 接口说明
     * @param name:shop_cart_ids type:int require:1 default:0 other: desc:购物车ID，从列表获取多个使用,隔开
     *
     * @return error: 0 成功 1 错误参考提示msg
     * @return msg:提示信息
     * @return data:返回数据
     *
     * <AUTHOR>
     * @url /net-small/cart/cart
     * @method DELETE
     *
     */
    public function delCart(CartValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("delCart")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $shop_cart_id = $requestData['shop_cart_ids'];
        $id           = explode(',', $shop_cart_id);
        foreach ($id as $k => $v) {
            if (empty($v)) {
                continue;
            }
            $remove_data = $this->shop_cart_model->getOneByPk($v);
            if(!$remove_data){
                return $this->setResponseError('删除失败~')->send();
            }
            if($remove_data['user_id']!=$this->user_id){
                return $this->setResponseError('删除失败!~')->send();
            }
            $res = $this->shop_cart_model->removeShoppingCart($v, $this->channel_type);
            if (!$res) {
                return $this->setResponseError('删除失败')->send();
            }else{
                $remove_data['op_type'] = 3;
                $remove_data['origin_id'] = $v;
                Queue::push('app\common\queue\ShopingCartChange', json_encode($remove_data),config('queue_type.order_change'));
            }
        }
        return $this->setResponseData('ok')->send();
    }

    /**
     * @title 购物车列表
     * @description 接口说明
     * @param name:unionid type:int require:1 default:0 other: desc:yonghuid
     *
     * @return 200:  成功
     * @return msg:提示信息
     * @return data:返回数据
     *
     * <AUTHOR>
     * @url /net-small/cart/list
     * @method GET
     *
     */
    public function CartList(CartValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
//        $result      = $validate->scene("cartList")->check($requestData);
//        //校验失败,返回异常
//        if (empty($result)) {
//            return $this->setResponseError($validate->getError())->send();
//        }
        $set_sku_model = new DbCommoditySetSku();
        $sp_model      = new DbSpecValue();
        $cart_model    = new BuShoppingCart();
        $order_model = new BuOrder();
        $dlr_model =  new DbDlr();
//        $channel_arr =  DbDlr::channel_to_arr($this->channel_type)['dlr_code'];//通过渠道去换品牌所有渠道
        $where         = ["cart.is_enable" => 1, 'cart.user_id' => $this->user_id,'cart.brand'=>$this->brand];//购物车打通不分渠道
        if(isset($requestData['shop_cart_ids'])){
            $where['cart.id']=$requestData['shop_cart_ids'];
        }
        if ($this->channel_type == 'GWSM'){
            $where[] = ['exp', 'cart.is_empower = 0 || (cart.is_empower = 1 && e.dd_commodity_type = 0 && e.commodity_class != 6)'];
        }
//        $where         = ["cart.is_enable" => 1, 'cart.user_id' => $this->user_id,'cart.dlr_code'=>$this->channel_type];
//        $where[]       = ['exp', "FIND_IN_SET('{$this->channel_type}',d.up_down_channel_dlr)"];//加入渠道代码
        $field         = 'd.group_commodity_ids_info,a.stock, b.sp_value_list, b.image,a.is_enable  AS is_sku_out, b.sku_code, IF (( c.is_enable = 0 OR d.is_enable = 0 or c.is_enable is null  OR d.is_enable is null ), "0",1) AS is_sold_out, c.commodity_name, c.cover_image, c.commodity_class, c.commodity_card_ids, c.card_id, c.id AS commodity_id, d.is_mail, d.is_store, favourable_introduction, favourable_detail, d.pay_style, a.price b_act_price, a.price, limit_dis, limit_wi_dis, n_dis, group_dis, pre_dis, cheap_dis, full_dis, full_wi_dis, seckill_dis, gift_dis, d.commodity_dlr_type_id,d.is_card_multic,d.mail_price,a.divided_into,a.install_fee,a.commodity_set_id,d.max_point,d.factory_points,d.dlr_points,cart.act_json,cart.wi_act_json,cart.id cart_id,cart.count,cart.commodity_id,cart.sku_id,cart.order_mail_type,cart.cart_car_info,cart.order_mail_type mail_method,cart.dd_dlr_code,cart.sku_c_json,cart.sku_c_json_cn,cart.work_time_json,cart.gift_act_id,cart.gift_c_json,cart.use_discount,c.is_grouped,cart.car_18n,a.relate_car_ids,c.dd_commodity_type,d.count_stock,b.maintain_q maintain_dis,cart.distance,d.mail_type,e.card_id,cart.car_series_id,cart.order_vin,a.commodity_sku_id,cart.gift_card_id,d.dlr_groups';
//        $list          = $set_sku_model->getSkuFlatCart(['where' => $where, 'field' => $field, 'group' => "cart.id", 'order' => 'cart.id desc']);
//        $where['cart.id'] = 557868;
        $list          = $cart_model->getCartList(['where' => $where, 'field' => $field, 'channel_type' => $this->channel_type, 'group' => "cart.id", 'order' => 'cart.last_updated_date desc']);
//          echo $cart_model->getLastSql();echo "========";die();
        $sCom               = new Common();
        $full_model         = new DbFullDiscount();
        $n_dis_model        = new DbNDiscount();
        $n_dis_info_model   = new DbNDiscountInfo();
        $limit_info_model   = new DbLimitDiscount();
        $com_dlr_type_model = new DbCommodityDlrType();
        $dbCommoditySetSku = new DbCommoditySetSku();
        $card_r_model =  new BuCardReceiveRecord();
        $goods_sku_model =  new DbCommoditySku();
        $net_goods = new NetGoods();
        $totime = date('Y-m-d H:i:s');
        //记录满足的

        $no_car_r= 0;
        $dd_dlr_pay = [];
        $full_act_can = [];
        $shelves = $dlr_model->channel_to_shelves($this->channel_type);
        $goods_id = [];
        $goods_arr = [];
        $goods_dlr_group = [];
        $group_list = [];
        $dlr_g_arr = [];
        $dlr_g_info = [];
        foreach ($list as $l_v){
            $goods_id[]=$l_v['commodity_id'];
            if($l_v['dd_dlr_code']){
                if(!$l_v['dlr_groups']){
                    $l_v['dlr_groups_arr'] = $this->user['group_id'];
                }else{
                    $l_v['dlr_groups_arr'] = explode(',',$l_v['dlr_groups']);

                }

                $goods_dlr_group[$l_v['cart_id']] =$l_v['dlr_groups_arr'];
                if(!$group_list){
                    $group_list= $l_v['dlr_groups_arr'];

                }else{
                    $group_list= array_merge($group_list,$l_v['dlr_groups_arr']);
                }

            }
        }
        if($group_list){
            $dlr_group_model =  new DbDlrGroup();
            $dlr_group_list_info =  $dlr_group_model->getList(['where'=>['id'=>['in',$group_list]]]);
            foreach ($dlr_group_list_info as $v){
                $dlr_codes = explode(',',$v['dlr_code_value']);
                $nev_where =  $this->user['nev_where'];
                $dlr_where = $nev_where;
                $dlr_where['dlr_code'] = ['in',$dlr_codes];
                $dlr = $dlr_model->getColumn(['where' => $dlr_where,'column'=>'dlr_code']);
                if($dlr){
                    $dlr_g_info[$v['id']] = $dlr;
                }
            }

            foreach ($goods_dlr_group as $gk=> $g_d_v){
                $dlr_g_arr[$gk]=[];
                foreach ($g_d_v as $gddv){
                    if(isset($dlr_g_info[$gddv])){
                        if(!isset($dlr_g_arr[$gk])){
                            $dlr_g_arr[$gk] = $dlr_g_info[$gddv];//只有一个分组情况下这样子对的
                        }else{
                            $dlr_g_arr[$gk] = array_merge($dlr_g_arr[$gk],$dlr_g_info[$gddv]);//只有一个分组情况下这样子对的
                        }
                    }
                }

            }
        }
//        print_json($dlr_g_arr,$dlr_g_info,$goods_dlr_group);

        $all_card_ids= [];
        $card_rules = [];
        if($goods_id){
            $g_list = $net_goods->goodsList(['pageSize' => 1000, 'commodity_ids' => implode(',', $goods_id),'use_gift_card'=>1], $this->user, $this->channel_type,[],'cart_list');

            if(isset($g_list['msg']['data'])){
                foreach ($g_list['msg']['data'] as $g_v){
                    $goods_arr[$g_v['commodity_id']] = $g_v['card_list']??[];
                    if(isset($g_v['card_list'])){
                        $all_card_ids =  array_merge($all_card_ids,$g_v['card_list']);
                    }
                }

                $card_rules = $g_list['msg']['goods_card_rule']??[];
                $gift_card_sku_arr = $g_list['msg']['user_info']['gift_card_sku_arr']??[];
                $gift_card_sku_class_arr = $g_list['msg']['user_info']['gift_card_sku_class_arr']??[];
            }

        }

        $user_card_list =  $card_r_model->getList([
            'where' =>['user_id' =>$this->user_id,'status' =>1,'is_enable'=>1,'validity_date_start'=>['<=',$totime],'validity_date_end'=>['>=',$totime],'card_id'=>['in',$all_card_ids]]
        ]);
        $user_get_card_ids = array_column($user_card_list,'card_id');
//        $goods_use_card_sku_arr =[];
//        if($user_get_card_ids){
////            $commodity_set_id_list = array_column($list,"commodity_set_id");
////            $mz_card_id_arr = $net_goods->_goods_card_arr($user_get_card_ids,$commodity_set_id_list);
////            $goods_use_card_sku_arr = $mz_card_id_arr['goods_use_card_sku_arr'];
//        }

        if ($list) {
            foreach ($list as $k1 => $v1) {
                $order_vin_list[]=$v1['order_vin'];
            }
            $user_gift_card_list = $net_goods->getUserGiftCardList($this->user,$order_vin_list,$this->brand);
            $card_r_gift_vin_user = $user_gift_card_list['card_r_gift_vin_user']??[];


//            活动类型ID1限时折扣2团购3满减4全积分折扣5套装6N件N折7预售8立减
            //子商品总库存数组
            foreach ($list as $k => $v) {
                $group_sub_count_stock = [];
                $group_must_sub_count_stock = [];
                $group_can_select_tmp = [];
                $group_down_arr_tmp = [];
                $list[$k]['is_sku_out']        = min($list[$k]['is_sku_out'],$list[$k]['is_sold_out']);
//                $list[$k]['is_sku_out']        = min($list[$k]['is_sku_out'],$list[$k]['is_sold_out']);
                $list[$k]['is_sold_out']        = 1;
                $ac_json                        = [];
                $goods_dlr_type                 = $com_dlr_type_model->getOneByPk($v['commodity_dlr_type_id']);
                $list[$k]['commodity_dlr_type'] = $goods_dlr_type['outside_name'] ?? '';
                $list[$k]['work_price_all_b_act'] =$list[$k]['work_price_all']=0;
                $list[$k]['have_work_price']        =0;
                if($v['car_18n']<>$this->user['car_18n']){
                    $no_car_r=1;
                }
                if($v['maintain_dis']==10){
                    $list[$k]['maintain_dis']='';
                }
                $list[$k]['is_gift_card'] = 0;
                //买赠赠品券判断


                $goods_sku_id = [$v['commodity_sku_id']];
                $goods_set_sku_id =[];
                $goods_set_sku_id[] = ['sku_id'=>$v['sku_id']];
                //子商品总库存0423版本
                if(!empty($v['group_commodity_ids_info'])){
                    if(!empty($v['sku_c_json_cn'])){
                        $goods_sku_id = [];
                        $goods_set_sku_id =[];

                        $commodity_set_list = json_decode($v['group_commodity_ids_info'], true);
                        $sku_c_json_cn = json_decode($v['sku_c_json_cn'], true);

                        foreach($sku_c_json_cn as $skucjsoncnitem){
                            $subgoodsinfo = $dbCommoditySetSku->getSubGoodId($skucjsoncnitem['sku']);
                            foreach($commodity_set_list as $commodity_set_tmp_item){
                                if($subgoodsinfo['group_sub_commodity_id'] == $commodity_set_tmp_item['commodity_id']){
                                    $goods_set_sku_id[]=['sku_id'=>$subgoodsinfo['group_sub_set_sku_id']];

                                    $goods_sku_id[]=$subgoodsinfo['commodity_sku_id'];
                                    if($commodity_set_tmp_item['can_select'] == 1) {
                                        $group_sub_count_stock[$commodity_set_tmp_item['commodity_id']] = $subgoodsinfo['stock'];
                                    }else{
                                        $group_must_sub_count_stock[$commodity_set_tmp_item['commodity_id']] =   $subgoodsinfo['stock'];
                                    }
                                    $group_can_select_tmp[$commodity_set_tmp_item['commodity_id']] = $commodity_set_tmp_item['can_select'];
                                }
                            }
                        }
                    }
                }
                $gift_card_sku_arr = [];
                $gift_card_sku_class_arr = [];
//                print_json($card_r_gift_vin_user);
                if($card_r_gift_vin_user){
                    $wjh_card_act_id = $card_r_gift_vin_user[$this->user_id]??[];
                    if($v['order_vin']){
                        $wjh_card_act_id = array_merge($wjh_card_act_id,$card_r_gift_vin_user[$v['order_vin']]??[]);
                    }

                    if($wjh_card_act_id){
                        $wjh_card_act_id = array_column($wjh_card_act_id,'activity_id');
                        $gift_card_rule_list= $net_goods->gift_card_rule($wjh_card_act_id);
                        $gift_card_sku_arr          = $gift_card_rule_list['gift_card_sku_arr'];
                        $gift_card_sku_class_arr    = $gift_card_rule_list['gift_card_sku_class_arr'];
                    }

                }
//                print_json($gift_card_sku_arr,$gift_card_sku_class_arr,$card_r_gift_vin_user);
                //为了匹配组合商品跟普通商品
                $cl_gift_json=0;
                if($v['order_mail_type']==2){
                    $cl_gift_json=1;
                }
                if(($gift_card_sku_arr || $gift_card_sku_class_arr) && $v['order_mail_type']==1){
                    $sku_info_list =  $goods_sku_model->getList(['where'=>['id'=>['in',$goods_sku_id]]]);
//                    print_json($goods_sku_model->getLastSql());
                    $sku_code_arr = [];
                    $variety_code_arr = [];
                    foreach ($sku_info_list as $sku_v){
                        if($sku_v['sku_code']){
                            foreach (explode(',',$sku_v['sku_code']) as $sku_c_v){
                                if($sku_c_v){
                                    $sku_code_arr[]=$sku_c_v;
                                }
                            }
                            $variety_code_arr[]=$sku_v['variety_code'];
                        }
                        unset($sku_c_v);
                        if($sku_v['variety_code']){
                            foreach (explode(',',$sku_v['variety_code']) as $sku_c_v){
                                if($sku_c_v){
                                    $variety_code_arr[]=$sku_c_v;
                                }
                            }
                        }
                    }
                    if($sku_code_arr || $variety_code_arr){
                        if(array_intersect($gift_card_sku_arr,$sku_code_arr) || array_intersect($gift_card_sku_class_arr,$variety_code_arr)){
                            //判断是否当前sku_code匹配
                            $gift_card_num_arr = $net_goods->checkGiftCard(json_encode($goods_set_sku_id),$this->user,[$v['cart_id']]);
                            $gift_card_num = $gift_card_num_arr['number'];
                            // is_gift_card , gift_card_desc ,gift_card_change_info

                            //todo
                            if($gift_card_num>0){
                                $list[$k]['gift_card_desc'] = $gift_card_num_arr['card_desc'];
                                if(!$v['gift_card_id']){
                                    $list[$k]['gift_card_id'] = $gift_card_num_arr['card_id'];
                                    $v['gift_c_json'] = $list[$k]['gift_c_json'] = '';
                                    $cart_model->saveData(['gift_c_json'=>'','modifier'=>'epcid'],['id'=>$v['cart_id']]);

                                }elseif($gift_card_num_arr['card_id']!=$v['gift_card_id']){
                                    $list[$k]['gift_card_id'] = $gift_card_num_arr['card_id'];
                                    $cart_model->saveData(['gift_card_id'=>$gift_card_num_arr['card_id'],'modifier'=>'epcid1'],['id'=>$v['cart_id']]);
                                }
                                $list[$k]['is_gift_card'] = 1;

                                $list[$k]['gift_card_number'] = $gift_card_num;
                                $gift_c_num = 0;
                                if($v['gift_c_json']){
                                    $gift_c_arr =  json_decode($v['gift_c_json'],true);
                                    $gift_c_num = array_sum(array_column($gift_c_arr,'count'));
                                }

                                if($v['count']<$gift_c_num){
                                    $list[$k]['gift_card_change_info'] = "所选赠品状态发生变更，请重新选择";
                                    $list[$k]['gift_c_json'] = "";
                                    $cl_gift_json=1;
                                }else{
                                    $list[$k]['gift_card_change_info'] = "未选择赠品";
                                }
                            }else{
                                $cl_gift_json=1;
                            }

                        }else{
                            $cl_gift_json=1;
                        }
                    }
                }else{
                    if($v['gift_card_id']){
                        $cl_gift_json=1;
                    }
                }
                if($cl_gift_json==1 && $v['gift_card_id']){
                    $cart_model->saveData(['gift_card_id'=>'','gift_c_json'=>'','modifier'=>'cart_n_g'],['id'=>$v['cart_id']]);

                }


//                if(!empty($group_must_sub_count_stock)){
//                    foreach($group_must_sub_count_stock as $group_must_sub_count_item){
//                        if($group_must_sub_count_item == 0){
//                            $goods_data['count_stock'] = 0;
//                            break;
//                        }
//                    }
//                }

                /////////////////////////////
//                if(!empty($v['group_commodity_ids_info'])){
//                    $commodity_set_list = json_decode($v['group_commodity_ids_info'], true);
//                    foreach ($commodity_set_list as $kk=>$cc_v) {
//                        if($cc_v['can_select'] == 1){
//                            $sum_stock = $dbCommoditySetSku->getSumStock($cc_v);
//                            if($sum_stock > 0){
//                                $group_sub_count_stock[$cc_v['commodity_id']] = $sum_stock;
//                            }
//                        }else{
//                            $count_sum = $dbCommoditySetSku->getSumStock($cc_v);
//                            $group_must_sub_count_stock[$cc_v['commodity_id']] =  $count_sum;
//                            if($count_sum == 0){
//                                $goods_data['count_stock'] = 0;
//                                break;
//                            }
//                        }
//                        $group_can_select_tmp[$cc_v['commodity_id']] = $cc_v['can_select'];
//                    }
//                }




                //子商品信息
                if($v['sku_c_json_cn'] && $v['sku_c_json_cn']<>'[]'){
                    $sub_sku = json_decode($v['sku_c_json_cn'],true);
                    $sub_sku_id_arr =  array_keys($sub_sku);
                    $sub_list = $set_sku_model->getList(['where'=>['id'=>['in',$sub_sku_id_arr]],'field'=>'price,stock,id,is_enable,group_sub_set_sku_id,group_sub_commodity_id']);
                    $sub_real_stock = $set_sku_model->getList(['where'=>['id'=>['in',array_column($sub_list,'group_sub_set_sku_id')]],'field'=>'stock,id,commodity_id,relate_car_ids']);
                    $sub_real_arr = [];

                    foreach($sub_real_stock as $v_sku){
                        $sub_real_arr[$v_sku['id']]=$v_sku['stock'];
                        //组合商品有一个关联了车型，则主商品关联车型 tzl20230428
                        if($v_sku['relate_car_ids']){
                            $list[$k]['relate_car_ids'] = $v_sku['relate_car_ids'];
                        }
                    }
//                    $have_bx = 0;
//                    foreach ($sub_sku as $sub_k=>$sub_v){
//                        if($sub_v['can_select']){
//                            $have_bx=1;
//                        }
//                    }
//                  echo $set_sku_model->getLastSql();die();
                    $one_g_price= 0;
                    $list[$k]['is_sku_out']=1;
                    $one_sub_work_price=0;
                    $sub_p_wor =0;
                    $sub_one_stock=0;

                    $all_can_select = 0;//当为1时是全部子商品为可选商品并下架
                    $sub_tmp_count = count($sub_list);
                    $sku_jj = [];
                    $sub_goods_id = [];
                    foreach ($sub_list as $kv=>$vv){
                        //0423版本
                        $group_count_stock_tmp = $group_sub_count_stock[$vv['group_sub_commodity_id']] ?? 0;
                        $group_count_stock_tmp = $group_count_stock_tmp == 0 ? $group_must_sub_count_stock[$vv['group_sub_commodity_id']] ?? 0 : $group_count_stock_tmp;
                        $sub_sku[$vv['id']]['count_stock'] = $group_count_stock_tmp;
                        $sub_sku[$vv['id']]['can_select'] = $group_can_select_tmp[$vv['group_sub_commodity_id']] ?? 0;
                        $canot_buy_tmp = $dbCommoditySetSku->commodityStatus($shelves,$vv['group_sub_commodity_id'],$vv['group_sub_set_sku_id']);
                        //echo $vv['group_sub_commodity_id']."||||".$vv['group_sub_set_sku_id']."--->".$canot_buy_tmp."\n";
                        if($sub_sku[$vv['id']]['can_select'] == 0 && $canot_buy_tmp == 1){
                            $list[$k]['is_sold_out'] = 0;
                        }
                        //全为可选商品并为下架

                        if(empty($group_must_sub_count_stock)){
                            $all_can_select = $all_can_select + $canot_buy_tmp;
                        }

                        if($sub_sku[$vv['id']]['count_stock'] == 0 || $canot_buy_tmp == 1){
                            $one_sub_price = 0;
                        }else{
                            $one_sub_price=$vv['price']* $sub_sku[$vv['id']]['count'];
                        }
                        //库存==最低库存，总库存==最低总库存

                        if($sub_one_stock==0){
                            $v['stock']=$sub_real_arr[$vv['group_sub_set_sku_id']];
                        }else{
                            if($sub_sku[$vv['id']]['can_select'] == 0 || ($sub_sku[$vv['id']]['can_select'] == 1 && $sub_real_arr[$vv['group_sub_set_sku_id']]>0)){
                                $v['stock']=min($sub_real_arr[$vv['group_sub_set_sku_id']],$sub_one_stock);
                            }
                        }
                        $vv['stock']=$sub_one_stock=$sub_real_arr[$vv['group_sub_set_sku_id']];

                        $one_g_price+=$one_sub_price;

                        if(isset($sub_sku[$vv['id']]['work_time_price']) && isset($sub_sku[$vv['id']]['work_time_number'])){
                            if($sub_sku[$vv['id']]['work_time_number']){
                                $one_sub_work_price += $sub_sku[$vv['id']]['work_time_price']*$sub_sku[$vv['id']]['work_time_number']* $sub_sku[$vv['id']]['count'];//*$v['count']
                                $list[$k]['have_work_price']        =1;
                            }
                        }
                        $sub_sku[$vv['id']]['all_goods_price'] =$one_sub_price;
                        $sub_sku[$vv['id']]['stock'] =$vv['stock'];
                        //$sub_sku[$vv['id']]['group_sub_commodity_id'] = $vv['group_sub_commodity_id'];
                        $sub_sku[$vv['id']]['canot_buy'] = $dbCommoditySetSku->commodityStatus($shelves,$vv['group_sub_commodity_id'],$vv['group_sub_set_sku_id']);

                        if($vv['stock'] == 0){
                            $one_g_price-=$one_sub_price;
                        }
//                        if($vv['is_enable']==0 || $vv['stock']<1){
//                               $list[$k]['is_sku_out']=0;
//                        }
                        $sku_jj[]=$vv['id'];
                        $sub_goods_id[]= $vv['group_sub_commodity_id'];
//                        $sub_key = $v['commodity_id'].'_'.$vv['group_sub_commodity_id'];

                    }
                    if (!empty($group_must_sub_count_stock)) {
                        $v['count_stock'] = min($group_must_sub_count_stock);
                    } else if (!empty($group_sub_count_stock)) {
                        $min_sub_stock = 0;
                        foreach ($group_sub_count_stock as $g_s_v){
                            if($g_s_v>0){
                                if($min_sub_stock==0){
                                    $min_sub_stock = $g_s_v;
                                }elseif ($min_sub_stock>$g_s_v){
                                    $min_sub_stock = $g_s_v;
                                }
                            }
                        }
                        $v['count_stock'] = $min_sub_stock;
                    }else{
                        $v['count_stock'] = 0;

                    }
//                    $v['count_stock']=min($group_must_sub_count_stock);


                    //全部可选并下架
                    //echo $all_can_select."==".$sub_tmp_count;echo "\n";
                    if($all_can_select == $sub_tmp_count){
                        $list[$k]['is_sold_out'] = 0;
                    }


                    $list[$k]['sub_sku'] = $sub_sku;
                    $list[$k]['work_price_all_b_act'] =$list[$k]['work_price_all']=$one_sub_work_price;//*$v['count']
                    $list[$k]['b_act_price'] = $list[$k]['price'] = round($one_g_price,2);

                }

                $card_id_arr =  explode(',',$v['card_id']);
                $can_use_card = 0;
                //1可用券，2其他规格可用，0无券
                $one_goods_catd_inter = $card_rules[$v['commodity_set_id']]??[];
//                print_json($one_goods_catd_inter);
                $one_card_ids = [];
//                print_json($one_goods_catd_inter);
                foreach ($one_goods_catd_inter as $o_v){
                    if ($o_v['can_use_card'] == 1) {
                        if ($o_v['car_series_id_str']) {
                            if (in_array($v['car_series_id'], explode(',', $o_v['car_series_id_str']))) {
                                $one_card_ids[] = $o_v['id'];
                            }
                        } else {
                            $one_card_ids[] = $o_v['id'];
                        }
                    }

                }
//                if($v['cart_id']==558415){
//                    print_json($goods_arr);
//                }
                if($one_card_ids){
                    $one_goods_catd_inter =  array_intersect($one_card_ids,$user_get_card_ids);
                    if($v['sku_c_json_cn'] && $v['sku_c_json_cn']<>'[]'){
                        $detail_card = $net_goods->_detail_card($v['commodity_set_id'],$sku_jj,$sub_goods_id,1,0,$one_goods_catd_inter,[],[],$this->channel_type,[],$v['car_series_id']);
                    }else{
                        $detail_card = $net_goods->_detail_card($v['commodity_set_id'],[$v['sku_id']],[],2,0,$one_goods_catd_inter,[],[],$this->channel_type,[],$v['car_series_id']);
                    }
                    //上面计算，通过当前的子商品skuid,满足用券ID，$one_goods_catd_inter这个已经判断已领券跟当前商品用券的关联，也就是整个商品可用券
//                    print_json($detail_card);

                    $mz_card = $detail_card['mz_card'];
                    if($mz_card){
                        $can_use_card = 1;
                    }else{
                        $can_use_card = 2;
                    }
                }

                $list[$k]['can_use_card'] = $can_use_card;
                $b_act_price = $list[$k]['b_act_price'];
                $list[$k]['commodity_dis_user_segment'] = 0;
                $list[$k]['commodity_dis_act_user_segment'] = 0;

                $list[$k]['commodity_dis_label'] = '';
                $list[$k]['commodity_dis_label_cn'] = '';
                $list[$k]['commodity_dis_segment_price'] = $b_act_price;
                $list[$k]['commodity_dis_original_price'] = $b_act_price;

                $list[$k]['commodity_dis_act_segment_price'] = $b_act_price;
                $list[$k]['commodity_dis_act_original_price'] = price_fmt($b_act_price);

                $list[$k]['commodity_dis_act_label'] = '';
                $list[$k]['commodity_dis_act_label_cn'] = '';

                $commodity_dis_info = $net_goods->getCommoditySegmentDiscount($v['commodity_id']);
                if ($commodity_dis_info) {
                    $list[$k]['commodity_dis_user_segment'] = $commodity_dis_info['user_segment'];
                    $list[$k]['commodity_dis_label'] = get_user_segment_label($commodity_dis_info['user_segment']);
                    $list[$k]['commodity_dis_label_cn'] = get_user_segment_label_cn($list[$k]['commodity_dis_label']);
                    $list[$k]['commodity_dis_segment_price'] = $list[$k]['commodity_dis_act_segment_price'] = $net_goods->getCommodityDisFinalPrice($commodity_dis_info, $b_act_price);

                    if ($v['use_discount']) {
                        $list[$k]['price'] = $list[$k]['commodity_dis_segment_price'];
                    }
                }

                if ($v['gift_c_json'] && $v['gift_c_json'] <> '[]') {
                    $gift_sku_id_arr = [];
                    $gift_commodities = json_decode($v['gift_c_json'], true);
                    foreach ($gift_commodities as $gift_k => $gift_c) {
                        $gift_sku_id_arr[] = $gift_c['set_sku_id'];

                        $gift_sub_sku_id_arr = [];
                        // 赠品为组合商品，获取子商品的库存
                        if(!empty($gift_c['com_json'])) {
                            foreach ($gift_c['com_json'] as $gift_com_v) {
                                $gift_sub_sku_id_arr[] = $gift_com_v['sku'];
                            }
                            $gift_com_sub_list = $set_sku_model->getList(['where' => ['id' => ['in', $gift_sub_sku_id_arr]], 'field' => 'price,stock,id,is_enable,group_sub_set_sku_id']);
                            $gift_com_sub_real_stock = $set_sku_model->getList(['where' => ['id' => ['in', array_column($gift_com_sub_list, 'group_sub_set_sku_id')]], 'field' => 'stock,id']);

                            $gift_com_sub_stock_arr = [];
                            $gift_com_group_sub_stock_arr = [];
                            foreach ($gift_com_sub_real_stock as $v_sku) {
                                $gift_com_group_sub_stock_arr[$v_sku['id']] = $v_sku['stock'];
                            }
                            foreach ($gift_com_sub_list as $v_sku) {
                                $v_sku['stock'] = $gift_com_group_sub_stock_arr[$v_sku['group_sub_set_sku_id']] ?? 0;
                                $gift_com_sub_stock_arr[$v_sku['id']] = $v_sku;
                            }

                            $gift_commodities[$gift_k]['stock'] = 999999;
                            $gift_commodities[$gift_k]['is_enable'] = 1;

                            foreach ($gift_c['com_json'] as $gift_com_k => $gift_com_v) {
                                $gift_commodities[$gift_k]['com_json'][$gift_com_k]['stock'] = $gift_com_sub_stock_arr[$gift_com_v['sku']]['stock'] ?? 0;
                                $gift_commodities[$gift_k]['com_json'][$gift_com_k]['is_enable'] = $gift_com_sub_stock_arr[$gift_com_v['sku']]['is_enable'] ?? 0;

                                // 组合商品库存，以子商品最小库存判断
                                if ($gift_commodities[$gift_k]['com_json'][$gift_com_k]['is_enable'] == 0) {
                                    $gift_commodities[$gift_k]['is_enable'] = 0;
                                }
                                $gift_commodities[$gift_k]['stock'] = min($gift_commodities[$gift_k]['com_json'][$gift_com_k]['stock'], $gift_commodities[$gift_k]['stock']);
                            }
                        }
                    }
                    if ($gift_sku_id_arr) {
                        $gift_sub_stock_arr = [];
                        $gift_sub_list = $set_sku_model->getList(['where' => ['id' => ['in', $gift_sku_id_arr]], 'field' => 'price,stock,id,is_enable,group_sub_set_sku_id']);
                        foreach ($gift_sub_list as $v_sku) {
                            $gift_sub_stock_arr[$v_sku['id']] = $v_sku;
                        }
                        foreach ($gift_commodities as $gift_k => $gift_c) {
                            if(empty($gift_c['com_json'])) {
                                $gift_commodities[$gift_k]['stock'] = $gift_sub_stock_arr[$gift_c['set_sku_id']]['stock'] ?? 0;
                                $gift_commodities[$gift_k]['is_enable'] = $gift_sub_stock_arr[$gift_c['set_sku_id']]['is_enable'] ?? 0;
                            }
                        }
                    }
                    $list[$k]['gift_c_json_cn'] = $gift_commodities;
                } else {
                    $list[$k]['gift_c_json_cn'] = [];
                }

                $gift_act_yh = 0;
                if ($v['gift_dis'] && !$v['gift_card_id']) {
                    $gift_dis_arr = json_decode($v['gift_dis'], true);
                    $gift_act_dis_id = $gift_dis_arr[$this->channel_type][0] ?? '';
                    if ($gift_act_dis_id) {
                        $gift_model = new DbGiftCommodity();
                        $gift_row = $gift_model->getGiftComInfo($v['commodity_id'], $gift_act_dis_id);
                        if ($gift_row) {
                            $gift_act_sku = json_decode($gift_row['sku_price'], true);
                            if (isset($gift_act_sku[$v['sku_id']])) {
                                $gift_act_yh = 1;
                                $list[$k]['gift_act'] = $sCom->_gift($v['commodity_id'], $gift_act_dis_id, $this->user_id, $this->channel_type);
                                $list[$k]['gift_act_id'] = $gift_act_dis_id;
                                $cart_model->where(['id' => $v['cart_id']])->update(['gift_act_id' => $gift_act_dis_id, 'modifier' => 'n_c_gift']);
                            }
                        }
                    }
                }
                if ($gift_act_yh == 0) {
                    $list[$k]['gift_act_id'] = 0;
                    $cart_model->where(['id' => $v['cart_id']])->update(['gift_act_id' => 0, 'modifier' => 'n_c_gift']);
                }

                //工时信息
                if($v['work_time_json']){
                    $work_time = json_decode($v['work_time_json'],true);
//                    $work_time = $work_time[0];
                    if($work_time){
                        $list[$k]['have_work_price']        =1;
                        $list[$k]['work_time_price']=$work_time['work_time_price'];//单价，
                        $list[$k]['work_time_number']=$work_time['work_time_number'];//数量，
                        $list[$k]['work_price_all_b_act'] =$list[$k]['work_price_all']=$work_time['work_time_price']*$work_time['work_time_number'];//*$v['count']
                    }
                }
                //到店专营店信息
                if($v['dd_dlr_code']){
                    if(in_array($v['dd_dlr_code'],$dlr_g_arr[$v['cart_id']])){
                        $dlr =  $dlr_model->getOne(['where'=>['dlr_code'=>$v['dd_dlr_code']]]);
                        $list[$k]['dd_dlr_name']=$dlr['dlr_name'];
                        $list[$k]['dlr_pay_bank_type']=$dlr['ly_bank_code'];//01为招行，02为工行
                        $dd_dlr_pay[] = $dlr['ly_bank_code'];
                    }else{
                        $list[$k]['dd_dlr_name']='请选择专营店';
                        $list[$k]['dd_dlr_code']='';
                        $list[$k]['dlr_pay_bank_type']="9999";//01为招行，02为工行

                    }
                }else{
                    $list[$k]['dlr_pay_bank_type']="01,02";//01为招行，02为工行
                }

                if ($v['limit_dis']) {
                    $dis_limit_info = json_decode($v['limit_dis'], true);
                    if (isset($dis_limit_info[$this->channel_type][0])) {

                        $dis_limit  = $sCom->_limit($v['commodity_id'], $dis_limit_info[$this->channel_type][0], $this->user_id, $this->channel_type, 0, 0, 1, '', $v['use_discount']);
                        $dis_limit_info = $dis_limit['limit_info'];
                        if ($dis_limit_info) {
                            $limit_sku_price = json_decode($dis_limit_info['sku_price'], true);
                            if ($dis_limit_info['act_status'] == 2 && $dis_limit_info['is_enable'] == 1 && isset($limit_sku_price[$v['sku_id']])) {
                                //dis_type 1限时折扣 2立减 未做
                                $commodity_dis_label = get_user_segment_label($dis_limit['user_segment']);
                                $ac_json[] = [
                                    'act_code' => 1,
                                    'act_id' => $dis_limit_info['id'],
                                    'act_name' => $dis_limit_info['title'],
                                    'commodity_dis_act_user_segment' => $dis_limit['user_segment'],
                                    'commodity_dis_label' => $commodity_dis_label,
                                    'commodity_dis_label_cn' => get_user_segment_label_cn($commodity_dis_label),
                                ];
                            }
                        }
                    }
                }
                // 秒杀
                if ($v['seckill_dis']) {
                    $dis_seckill_info = json_decode($v['seckill_dis'], true);
                    if (isset($dis_seckill_info[$this->channel_type][0])) {

                        $dis_seckill = $sCom->_seckill($v['commodity_id'], $dis_seckill_info[$this->channel_type][0], $this->user_id, $this->channel_type, 0, 0, 1, '', $v['use_discount']);
                        $dis_seckill_info = $dis_seckill['seckill_info'];
                        //秒杀前购买
                        if (isset($dis_seckill_info['act_status']) && $dis_seckill_info['act_status'] == 2) {
                            $seckill_sku_price = json_decode($dis_seckill_info['sku_price'], true);
                            if ($dis_seckill_info['is_enable'] == 1 && isset($seckill_sku_price[$v['sku_id']])) {
                                $commodity_dis_label = get_user_segment_label($dis_seckill['user_segment']);
                                $ac_json[] = [
                                    'act_code' => 10,
                                    'act_id' => $dis_seckill_info['id'],
                                    'act_name' => $dis_seckill_info['title'],
                                    'act_status' => $dis_seckill_info['act_status'],
                                    'commodity_dis_act_user_segment' => $dis_seckill['user_segment'],
                                    'commodity_dis_label' => $commodity_dis_label,
                                    'commodity_dis_label_cn' => get_user_segment_label_cn($commodity_dis_label),
                                ];
                            }
                        }
                    }
                }

                if ($v['full_dis']) {
                    $full_dis_limit = json_decode($v['full_dis'], true);
                    if (isset($full_dis_limit[$this->channel_type][0])) {
                        $segment_info = get_user_segment_info();
                        $membership = $segment_info['membership_level'];
                        $owner = $segment_info['brand_owner_label'];
                        $where = sprintf("user_segment=0 or (user_segment=1 and FIND_IN_SET('%s',user_segment_options)) or (user_segment=2 and FIND_IN_SET('%s',user_segment_options))", $membership, $owner);
                        $full_info = $full_model->where(['id' => $full_dis_limit[$this->channel_type][0]])->where($where)->find();
                        if ($full_info) {
                            $commodity_dis_label = get_user_segment_label($full_info['user_segment']);
                            $act = [
                                'act_code' => 3,
                                'act_id'   => $full_info['id'],
                                'act_name' => $full_info['activity_title'],
                                'commodity_dis_act_user_segment' => $full_info['user_segment'],
                                'commodity_dis_label' => $commodity_dis_label,
                                'commodity_dis_label_cn' => get_user_segment_label_cn($commodity_dis_label),
                            ];
                            if ($full_info['purchase_number'] > 0) {
                                $o_g_where = ['a.user_id' => $this->user_id, 'a.order_status' => ['NOT IN', '1,3,5,6,8,18']];
                                $o_g_count = $order_model->alias('a')->whereExists(function ($query) use ($full_info) {
                                    $query->table('t_bu_order_commodity')->alias('b')->where('a.order_code=b.order_code')->where('b.full_id', $full_info['id']);
                                })->where($o_g_where)->field("count(*) number")->find();
                                $act['can_buy_number'] = $full_info['purchase_number'] - $o_g_count['number'];
                            } else {
                                $act['can_buy_number'] = 999;
                            }
                            $ac_json[] = $act;
                            $full_act_can[$full_info['id']] = $act['can_buy_number'];
                        }
                    }
                }
                if ($v['n_dis']) {
                    $n_dis_limit = json_decode($v['n_dis'], true);
                    if (isset($n_dis_limit[$this->channel_type][0])) {
                        $segment_info = get_user_segment_info();
                        $membership = $segment_info['membership_level'];
                        $owner = $segment_info['brand_owner_label'];
                        $where = sprintf("user_segment=0 or (user_segment=1 and FIND_IN_SET('%s',user_segment_options)) or (user_segment=2 and FIND_IN_SET('%s',user_segment_options))", $membership, $owner);
                        $n_dis_info = $n_dis_model->where($where)->where(['id' => $n_dis_limit[$this->channel_type][0]])->find();

                        if ($n_dis_info) {
                            $commodity_dis_label = get_user_segment_label($n_dis_info['user_segment']);
                            $ac_json[] = [
                                'act_code' => 6,
                                'act_id'   => $n_dis_info['id'],
                                'act_name' => $n_dis_info['title'],
                                'commodity_dis_act_user_segment' => $n_dis_info['user_segment'],
                                'commodity_dis_label' => $commodity_dis_label,
                                'commodity_dis_label_cn' => get_user_segment_label_cn($commodity_dis_label),
                            ];
                        }
                    }
                }
                $list[$k]['act'] = $ac_json;
                ///$sku_image     =json_decode($row['sku_image'],true);
                $sp_value_arr  = explode(',', $v['sp_value_list']);
                $sp_value_list = $sp_model->getAllList(['a.id' => ['in', $sp_value_arr]]);
                $sku_value     = '';
                if(!$v['sku_c_json']){
                    if ($sp_value_list) {
                        foreach ($sp_value_list as $vv) {
                            $sku_value .= $vv['sp_name'] . ":" . $vv['sp_value_name'] . ", ";
                        }
                    }
                }

                $list[$k]['sp_name']   = $sku_value;
                $list[$k]['sku_image'] = !empty($v['image']) ? $v['image'] : $v['cover_image'];
//                $can_number            = $v['stock'];//购买数量==库存
                $can_number            = 999;//购买数量==库存
//                dd($can_number);
                $yh_sku_info           = [];
                //从数据库判断添加购物车的时候是否选择了活动
                if ($v['act_json']) {
                    $yh_data_json_str[$v['cart_id']] = $v['act_json'];
                    $yh_sku_info                    = $sCom->yh_info($yh_data_json_str);
                }

                $yh_wi_sku_info           = [];
                if ($v['wi_act_json']) {
                    $yh_data_json_str[$v['cart_id']] = $v['wi_act_json'];
                    $yh_wi_sku_info = $sCom->yh_wi_info($yh_data_json_str);
                }
//                dd($yh_sku_info);
                //限时购信息
                $yh = 0;//后续==0要更新掉act_json='';
                $wi_yh = 0;
                //不判断加入购物车的时候是否有限时购
                //只要商品有限时购，并且规格有限时购的东西，默认给他加上限时购ID && isset($yh_sku_info[$v['cart_id']]['limit_id'])
                //显示优惠增加一个要有限时购或者没有任何活动才进去limit_dis
                $one_act_json = json_decode($v['act_json'],true);
                if ($v['limit_dis'] && (isset($one_act_json[1]) || !$one_act_json)) {
                    $dis_limit = json_decode($v['limit_dis'], true);
                    if (isset($dis_limit[$this->channel_type][0])) {
                        $dis_id = $dis_limit[$this->channel_type][0];
                        $limit_model = new DbLimitDiscountCommodity();
                        $limit_row   = $limit_model->getOneDisByCommodity($v['commodity_id'], $dis_id);
                        if ($limit_row) {
//                            $sku_price = json_decode($limit_row['sku_price'], true);
                            $l_sku_id = $v['sku_id'];
                            //组合商品不传skuid
                            if($v['sku_c_json_cn'] && $v['sku_c_json_cn']<>'[]'){
                                $l_sku_id='';
                            }
                            $limit  = $sCom->_limit($v['commodity_id'], $dis_id, $this->user_id, $this->channel_type, $list[$k]['b_act_price'], $list[$k]['work_price_all_b_act'], $v['count'], $l_sku_id);

                            $can_number = $limit['can_no'];
                            $limit_info_info = $limit['limit_info'];
                            $limit_sku_price = json_decode($limit_info_info['sku_price'], true);
                            if ($can_number > 0) {
                                $is_save_dis_act=0;
                                //被清空了限时折扣信息，或者限时折扣id与进行的限时折扣ID不一致，则取最新的限时折扣ID
                                //本来没有活动
                                //限时活动对商品，工时，商品+工时
//                                    if(!isset($yh_sku_info[$v['sku_id']]['limit_id'])){
                                if(!$v['act_json']){
                                    $is_save_dis_act =1;
                                }
                                //选择限时活动。
                                if((isset($yh_sku_info[$v['cart_id']]['limit_id']) || $is_save_dis_act)  && (empty($l_sku_id) || isset($limit_sku_price[$l_sku_id]))){
                                    //限时购的商品有优惠就有，没有就跟原价一致
                                    $list[$k]['price'] = $v['use_discount'] ? $limit_info_info['goods_after_price'] : $limit_info_info['original_goods_after_price'];
                                    $list[$k]['work_price_all'] = $limit_info_info['work_time_after_price'];
                                    $list[$k]['goods_yh_money'] = $v['use_discount'] ? $limit_info_info['goods_dis_all'] : $limit_info_info['original_goods_dis_all'];
                                    $list[$k]['work_yh_money']  = $limit_info_info['work_dis_all'];
                                    $list[$k]['limit_discount_id'] = $limit_row['limit_discount_id'];
                                    $list[$k]['limit_info'] = $limit_info_info;
//                                    $list[$k]['limit_dis_type']    = $limit_row['dis_type'];
                                    if(in_array($limit_info_info['discount_type'],[1,3])){
                                        $ac_dis_type = $limit_info_info['dis_type'];
                                        $ac_dis_count = $limit_info_info['goods_dis_all'];
                                        $list[$k]['ac_dis_type'] = $ac_dis_type;
                                        $list[$k]['ac_dis_count'] = $ac_dis_count;

                                        $list[$k]['ac_origin_dis_count'] = $ac_dis_count;
                                        $list[$k]['ac_origin_dis_type'] = $ac_dis_type;
                                    }
                                    //但是活动ID不对 修改ID
                                    if (isset($yh_sku_info[$v['cart_id']]['limit_id']) && $yh_sku_info[$v['cart_id']]['limit_id'] <> $limit_row['limit_discount_id']) {
                                        $is_save_dis_act = 1;
                                    }

                                    $list[$k]['commodity_dis_act_user_segment'] = $limit['user_segment'];
                                    if ($limit['user_segment']) {
                                        $list[$k]['commodity_dis_label'] = get_user_segment_label($limit['user_segment']);
                                        $list[$k]['commodity_dis_label_cn'] = get_user_segment_label_cn($list[$k]['commodity_dis_label']);
                                    }
                                    $list[$k]['commodity_dis_act_segment_price'] = $limit_info_info['goods_after_price'];
                                    $list[$k]['commodity_dis_act_original_price'] = $limit_info_info['original_goods_after_price'];
                                }
                                //自动有优惠，不清空优惠信息
                                $yh                            = 1;
                                if($is_save_dis_act==1){
                                    $cart_model->where(['id' => $v['cart_id']])->update(['act_json' => json_encode([1 => $limit_row['limit_discount_id']]),'modifier'=>'n_c_01']);//记录数据库
                                }
                            }else{

                            }
                        }
                    }
                }
                // 秒杀
                if ($v['seckill_dis'] && (isset($one_act_json[10]) || !$one_act_json)) {
                    $dis_seckill = json_decode($v['seckill_dis'], true);
                    if (isset($dis_seckill[$this->channel_type][0])) {
                        $dis_id = $dis_seckill[$this->channel_type][0];
                        $seckill_model = new DbSeckillCommodity();
                        $seckill_row   = $seckill_model->getSeckillDisComInfonoTime($v['commodity_id'], $dis_id);
                        if ($seckill_row) {
//                            $sku_price = json_decode($limit_row['sku_price'], true);
                            $l_sku_id = $v['sku_id'];
                            //组合商品不传skuid
                            if($v['sku_c_json_cn'] && $v['sku_c_json_cn']<>'[]'){
                                $l_sku_id='';
                            }
                            $is_save_dis_act = 0;
                            if(!$v['act_json']){
                                $is_save_dis_act =1;
                            }
                            $seckill  = $sCom->_seckill($v['commodity_id'], $dis_id, $this->user_id, $this->channel_type, $list[$k]['b_act_price'],$list[$k]['work_price_all_b_act'],$v['count'],$l_sku_id);
                            //                            $v['count_stock'] =min($seckill['kill_count'],$v['count_stock']);//秒杀库存
                            $can_number = $seckill['can_no'];
                            $miaosha_stock = $seckill['kill_count']; //秒杀库存
                            $seckill_info_info = $seckill['seckill_info'];
                            $seckill_info_info['miaosha_stock'] = $miaosha_stock;
                            $seckill_sku_price = json_decode($seckill_info_info['sku_price'], true);
                            // if((isset($yh_sku_info[$v['cart_id']]['seckill_id']) || $is_save_dis_act)  && (empty($l_sku_id) || isset($seckill_sku_price[$l_sku_id]))){
                            //20230424增加一个秒杀库存》0 && $miaosha_stock>0
                            if ($can_number > 0  && $miaosha_stock>0) {
                                if(in_array($seckill_info_info['act_status'],[1,2])){
                                    if($seckill_info_info['act_status'] == 2) $list[$k]['price'] = $v['use_discount'] ? $seckill_info_info['goods_after_price'] : $seckill_info_info['original_goods_after_price'];//秒杀前购买
                                    $yh = 1;
                                    $list[$k]['work_price_all'] = $seckill_info_info['work_time_after_price'];
                                    $list[$k]['goods_yh_money'] = $v['use_discount'] ? $seckill_info_info['goods_dis_all'] : $seckill_info_info['original_goods_dis_all'];
                                    $list[$k]['work_yh_money']  = $seckill_info_info['work_dis_all'];
                                    $list[$k]['seckill_id'] = $seckill_row['seckill_id'];
                                    $list[$k]['act_status'] = $seckill_info_info['act_status'];
                                    $list[$k]['seckill_dis_price'] = $seckill_info_info['goods_after_price'];//秒杀前购买
                                    //秒杀增加一个活动进行中了就自动更新活动ID
                                    if($seckill_info_info['act_status']==2){
                                        $yh                            = 1;
                                        if($is_save_dis_act==1){
                                            $cart_model->where(['id' => $v['cart_id']])->update(['act_json' => json_encode([10 => $dis_id]),'modifier'=>'n_c_01']);//记录数据库
                                        }
                                        if(in_array($seckill_info_info['discount_type'],[1,3])){
                                            $ac_dis_type = $seckill_info_info['dis_type'];
                                            $ac_dis_count = $seckill_info_info['goods_dis_all'];
                                            $list[$k]['ac_dis_type'] = $ac_dis_type;
                                            $list[$k]['ac_dis_count'] = $ac_dis_count;

                                            $list[$k]['ac_origin_dis_count'] = $ac_dis_type;
                                            $list[$k]['ac_origin_dis_type'] = $ac_dis_count;
                                        }

                                        if ($seckill_info_info['dis_type'] == 3) {
                                            $list[$k]['commodity_dis_user_segment'] = 0;
                                        }

                                        $list[$k]['commodity_dis_act_user_segment'] = $seckill['user_segment'];
                                        if ($seckill['user_segment']) {
                                            $list[$k]['commodity_dis_label'] = get_user_segment_label($seckill['user_segment']);
                                            $list[$k]['commodity_dis_label_cn'] = get_user_segment_label_cn($list[$k]['commodity_dis_label']);
                                        }
                                        $list[$k]['commodity_dis_act_segment_price'] = $seckill_info_info['goods_after_price'];
                                        $list[$k]['commodity_dis_act_original_price'] = $seckill_info_info['original_goods_after_price'];
                                    }

                                    if ($seckill['user_segment']) {
                                        $seckill_info_info['commodity_dis_label'] = get_user_segment_label($seckill['user_segment']);
                                        $seckill_info_info['commodity_dis_label_cn'] = get_user_segment_label_cn($seckill_info_info['commodity_dis_label']);
                                    }
                                }
                            }else{
//                                $can_number = 999;
                                $yh                            = 0;
                            }
                            if(in_array($seckill_info_info['act_status'], [1,2])) {
                                $list[$k]['seckill_id'] = $seckill_row['seckill_id'];
                                $list[$k]['act_status'] = $seckill_info_info['act_status'];
                                $list[$k]['seckill_info'] = $seckill_info_info;
                            }
                        }
                    }
                }


                $one_wi_act_json = json_decode($v['wi_act_json'],true);
                if ($v['limit_wi_dis'] && $one_wi_act_json) {
                    $dis_limit_wi = json_decode($v['limit_wi_dis'], true);
                    if (isset($dis_limit_wi[$this->channel_type][0])) {
                        $dis_wi_id = $dis_limit_wi[$this->channel_type][0];
                        $limit_model = new DbLimitDiscountCommodity();
                        $limit_wi_row = $limit_model->getOneDisByCommodity($v['commodity_id'], $dis_wi_id);
                        if ($limit_wi_row) {
                            $l_sku_id = $v['sku_id'];
                            if ($v['sku_c_json_cn'] && $v['sku_c_json_cn'] <> '[]') {
                                $l_sku_id = '';
                            }
                            $limit_wi = $sCom->_limit_wi($v['commodity_id'], $dis_wi_id, $this->user_id, $this->channel_type, $list[$k]['work_price_all_b_act'], $v['count'], $l_sku_id);
                            $limit_wi_info = $limit_wi['limit_info'];
                            if ($limit_wi['can_no'] > 0) {
                                //选择限时活动。
                                if (isset($yh_wi_sku_info[$v['cart_id']]['limit_id'])) {
                                    $list[$k]['work_price_all'] = $limit_wi_info['work_time_after_price'];
                                    $list[$k]['work_yh_money'] = $limit_wi_info['work_dis_all'];
                                    $list[$k]['limit_wi_discount_id'] = $limit_wi_row['limit_discount_id'];
                                    $list[$k]['limit_wi_info'] = $limit_wi_info;
                                }
                                $wi_yh = 1;
                            }
                        }
                    }
                }
                if(in_array($v['dd_commodity_type'],[1,3,4,12])){
                    $can_number = min($can_number,1);
                    if($v['maintain_dis']>0){
                        $list[$k]['b_act_price'] = sprintf("%.2f",bcdiv($v['b_act_price'],$v['maintain_dis']/10,0));

                        if ($list[$k]['use_discount'] && ($list[$k]['commodity_dis_label'] != '') && $list[$k]['commodity_dis_user_segment'] &&
                            ($list[$k]['commodity_dis_act_segment_price'] != $list[$k]['commodity_dis_original_price'])) {
                            $list[$k]['ac_dis_type'] = 2;
                            $list[$k]['ac_dis_count'] = round($list[$k]['commodity_dis_original_price'] - $list[$k]['commodity_dis_act_segment_price'], 2);
                        }
                    }
                }
                $list[$k]['can_number'] = $can_number;
                // 判断秒杀前和进行中
                if(isset($list[$k]['act_status']) && in_array($list[$k]['act_status'], [1,2]) && $can_number == 0) {
                    $can_number = 999; // 按普通商品购买
                }
                $real_count =  min($can_number,$v['stock']);
                if($v['count']>$real_count){

                    $cart_model->where(['id' => $v['cart_id']])->update(['count' => $real_count,'modifier'=>'n_c_01']);
                    $list[$k]['count'] = $real_count;//加购数》库存的时候强制==库存
                }

                if($v['count']==0 && $real_count>0){
                    $cart_model->where(['id' => $v['cart_id']])->update(['count' => 1,'modifier'=>'n_c_01']);
                    $list[$k]['count'] = 1;//购物车==0 但是可以买，那么就改成1
                }
                if(($real_count<=0 && $v['count_stock']==0)|| $can_number<=0 || $v['count_stock']==0 || $v['mail_type']==4){
                    $list[$k]['is_sold_out'] =0;
                }

                //满减内容
                if (isset($yh_sku_info[$v['cart_id']]['full_id']) && $v['full_dis']) {
                    $dis_full = json_decode($v['full_dis'], true);
                    if (isset($dis_full[$this->channel_type][0])) {
                        if ($yh_sku_info[$v['cart_id']]['full_id'] == $dis_full[$this->channel_type][0]) {
                            if (isset($full_act_can[$yh_sku_info[$v['cart_id']]['full_id']]) && $full_act_can[$yh_sku_info[$v['cart_id']]['full_id']] > 0) {
                                $list[$k]['full_id'] = $yh_sku_info[$v['cart_id']]['full_id'];
                                $yh = 1;

                                foreach ($ac_json as $act_item) {
                                    if ($act_item['act_code'] == '3' && $act_item['act_id'] == $yh_sku_info[$v['cart_id']]['full_id']) {
                                        if ($act_item['commodity_dis_act_user_segment']) {
                                            $list[$k]['commodity_dis_act_label'] = $act_item['commodity_dis_label'];
                                            $list[$k]['commodity_dis_act_label_cn'] = $act_item['commodity_dis_label_cn'];
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                if (isset($yh_wi_sku_info[$v['cart_id']]['full_id']) && $v['full_wi_dis']) {
                    $dis_full_wi = json_decode($v['full_wi_dis'], true);
                    if (isset($dis_full_wi[$this->channel_type][0])) {
                        if ($yh_wi_sku_info[$v['cart_id']]['full_id'] == $dis_full_wi[$this->channel_type][0]) {
                            $full_wi_info = $full_model->getOneByPk($dis_full_wi[$this->channel_type][0]);
                            $can_buy_number = 999;
                            if ($full_wi_info && $full_wi_info['purchase_number'] > 0) {
                                $o_g_where = ['b.full_wi_id' => $full_wi_info['id'], 'b.commodity_id' => $v['commodity_id'], 'a.user_id' => $this->user_id, 'a.order_status' => ['NOT IN', '1,3,5,6,8,18']];
                                $o_g_count = $order_model->alias('a')->join('t_bu_order_commodity b', 'a.order_code=b.order_code')->where($o_g_where)->field("sum(count) number")->find();
                                $can_buy_number = $full_wi_info['purchase_number'] - $o_g_count['number'];
                            }
                            if ($full_wi_info && $can_buy_number > 0) {
                                $list[$k]['full_wi_id'] = $yh_wi_sku_info[$v['cart_id']]['full_id'];
                                $wi_yh = 1;
                            }
                        }
                    }
                }

                //满减内容
                if (isset($yh_sku_info[$v['cart_id']]['n_dis_id']) && $v['n_dis']) {
                    $dis_n = json_decode($v['n_dis'], true);
                    if (isset($dis_n[$this->channel_type][0])) {
                        if ($yh_sku_info[$v['cart_id']]['n_dis_id'] == $dis_n[$this->channel_type][0]) {
                            $list[$k]['n_dis_id'] = $yh_sku_info[$v['cart_id']]['n_dis_id'];
                            $yh                   = 1;

                            foreach ($ac_json as $act_item) {
                                if ($act_item['act_code'] == '6' && $act_item['act_id'] == $yh_sku_info[$v['cart_id']]['n_dis_id']) {
                                    if ($act_item['commodity_dis_act_user_segment']) {
                                        $list[$k]['commodity_dis_act_label'] = $act_item['commodity_dis_label'];
                                        $list[$k]['commodity_dis_act_label_cn'] = $act_item['commodity_dis_label_cn'];
                                    }
                                }
                            }
                        }
                    }
                }
                if ($yh == 0) {
                    $list[$k]['act_json'] = ''; // 接口返回空
                    $cart_model->where( ['id' => $v['cart_id']])->update(['act_json' => '','modifier'=>'n_c_01']);
                }
                if ($wi_yh == 0) {
                    $cart_model->where( ['id' => $v['cart_id']])->update(['wi_act_json' => '','modifier'=>'n_c_02']);
                }

                //添加时间
                $list[$k]['ap_time']= date('Y-m-d H:i:s');


//                //0423版本
//                if(!empty($v['group_commodity_ids_info'])) {
//                    $stock_tmp = 0;
//                    if (!empty($group_must_sub_count_stock)) {
//                        $stock_tmp = min($group_must_sub_count_stock);
//                    } else if (!empty($group_sub_count_stock)) {
//                        $stock_tmp = min($group_sub_count_stock);
//                    }
//                    if($stock_tmp == 0) $list[$k]['is_sold_out'] = 0;
//                    if($list[$k]['count'] > $list[$k]['stock'])  $list[$k]['count']= $list[$k]['stock'];
//                    $list[$k]['count_stock'] = $list[$k]['stock'] =  $stock_tmp;
//                }
            }


            $full_ids      = [];
            $n_dis_ids     = [];
            $all_cart_list = [];//输出前端购物车列表
            $dndc_pay_pay_code = '';
            if($dd_dlr_pay){
                $dd_dlr_pay_arr = array_unique($dd_dlr_pay);
                if(count($dd_dlr_pay_arr)>1){
                    $dndc_pay_pay_code = '02';
                }else{
                    $dndc_pay_pay_code = $dd_dlr_pay_arr[0];
                }
            }else{
                $dndc_pay_pay_code='02';
            }
            foreach ($list as $k => $v) {
                if($v['dlr_pay_bank_type']=='01,02'){
                    $v['dlr_pay_bank_type'] = $dndc_pay_pay_code;
                }
                unset($list[$k]['group_commodity_ids_info']);//字段太长，APP受不了
                $have_dd_order = 0;
                if($v['order_vin']){
                    if ($v['commodity_class'] == 6 || $v['dd_commodity_type'] > 0) {
                        $have_dd_order = 1;
                    }
                    if(!$have_dd_order){
                        $cart_model->where(['id' => $v['cart_id']])->update(['order_vin' => '','modifier'=>'no_od_vin']);
                    }
                }
                //没关联车型不显示下单车型
                if(!$v['relate_car_ids']){
                    if($v['car_18n']){
                        //在列表这里更新了--********
                        $cart_model->where(['id' => $v['cart_id']])->update(['car_18n' => '','modifier'=>'n_c_18n']);
                    }
                    unset($v['cart_car_info']);
                    $list[$k]['car_18n']=0;
                    $v['car_18n']=0;
//                    unset($v['car_18n']);
                }
                if($v['price']<0){
                    $v['price']=0;
                }
                unset($list[$k]['relate_car_ids']);//字段太长，APP受不了
                if (isset($v['full_id'])) {
                    $all_cart_list[$v['full_id']]['full_id'] = $v['full_id'];
                    $full_ids[]                              = $v['full_id'];
                    $all_cart_list[$v['full_id']]['list'][]  = $v;
                } elseif (isset($v['n_dis_id'])) {
                    $all_cart_list[$v['n_dis_id']]['n_dis_id'] = $v['n_dis_id'];
                    $n_dis_ids[]                               = $v['n_dis_id'];
                    $all_cart_list[$v['n_dis_id']]['list'][]   = $v;
                } else {
                    $all_cart_list[$k]['list'][] = $v;
                }

            }
            foreach ($all_cart_list as $k => $v) {
                if (isset($v['full_id'])) {
                    $full_info                           = $full_model->getOneByPk($v['full_id']);
                    $all_full_money = 0;
//                    print_json();
                    foreach ($v['list'] as $vv) {
//                        $all_full_money += $vv['price'] * $vv['count'];
                        $goods_price = $vv['price']*$vv['count'];
                        $work_time = json_decode($vv['work_time_json'],true);
                        $work_time_all_price=0;
                        if(isset($work_time) && $work_time){
                            $work_time_all_price = $work_time['work_time_price']*$vv['work_time_number']*$vv['count'];
                        }

                        if($full_info['discount_type']==1){
                            $all_full_money += $goods_price;
//                $full_all_price += $vvv['goods_prices']+$vvv['work_time_price'];
                        }elseif ($full_info['discount_type']==2){
                            $all_full_money += $work_time_all_price;
                        }else{
                            $all_full_money += $goods_price+$work_time_all_price;
                        }
                    }
//                    dd($all_full_money);
                    $all_cart_list[$k]['all_full_money'] = $all_full_money;
                    if ($full_info) {
                        $full_rule = $full_info['full_discount_rules'];
                        if ($full_rule) {
                            $full_rule = json_decode($full_rule, true);
                            if ($full_info['user_segment']) {
                                $segment_info = get_user_segment_info();
                                $membership = $segment_info['membership_level'];
                                $owner = $segment_info['brand_owner_label'];
                                if ($full_info['user_segment'] == 1) {
                                    $full_rule = $full_rule[$membership];
                                } else {
                                    $full_rule = $full_rule[$owner];
                                }
                            }
//                            $full_rule_count =  count($full_rule);
                            foreach ($full_rule as $f_k => $f_r_v) {
                                if ($all_full_money >= $f_r_v[0]) {
                                    $mj    = $f_r_v[1];
                                    $mj_ed = $f_r_v[0];
                                    if ($f_k == 0) {
                                        $next_mj        = 0;
                                        $next_mj_ed     = 0;
                                        $next_mj_ed_cha = 0;
                                    } else {
                                        $next_mj        = $full_rule[$f_k - 1][1];
                                        $next_mj_ed     = $full_rule[$f_k - 1][0];
                                        $next_mj_ed_cha = $full_rule[$f_k - 1][0] - $all_full_money;
                                    }
                                    break;
                                } else {
                                    $mj             = 0;
                                    $mj_ed          = 0;
                                    $next_mj        = end($full_rule)[1];
                                    $next_mj_ed     = end($full_rule)[0];
                                    $next_mj_ed_cha = end($full_rule)[0] - $all_full_money;
                                }
                            }
                            if ($mj == 0 && $next_mj > 0) {
                                $full_word = sprintf("购满%s元，可减%s元，还差%s元", $next_mj_ed, $next_mj, $next_mj_ed_cha);
                            } elseif ($mj > 0 && $next_mj > 0) {
                                $full_word = sprintf("已减%s元，还差%s元，可减%s元", $mj, $next_mj_ed_cha, $next_mj);
                            } else {
                                $full_word = sprintf("已购满%s元，可减%s元", $mj_ed, $mj);
                            }
                            $all_cart_list[$k]['all_full_info'] = [
                                'mj'             => $mj,
                                'mj_ed'          => $mj_ed,
                                'next_mj'        => $next_mj,
                                'next_mj_ed'     => $next_mj_ed,
                                'next_mj_ed_cha' => $next_mj_ed_cha,
                                'full_word'      => $full_word
                            ];
                        }
                    }
                } elseif (isset($v['n_dis_id'])) {
                    $all_dis_count = 0;
                    foreach ($v['list'] as $vv) {
                        $all_dis_count += $vv['count'];
                    }
                    $all_cart_list[$k]['all_dis_count'] = $all_dis_count;
                    $n_dis_info_list                    = $n_dis_info_model->getList(['where' => ['n_id' => $v['n_dis_id']], 'order' => "piece desc"]);
                    if ($n_dis_info_list) {
                        $n_dis_t_info = $n_dis_model->getOneByPk($v['n_dis_id']);
                        foreach ($n_dis_info_list as $n_k => $n_v) {
                            if ($all_dis_count >= $n_v['piece']) {
                                $dis_p  = $n_v['discount'];
                                $dis_ed = $n_v['piece'];
                                if ($n_k == 0) {
                                    $next_dis    = 0;
                                    $next_dis_ed = 0;
                                } else {
                                    $next_dis    = $n_dis_info_list[$n_k - 1]["discount"];
                                    $next_dis_ed = $n_dis_info_list[$n_k - 1]["piece"];
                                }
                                break;
                            } else {
                                $dis_p       = 0;
                                $dis_ed      = 0;
                                $next_dis    = end($n_dis_info_list)["discount"];
                                $next_dis_ed = end($n_dis_info_list)["piece"];
                            }
                        }
                        $all_cart_list[$k]['all_dis_info'] = [
                            'dis_p'         => $dis_p,
                            'dis_info_desc' => $n_dis_t_info['des'],
                            'dis_ed'        => $dis_ed,
                            'next_dis'      => $next_dis,
                            'next_dis_ed'   => $next_dis_ed,
                        ];
                    }
                }
            }
            $all_cart_list = array_values($all_cart_list);
            return $this->setResponseData($all_cart_list)->send();
        } else {
            return $this->setResponseData([])->send();
        }

    }

    /**
     * @title 购物车活动重选优惠
     * @description 接口说明
     * @param name:unionid type:int require:1 default:0 other: desc:yonghuid
     * @param name:shop_cart_ids type:int require:1 default:0 other: desc:购物车ID
     * @param name:act_type_id type:int require:1 default:0 other: desc:活动类型ID：3满减，6N件N折
     * @param name:act_id type:int require:1 default:0 other: desc:活动ID
     *
     * @return 200:  成功
     * @return msg:提示信息
     * @return data:返回数据@!
     * @data full_word:满减词 full_yh:满减优惠 n_dis_money:N件优惠金额 n_dis_word:N件词 n_dis:N件折扣
     *
     * <AUTHOR>
     * @url /net-small/cart/act-change
     * @method GET
     *
     */
    public function actChange(CartValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("actChange")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $ids      = $requestData['shop_cart_ids'];
        $act_type = $requestData['act_type_id'];
        $act_id   = $requestData['act_id'];

        $set_sku_model    = new DbCommoditySetSku();
        $cart_model       = new BuShoppingCart();
        $full_model       = new DbFullDiscount();
        $n_dis_info_model = new DbNDiscountInfo();
        $n_dis_model      = new DbNDiscount();
        $net_goods = new NetGoods();

        $where         = ["cart.is_enable" => 1, 'cart.user_id' => $this->user_id, 'cart.id' => ['in', $ids]];
        $where[]       = ['exp', "FIND_IN_SET('{$this->channel_type}',d.up_down_channel_dlr)"];//加入渠道代码
        $field         = 'a.stock, b.sp_value_list, b.image, a.is_enable AS is_sku_out, b.sku_code, IF ((c.is_enable=0 OR c.is_enable=0), "0", 1) AS is_sold_out, c.commodity_name, c.cover_image, c.commodity_class, c.commodity_card_ids, c.card_id, c.id AS commodity_id, d.is_mail, d.is_store, favourable_introduction, favourable_detail, d.pay_style, a.price b_act_price, a.price, limit_dis, n_dis, group_dis, pre_dis, cheap_dis, full_dis, d.commodity_dlr_type_id,d.is_card_multic,d.mail_price,a.divided_into,a.install_fee,a.commodity_set_id,d.max_point,d.factory_points,d.dlr_points,cart.act_json,cart.id cart_id,cart.count,cart.commodity_id,cart.sku_id,cart.work_time_json,cart.sku_c_json_cn,cart.use_discount';
        $all_cart_list = $set_sku_model->getSkuFlatCart(['where' => $where, 'field' => $field, 'group' => "cart.id", 'order' => 'cart.id desc']);
//        echo $set_sku_model->getLastSql();
        if (!$all_cart_list) {
            return $this->setResponseError('信息不存在')->send();
        }
        $all_full_money  = 0;
        $all_work_money  = 0;
        $all_dis_count   = 0;
        $all_n_dis_money = 0;
//活动类型ID1限时折扣2团购3满减4全积分折扣5套装6N件N折7预售8立减
        foreach ($all_cart_list as $k => $v) {
            $v['work_price_all_b_act'] =0;
            if($v['sku_c_json_cn'] && $v['sku_c_json_cn']<>'[]'){
                $sub_sku = json_decode($v['sku_c_json_cn'],true);
                $sub_sku_id_arr =  array_keys($sub_sku);
                $sub_list = $set_sku_model->getList(['where'=>['id'=>['in',$sub_sku_id_arr]],'field'=>'price,stock,id,is_enable']);
                $one_g_price= 0;
                $one_sub_work_price=0;
                foreach ($sub_list as $vv){
                    $one_sub_price=$vv['price']* $sub_sku[$vv['id']]['count'];
                    $one_g_price+=$one_sub_price;
                    if(isset($sub_sku[$vv['id']]['work_time_price'])){
                        $one_sub_work_price += $sub_sku[$vv['id']]['work_time_price']*$sub_sku[$vv['id']]['work_time_number']* $sub_sku[$vv['id']]['count'];//*$v['count']
                    }
                    $sub_sku[$vv['id']]['all_goods_price'] =$one_sub_price;
                    if($vv['is_enable']==0 || $vv['stock']<1){
                        $list[$k]['is_sku_out']=0;
                    }
                }
                $v['work_price_all_b_act'] =$one_sub_work_price;//*$v['count']
                $v['price'] = $one_g_price;
            }

            //工时信息
            if($v['work_time_json']){
                $work_time = json_decode($v['work_time_json'],true);
                if($work_time){
                    $v['work_price_all_b_act'] =$work_time['work_time_price']*$work_time['work_time_number'];//*$v['count']
                }
            }

            $commodity_dis_info = $net_goods->getCommoditySegmentDiscount($v['commodity_id']);
            if ($commodity_dis_info) {
                if ($v['use_discount']) {
                    $v['price'] = $net_goods->getCommodityDisFinalPrice($commodity_dis_info, $v['price']);
                }
            }

            if ($act_type == 3) {
                $all_full_money += $v['price'] * $v['count'];
                $all_work_money +=$v['work_price_all_b_act']*$v['count'];
            } elseif ($act_type == 6) {
                $all_dis_count   += $v['count'];
                $all_n_dis_money += ($v['price']) * $v['count'];
            }
        }
//        var_dump($all_n_dis_money);
        $full_word   = '';
        $full_yh     = 0;
        $n_dis_money = 0;
        $n_dis_word  = '';
        $n_dis       = 0;
        if ($act_type == 3) {
            $full_info = $full_model->getOneByPk($act_id);
//            print_json($full_info);
            $full_rule = $full_info['full_discount_rules'];
            if($full_info['discount_type']==1){

            }elseif ($full_info['discount_type']==2){
                $all_full_money = $all_work_money;
            }else{
                $all_full_money += $all_work_money;
            }
//            echo $full_rule.'--';
//            echo $all_full_money;
            if ($full_rule) {
                $full_rule = json_decode($full_rule, true);
                if ($full_info['user_segment']) {
                    $segment_info = get_user_segment_info();
                    $membership = $segment_info['membership_level'];
                    $owner = $segment_info['brand_owner_label'];
                    if ($full_info['user_segment'] == 1) {
                        $full_rule = $full_rule[$membership];
                    } else {
                        $full_rule = $full_rule[$owner];
                    }
                }
                foreach ($full_rule as $f_k => $f_r_v) {
                    if ($all_full_money >= $f_r_v[0]) {
                        $mj    = $f_r_v[1];
                        $mj_ed = $f_r_v[0];
                        if ($f_k == 0) {
                            $next_mj        = 0;
                            $next_mj_ed     = 0;
                            $next_mj_ed_cha = 0;
                        } else {
                            $next_mj        = $full_rule[$f_k - 1][1];
                            $next_mj_ed     = $full_rule[$f_k - 1][0];
                            $next_mj_ed_cha = $full_rule[$f_k - 1][0] - $all_full_money;
                        }
                        break;
                    } else {
                        $mj             = 0;
                        $mj_ed          = 0;
                        $next_mj        = end($full_rule)[1];
                        $next_mj_ed     = end($full_rule)[0];
                        $next_mj_ed_cha = end($full_rule)[0] - $all_full_money;
                    }
                }
                $next_mj_ed_cha = round($next_mj_ed_cha,2);
                if ($mj == 0 && $next_mj > 0) {
                    $full_word = sprintf("购满%s元，可减%s元，还差%s元", $next_mj_ed, $next_mj, $next_mj_ed_cha);
                } elseif ($mj > 0 && $next_mj > 0) {
                    $full_word = sprintf("已减%s元，还差%s元，可减%s元", $mj, $next_mj_ed_cha, $next_mj);
                } else {
                    $full_word = sprintf("已购满%s元，可减%s元", $mj_ed, $mj);

                }
                $full_yh = $mj;
            }
        } elseif ($act_type == 6) {

            $n_dis_t_info = $n_dis_model->getOneByPk($act_id);
            $n_dis_where['n_id'] = $act_id;
            if ($n_dis_t_info['user_segment']) {
                $segment_info = get_user_segment_info();
                $membership = $segment_info['membership_level'];
                $owner = $segment_info['brand_owner_label'];

                if ($n_dis_t_info['user_segment'] == 1) {
                    $n_dis_where['segment_label'] = $membership;
                } else {
                    $n_dis_where['segment_label'] = $owner;
                }
            }
            $n_dis_info_list = $n_dis_info_model->getList(['where' => $n_dis_where, 'order' => "piece desc"]);
//            echo $n_dis_info_model->getLastSql();
            if ($n_dis_info_list) {
//                echo  $all_dis_count;
//                print_json($n_dis_info_list);
//                var_dump($n_dis_t_info);
                foreach ($n_dis_info_list as $n_k => $n_v) {
                    if ($all_dis_count >= $n_v['piece']) {
                        $dis_p  = $n_v['discount'];
                        $dis_ed = $n_v['piece'];
                        if ($n_k == 0) {
                            $next_dis    = 0;
                            $next_dis_ed = 0;
                        } else {
                            $next_dis    = $n_dis_info_list[$n_k - 1]["discount"];
                            $next_dis_ed = $n_dis_info_list[$n_k - 1]["piece"];
                        }
                        break;
                    } else {
                        $dis_p       = 0;
                        $dis_ed      = 0;
                        $next_dis    = end($n_dis_info_list)["discount"];
                        $next_dis_ed = end($n_dis_info_list)["piece"];
                    }
                }
                //tzl：不到也可以显示活动。。。
//                $n_dis_money = round($all_n_dis_money - ($all_n_dis_money * ($dis_p / 10)), 2);
                $n_dis_word  = $n_dis_t_info['des'];
                $n_dis       = $dis_p;
//
                if($dis_p>0){
                    $n_dis_money = round($all_n_dis_money - ($all_n_dis_money * ($dis_p / 10)), 2);
//                    $n_dis_word  = $n_dis_t_info['des'];
                    $n_dis       = $dis_p;
                }else{
                    $n_dis_money =0;
//                    $n_dis_word  = '';
                }

            }

        }
        $data = [
            "full_word"   => $full_word,
            "full_yh"     => $full_yh,
            "n_dis_money" => $n_dis_money,
            "n_dis_word"  => $n_dis_word,
            "n_dis"       => $n_dis
        ];
        return $this->setResponseData($data)->send();
    }

    /**
     * @title 购物车显示卡券+金额总数.
     * @description 接口说明
     * @param name:shop_cart_ids type:int require:1 default:0 other: desc:购物车ID
     *
     * @return 200:  成功
     * @return msg:提示信息
     * @return data:返回数据@!
     * @data all_point:需付积分数 all_money:需付现金 limit_dis:限时优惠 full_dis:满减优惠 n_dis:N件折扣优惠
     *
     * <AUTHOR>
     * @url /net-small/cart/point-money
     * @method GET
     *
     */

    public function cart_point_money(CartValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("pointMoney")->check($requestData);
        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $ids      = $requestData['shop_cart_ids'];
        $cart_model    = new BuShoppingCart();
        $where         = ["cart.is_enable" => 1, 'cart.user_id' => $this->user_id, 'cart.id' => ['in', $ids]];//,"b.is_enable"=>1,'a.stock'=>['>',0],
        //前端如果没有库存不会请求这里了，不用判断库存，不然组合商品会有问题
        if ($this->channel_type == 'GWSM'){
            $where[] = ['exp', 'cart.is_empower = 0 || (cart.is_empower = 1 && e.dd_commodity_type = 0 && e.commodity_class != 6)'];
        }
        $field         = 'a.stock, b.sp_value_list, b.image,a.is_enable  AS is_sku_out, b.sku_code, IF (( c.is_enable = 0 OR d.is_enable = 0 or c.is_enable is null  OR d.is_enable is null ), "0",1) AS is_sold_out, c.commodity_name, c.cover_image, c.commodity_class, c.commodity_card_ids, c.card_id, c.id AS commodity_id, d.is_mail, d.is_store, favourable_introduction, favourable_detail, d.pay_style, a.price b_act_price, a.price, limit_dis, n_dis, group_dis, pre_dis, cheap_dis, full_dis,seckill_dis, d.commodity_dlr_type_id,d.is_card_multic,d.mail_price,a.divided_into,a.install_fee,a.commodity_set_id,d.max_point,d.factory_points,d.dlr_points,cart.act_json,cart.gift_c_json,cart.gift_act_id,cart.use_discount,cart.wi_act_json,cart.id cart_id,cart.count,cart.commodity_id,cart.sku_id,cart.order_mail_type,cart.order_mail_type mail_method,cart.work_time_json,cart.sku_c_json_cn,cart.dd_dlr_code,cart.car_18n,c.dd_commodity_type,b.maintain_q maintain_dis,b.relate_car_ids,cart.gift_card_id';//point-money

        $all_cart_list          = $cart_model->getCartList(['where' => $where, 'field' => $field, 'channel_type' => $this->channel_type, 'group' => "cart.id", 'order' => 'cart.last_updated_date desc']);
//        echo $cart_model->getLastSql();die();
//        print_json($all_cart_list);
        if (!$all_cart_list) {
            return $this->setResponseError('购物车信息不存在!')->send();
        }

        //0423版本
        $goods_set_sku_model =  new DbCommoditySetSku();
        foreach($all_cart_list as $k=>$all_cart_item){
            $sku_key_arr = [];
            if(!empty($all_cart_item['sku_c_json_cn'])){
                $all_cart_item_arr = json_decode($all_cart_item['sku_c_json_cn'],true);

                foreach($all_cart_item_arr as $sku_key=>$sku_item ){
                    $sku_key_arr[] = $sku_key;
                }
                $group_sub_goods_id = [];
                if(!empty($sku_key_arr)){
                   $goods_tmp_list =  $goods_set_sku_model->whereIn('id',$sku_key_arr)->select();
                   $group_set_id_arr = [];
                   foreach($goods_tmp_list as $goods_tmp_item){
                       if(!empty($goods_tmp_item['group_sub_set_sku_id'])){
                           $group_set_id_arr[] = $goods_tmp_item['group_sub_set_sku_id'];
                           $group_sub_goods_id[$goods_tmp_item['group_sub_set_sku_id']] = $goods_tmp_item['id'];
                       }
                   }
                   if(!empty($group_set_id_arr)){
                       $new_sub_group_goods = [];
                       $sub_goods_tmp_list =  $goods_set_sku_model->whereIn('id',$group_set_id_arr)->where(['stock'=>['>',0]])->select();
                       foreach($sub_goods_tmp_list as $goods_sub_item){
                           $new_sub_group_goods[$group_sub_goods_id[$goods_sub_item['id']]] = $all_cart_item_arr[$group_sub_goods_id[$goods_sub_item['id']]];
                       }
                       if(!empty($new_sub_group_goods)){
                           $all_cart_list[$k]['sku_c_json_cn'] = json_encode($new_sub_group_goods);
                       }
                   }
                }
            }
       }
       /// $new_all_cart_list = [];
        foreach($all_cart_list as $k=>$all_cart_item){
            if(!empty($all_cart_item['sku_c_json_cn'])){
                $new_sku_c_json_arr = [];
                $sku_c_json_cn = json_decode($all_cart_item['sku_c_json_cn'],true);
                foreach($sku_c_json_cn as $sku_c_json_cn_item){
                    $stcok_info =  $goods_set_sku_model->alias("a")->join("t_db_commodity_set_sku b","b.id=a.group_sub_set_sku_id")->where(['a.id'=>$sku_c_json_cn_item['sku']])->field("b.*")->find();
                    if($stcok_info['is_enable'] == 1 && $stcok_info['stock'] > 0){
                        $new_sku_c_json_arr[$sku_c_json_cn_item['sku']] = $sku_c_json_cn_item;
                    }
                }
                $all_cart_list[$k]['sku_c_json_cn'] = json_encode($new_sku_c_json_arr);
            }else{
                if(!$all_cart_item['is_sku_out'] || !$all_cart_item['is_sold_out']){
                    unset($all_cart_list[$k]);
                    continue;
                }
            }

        }
//        print_json($all_cart_list);
        $net_cart = new NetCart();
        $params = $net_cart->card_point_yh($all_cart_list,$this->user_id,$this->channel_type);
        return $this->setResponseData($params)->send();
    }

    /**
     * 购物车数量
     * */
    public function cart_count(){
        $count = $this->shop_cart_model->getShopCartCount($this->user_id, $this->brand, $this->channel_type);
        return $this->setResponseData(['cart_count' => $count])->send();
    }




    /**
     * @title 选择专营店
     * @description 接口说明
     * @param name:commodity_id type:int require:0 default:0 other: desc:商品ID
     * @param name:area_id type:int require:0 default:0 other: desc:地区ID,不传可得地区列表,
     * @param name:group_id type:int require:0 default:0 other: desc:专营店分组ID运维提供
     * @param name:order type:string require:0 default:score other: desc:排序方式：score为综合评分排序，distance为距离排序
     * @param name:longitude type:string require:0 other: desc:经度，不传则不计算距离
     * @param name:latitude type:string require:0 other: desc:纬度，不传则不计算距离
     * 专营店的省市也是拿的营销平台 wechat/point_get_city
     * 快递类的  manager/base_city?
     * 然后再根据地区拿的专营店列表
     *
     * @return 200:成功
     * @return msg:提示信息
     * @return data:返回数据地区列表或者专营店列表@!
     * @data id:专营店id dlr_name:专营店名称 dlr_code:专营店编码 area_id城市id base_sale_tel:销售电话 base_service_tel:服务电话 base_address:地址 base_dlr_type:网店类型：1一网店，2二网店 base_comprehensive_score:综合评分 base_service_score:服务评分 base_product_score:商品评分 base_is_drive:是否试驾 0否 1是 base_is_ssa_fast:是否开通快速保养 base_is_ssa_self:是否开通自助保养 base_is_ssa_spray:是否开通极速钣喷 base_is_take_send:是否取送车 base_is_friday:是否星光星期五 distance距离
     *
     * <AUTHOR>
     * @url /net-small/cart/dlr
     * @method GET
     *
     */
    public function choose_dlr(CartValidate $validate)
    {

        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("dlr")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $brand_channel = DbDlr::$brand_channel;
        $channel_type  = input('channel_sm');
        if (empty($channel_type) || !isset($brand_channel[$channel_type])) {
            return $this->setResponseError('渠道参数错误')->send();
        }

        $brand = $brand_channel[$channel_type];

        $commodity_id = $requestData['commodity_id'] ?? 0;
        $goods_ids = $requestData['goods_ids'] ?? [];
        $area_id      = $requestData['area_id'] ?? 0;
        $group_id     = $requestData['group_id'] ?? 0;
        $ly_bank_code = $requestData['ly_bank_code'] ?? '';
        $dlr_name     = $requestData['dlr_name'] ?? '';

        $field = "*";

        // 排序
        $order          = 'base_warn_type ASC';
        $distance_order = '';
        if (!empty($requestData['longitude']) && !empty($requestData['latitude'])) {
            $field          .= ",round((st_distance(point(base_longitude,base_latitude),point({$requestData['longitude']},{$requestData['latitude']}))*111195/1000),2) as distance";
            $distance_order = ', distance ASC';
        } else {
            $field .= ",null as distance";
        }

        if (empty($requestData['order']) || $requestData['order'] == 'score') {
            $order .= ', base_comprehensive_score DESC, base_service_score DESC, base_product_score DESC' . $distance_order;
        } else {
            $order .= $distance_order . ', base_comprehensive_score DESC, base_service_score DESC, base_product_score DESC';
        }
        $order .= ', base_dlr_type ASC, sort_order ASC';

        $where = [
            'is_enable'         => 1,
            'base_dealer_state' => 1,
            'area_id'           => $area_id,
            'brand_type'        => $brand,
            'dlr_get_source'    => $brand == 2 ? 2 : 1,
        ];
        if (!empty($dlr_name)){
            unset($where['area_id']);
            $where['dlr_name'] = ['like', "%$dlr_name%"];
        }

        if (!empty($ly_bank_code)) {
            $where['ly_bank_code'] = $ly_bank_code;
        }
        if ($brand == 1) {
            $group_id = 15;
        }
        if ($brand == 2) {
            $group_id = 37;
        }
        if ($brand == 3) {
            $group_id = 15;
        }

        $params = [
            'where' => $where,
            'field' => $field,
            'order' => $order,
        ];
        $new_group_id = [];
        if($goods_ids){
            $com_set_model =  new DbCommoditySet();
            $com_where = ['commodity_id'=>['in',$goods_ids]];
            $com_where[] = ['exp', " (find_in_set('{$channel_type}',up_down_channel_dlr)) "];
//            $com_list = $com_set_model->alias('a')->join('t_db_dlr_group g','find_in_set(g.id,a.dlr_groups)')->where($com_where)->field('g.dlr_code_value')->select();
            $group_list = $com_set_model->getList(['where'=>$com_where,'field'=>'dlr_groups,commodity_id']);

            if($group_list){
                foreach ($group_list as $v){
                    if($v['dlr_groups']){
                        $one_dlr_g_arr = explode(',',$v['dlr_groups']);
                        foreach ($one_dlr_g_arr as $vv){
                            if($vv){
                                $new_group_id[]=$vv;
                            }
                        }
                    }else{
                        $new_group_id[]=$group_id;
                    }
                }
            }
        }

        if(!$new_group_id){
            $new_group_id[]=$group_id;
        }
        //跳转保养套餐选择专营店  选择专营店的这个应该要单独迁出来
        if (config('PAY_CHEBABA') == 1) {
            if (in_array($commodity_id, [1513, 1515, 1517, 1518, 1519])) {
                return $this->cho_dlr_by($area_id, $new_group_id);
            } else {
                return $this->cho_dlr($area_id, $new_group_id, $params, $dlr_name,$this->user);
            }
        } else {
            if ($commodity_id == 488) {
                return $this->cho_dlr_by($area_id, $new_group_id);
            } else {
                return $this->cho_dlr($area_id, $new_group_id, $params, $dlr_name,$this->user);
            }
        }
    }

    //公用版选择专营店
    public function cho_dlr($area_id = '', $group_id=[], $params = [], $dlr_name = '',$user)
    {
        if (!$group_id) {
            $group_id = [15];
        }
        $dlr_model       = new DbDlr();
        $dlr_group_model = new DbDlrGroup();
        $dlr_group       = $dlr_group_model->getList(['where'=>['id'=>['in',$group_id]]]);//15	官微商城
        $dlr_code_arr=[];
//        $nev_where=[];
//        print_json($user);
//        if(isset($user['is_nev']) && $user['is_nev']){
//            $nev_where = ['has_nev_service_type'=>['<>',0]];
//        }else{
//            $nev_where = ['has_ice_service_type'=>1];
//        }
        $nev_where = $user['nev_where'];
        foreach ($dlr_group as $dv){
            if($dv['dlr_code_value']){
                $dlr_code_one =  explode(',',$dv['dlr_code_value']);
                if($dlr_code_arr){
                    $dlr_code_arr = array_intersect($dlr_code_arr,$dlr_code_one);//配置 了就是交集了
                }else{
                    $dlr_code_arr = $dlr_code_one;
                }
            }

        }
        $dlr_code_arr =  array_unique($dlr_code_arr);

//        echo $group_id;die();
        if ($area_id || !empty($dlr_name)) {
            if ($group_id == 25) {
                $ac_model                    = new AcInsuranceSpecialist();
                $dlr_code_value              = $ac_model->getList(['where' => ['is_enable' => 1], 'field' => 'dlr_code', 'group' => 'dlr_code']);
                $dlr_code_arr = array_column(collection($dlr_code_value)->toArray(), 'dlr_code');
            }

            $params['where']['dlr_code'] = ["in", $dlr_code_arr];
            $params['where'] = array_merge($params['where'],$nev_where);//
//            $where = ['area_id' => $area_id, 'is_enable' => 1, 'dlr_code' => ["in", $dlr_group['dlr_code_value']]];
            $dlr_list = $dlr_model->getList($params);
//            print_json($dlr_list,$dlr_model->getLastSql());
            if ($dlr_list) {
                foreach ($dlr_list as $k => $v) {
                    $dlr_list[$k]['base_service_score'] = number_format(floatval($v['base_service_score']), 1);
                    $dlr_list[$k]['base_product_score'] = number_format(floatval($v['base_product_score']), 1);
                }
                return $this->setResponseData($dlr_list)->send();
            } else {
                return $this->setResponseData([])->send();
            }
        } else {
            $redis_name = "index_v5_normal_cho_dlr_js_" . implode(',',$group_id);
            $js         = redis($redis_name);
            $js         = [];
            if (!$js) {
                $area_model = new DbArea();
                $where      = ['is_enable' => 1, 'dlr_code' => ["in", $dlr_code_arr]];
                $where = array_merge($where,$nev_where);//
                $area_aa_id = $dlr_model->getColumn(['where' => $where, 'column' => "area_parent_id"]);
                $area_aa_id =  array_unique($area_aa_id);
                $area_gg    = $dlr_model->getColumn(['where' => $where, 'column' => "area_id"]);
                $area_gg =  array_unique($area_gg);
                $where      = ['area_id' => ['in', $area_aa_id], 'is_enable' => 1];
                $dlr_p_arr  = $area_model->getList(['where' => $where, 'field' => "area_name name, area_id code"]);

                $dlr_arr_s_arr =  $area_model->getList(['field' => 'area_name name, area_id code,area_parent_id', 'where' => [
                    'area_parent_id' => ['in', $area_aa_id],
                    'area_id'        => ['in', $area_gg],
                    'is_enable'      => 1
                ]]);
                $dlr_arr_one_to_arr = [];

                foreach ($dlr_arr_s_arr as $dlr_v){
                    $dlr_arr_one_to_arr[$dlr_v['area_parent_id']][] = ['name'=>$dlr_v['name'],'code'=>$dlr_v['code']];
                }

                if ($dlr_p_arr) {
                    foreach ($dlr_p_arr as $k => $v) {
//                        $dlr_arr = $area_model->getList(['field' => 'area_name name, area_id code', 'where' => [
//                            'area_parent_id' => $v['code'],
//                            'area_id'        => ['in', $area_gg],
//                            'is_enable'      => 1
//                        ]]);
                        if (isset($dlr_arr_one_to_arr[$v['code']])) {
                            $dlr_p_arr[$k]['sub'] = $dlr_arr_one_to_arr[$v['code']];
                        } else {
                            unset($dlr_p_arr[$k]);
                        }
                    }
                }
                $dlr_p_arr = array_merge($dlr_p_arr);
                $js        = json_encode_cn($dlr_p_arr);
                redis($redis_name, $js, mt_rand(300, 600));
            } else {
                $dlr_p_arr = json_decode($js, true);
            }
            return $this->setResponseData($dlr_p_arr)->send();
        }
    }

    //保养套餐选择专营店
    public function cho_dlr_by($city = '', $group_id = '')
    {
        $model = new AcByDlrCode();
        if ($city) {
            $where    = ['city' => $city];
            $dlr_list = $model->getList(['where' => $where]);
            if ($dlr_list) {
                return $this->setResponseData($dlr_list)->send();
            } else {
                return $this->setResponseData([])->send();
            }
        } else {
            $redis_name = "index_v5_normal_cho_dlr_by_js";
            $js         = '';
            if (!$js) {
                $sql      = "SELECT prov, city FROM t_ac_by_dlr_code GROUP BY city ORDER BY prov ASC ";
                $res      = $model->query($sql);
                $prov_arr = array_unique(array_column($res, 'prov'));
                $all_arr  = [];
                foreach ($prov_arr as $k => $v) {
                    $all_arr[$k]['name'] = $v;
                    $all_arr[$k]['code'] = $k;
                    foreach ($res as $kk => $vv) {
                        $vvv['name'] = $vvv['code'] = $vv['city'];
                        if ($v == $vv['prov']) {
                            $all_arr[$k]['sub'][] = $vvv;
                        }
                    }
                }
                $js = json_encode_cn(array_values($all_arr));
                redis($redis_name, $js, 7200);
            } else {
                $all_arr = json_decode($js, true);
            }
            return $this->setResponseData($all_arr)->send();
        }
    }

    /**
     * @title 购物车赠品券数量校验
     * @description 校验购物车中主品关联的待激活赠品券数量是否超过已选择赠品数量
     * @param name:cart_ids type:string require:1 default: other: desc:购物车ID，多个用逗号分隔
     *
     * @return 200: 成功
     * @return msg:提示信息
     * @return data:返回数据
     *
     * <AUTHOR>
     * @url /net-small/cart/check-gift-card-count
     * @method POST
     */
    public function checkGiftCardCount(CartValidate $validate)
    {
        $requestData = $this->request->only(['cart_ids']);

        if (empty($requestData['cart_ids'])) {
            return $this->setResponseError('购物车ID不能为空')->send();
        }

        $cart_ids = explode(',', $requestData['cart_ids']);
        $cart_model = new BuShoppingCart();
        $net_goods = new NetGoods();

        // 查询购物车信息
        $cart_list = $cart_model->where([
            'id' => ['in', $cart_ids],
            'user_id' => $this->user_id,
            'is_enable' => 1
        ])->select();

        if (empty($cart_list)) {
            return $this->setResponseError('购物车数据不存在')->send();
        }

        $validation_results = [];
        $can_checkout = true;
        $error_message = '';

        foreach ($cart_list as $cart_item) {
            $result = $this->validateCartGiftCard($cart_item, $net_goods);
            $validation_results[] = $result;

            if (!$result['can_select']) {
                $can_checkout = false;
                $error_message = $result['message'];
                break;
            }
        }

        $response_data = [
            'can_checkout' => $can_checkout,
            'message' => $error_message,
            'validation_results' => $validation_results
        ];

        if (!$can_checkout) {
            return $this->setResponseError($error_message, 422)->send();
        }

        return $this->setResponseData($response_data)->send();
    }

    /**
     * 校验单个购物车项的赠品券数量
     * @param array $cart_item 购物车项
     * @param NetGoods $net_goods NetGoods实例
     * @return array
     */
    private function validateCartGiftCard($cart_item, $net_goods)
    {
        $result = [
            'cart_id' => $cart_item['id'],
            'commodity_id' => $cart_item['commodity_id'],
            'can_select' => true,
            'message' => '',
            'gift_card_count' => 0,
            'selected_gift_count' => 0,
            'is_main_product' => false
        ];

        // 判断是否为主品（这里需要根据实际业务逻辑判断）
        $is_main_product = $this->isMainProduct($cart_item);
        $result['is_main_product'] = $is_main_product;

        if (!$is_main_product) {
            $result['message'] = '非主品商品，无需校验';
            return $result;
        }

        // 获取主品关联的待激活赠品券数量
        $gift_card_count = $this->getMainProductGiftCardCount($cart_item);
        $result['gift_card_count'] = $gift_card_count;

        // 获取已选择的赠品数量
        $selected_gift_count = $this->getSelectedGiftCount($cart_item);
        $result['selected_gift_count'] = $selected_gift_count;

        // 校验数量
        if ($gift_card_count > $selected_gift_count) {
            $result['can_select'] = false;
            $result['message'] = sprintf(
                '主品关联的待激活赠品券数量(%d)大于已选择赠品数量(%d)，不能勾选下单',
                $gift_card_count,
                $selected_gift_count
            );
        } else {
            $result['message'] = '校验通过';
        }

        return $result;
    }

    /**
     * 判断是否为主品
     * @param array $cart_item 购物车项
     * @return bool
     */
    private function isMainProduct($cart_item)
    {
        // 根据业务逻辑判断是否为主品
        // 这里可以根据商品类型、分类或其他字段来判断
        // 暂时简单判断：如果有gift_card_id字段且不为空，则认为是主品
        return !empty($cart_item['gift_card_id']);
    }

    /**
     * 获取主品关联的待激活赠品券数量
     * @param array $cart_item 购物车项
     * @return int
     */
    private function getMainProductGiftCardCount($cart_item)
    {
        if (empty($cart_item['gift_card_id'])) {
            return 0;
        }

        $card_r_model = new BuCardReceiveRecord();

        // 查询该用户该商品相关的待激活赠品券数量
        $count = $card_r_model->where([
            'user_id' => $this->user_id,
            'card_id' => $cart_item['gift_card_id'],
            'status' => 7, // 待激活状态
            'is_enable' => 1
        ])->count();

        return $count;
    }

    /**
     * 获取已选择的赠品数量
     * @param array $cart_item 购物车项
     * @return int
     */
    private function getSelectedGiftCount($cart_item)
    {
        $selected_count = 0;

        // 从gift_c_json字段中解析已选择的赠品数量
        if (!empty($cart_item['gift_c_json'])) {
            $gift_c_json = json_decode($cart_item['gift_c_json'], true);
            if (is_array($gift_c_json)) {
                foreach ($gift_c_json as $gift_item) {
                    $selected_count += intval($gift_item['count'] ?? 0);
                }
            }
        }

        return $selected_count;
    }

}
