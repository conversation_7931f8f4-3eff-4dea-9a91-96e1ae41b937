<?php
/**
 * Created by PhpStorm.
 * User: lzx
 * Date: 2020/12/17
 * Time: 10:26 AM
 */

namespace app\net_small\controller;


use api\lianyou\Ccs;
use api\wechat\Card;
use app\common\model\act\AcGroup;
use app\common\model\act\AcSignCount;
use app\common\model\bu\BuActivityPersonnel;
use app\common\model\bu\BuCardReceiveRecord;
use app\common\model\bu\BuCheapSuitIndex;
use app\common\model\bu\BuOrder;
use app\common\model\bu\BuOrderCommodity;
use app\common\model\bu\BuShoppingCart;
use app\common\model\bu\BuUserCollection;
use app\common\model\db\DbAfterSaleOrders;
use app\common\model\db\DbCard;
use app\common\model\db\DbCarSeries;
use app\common\model\db\DbCommodity;
use app\common\model\db\DbCommodityExpand;
use app\common\model\db\DbCommodityFlat;
use app\common\model\db\DbCommoditySet;
use app\common\model\db\DbCommoditySetSku;
use app\common\model\db\DbCommoditySkuCode;
use app\common\model\db\DbCommodityType;
use app\common\model\db\DbCustomerService;
use app\common\model\db\DbDlr;
use app\common\model\db\DbFightGroup;
use app\common\model\db\DbFullDiscount;
use app\common\model\db\DbGift;
use app\common\model\db\DbHomeType;
use app\common\model\db\DbLimitDiscount;
use app\common\model\db\DbLimitDiscountCommodity;
use app\common\model\db\DbNDiscount;
use app\common\model\db\DbNDiscountCommodity;
use app\common\model\db\DbPreSale;
use app\common\model\db\DbSeckill;
use app\common\model\db\DbSeckillCommodity;
use app\common\model\db\DbSection;
use app\common\model\db\DbSpecValue;
use app\common\model\db\DbSystemValue;

//use app\common\net_service\Common;
//use app\net_small\controller\Common;
use app\common\model\inter\IRequestLog;
use app\common\net_service\NetComment;
use app\common\net_service\NetGoods;
use app\common\net_service\NetOrder;
use app\common\net_service\NetUser;
use app\common\port\connectors\JdCloudWarehouse;
use app\common\port\connectors\Website;
use App\Models\Db\DbCommodityCard;
use think\Db;
use think\Model;
use think\Request;
use ForkModules\Traits\ResponseTrait;
use app\common\validate\Goods as GoodsValidate;
use think\Validate;
use hg\apidoc\annotation as Apidoc;
use tool\Logger;

/**
 * @title 商品
 * @description 接口说明
 */
class Goods extends Common
{
    protected $order_model;
    protected $user;
    protected $unionid;
    private $point_times = 10;//积分倍数

    private $car_vip = ['会员金卡', '会员金卡(VIP)', '会员金卡（VIP）', '会员银卡', '会员银卡VIP', '会员银卡（VIP）', '员工卡', '铂金卡', '黑卡'];//金银卡会员登记，用于积分兑换
    private $jk_goods = [2780, 2782, 2784, 2786, 2788, 2790];
    private $pk_goods = [2779, 2781, 2783, 2785, 2787, 2789];
    //口罩商品ID
    private $kz_goods = [3790];
    private $kz_dlr = "JSJY01";//口罩专营店

    private $jk_one_point_good;

    use ResponseTrait;

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->order_model = new BuOrder();

    }

    private function sj_ccs()
    {
        if (config('app_status') == "develop") {
            $ids = [2940, 2941];
        } else {
            $ids = [3610, 3611];
        }
        return $ids;
    }

    public function test_card_main()
    {
        $net_goods = new NetGoods();
        $card_id =  input('card_id');
        $res =  $net_goods->getGiftMainGoods($card_id,$this->user);
        print_json($res);
    }

    /**
     * @title 商品列表与搜索
     * @description 接口说明
     *
     * @param name:page type:int require:1 default:1 other: desc:页码
     * @param name:pageSize type:int require:0 default:20 desc:当前页显示数量,不传默认20
     * @param name:comm_type_id type:int require:0  desc:分类ID
     * @param name:new_comm_type_id type:int require:0  desc:新版分类ID（日产，启辰修改,pz继续使用comm_type_id）
     * @param name:car_id type:int require:0  desc:车型ID
     * @param name:search type:string require:0  desc:关键词 模糊搜索
     * @param name:commodity_ids type:int require:0  desc:商品ID，多个用英文,隔开
     * @param name:card_id type:int require:0  desc:卡券ID，通过卡券搜索关联商品
     * @param name:price_start type:int require:0  desc:最低价
     * @param name:price_end type:int require:0  desc:最高价
     * @param name:n_dis_id type:int require:0  desc:N件N折ID
     * @param name:full_cut_id type:int require:0  desc:满减ID
     * @param name:com_s_types' type:int require:0  desc:多个三级分类,隔开
     *
     * @return 200: 成功
     * @return data:返回数据@!
     * @data total:总条数 per_page:页条数 current_page:当前页数 list:商品列表字段名data@
     * @list commodity_name:商品名称 commodity_id:商品ID commodity_set_id:商品上架ID comm_type_id:商品分类ID car_series_id:车系ID,隔开多个 tag:标签 tag_name:标签中文 is_pure:是否纯正精品1是0否 cover_image:首图 final_price:最终售价 price:原价 sort_order:排序 card_id:包含啊卡券ID cheap_dis:套餐 group_dis:团购 full_dis:满减 limit_dis:限时购 n_dis:N件N折 pre_dis:预售 com_s_types:三级分类ID逗号隔开多个
     *
     * <AUTHOR>
     * @url /net-small/goods/list
     * @method GET
     *
     */
    public function goodsList(GoodsValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("list")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        if (!empty($requestData['commodity_ids'])) {
            $requestData['commodity_ids'] = urldecode($requestData['commodity_ids']);
        }

        $where     = [];
        $net_goods = new NetGoods();
        $res       = $net_goods->goodsList($requestData, $this->user, $this->channel_type, $where);

        if ($res['code'] <> 200) {
            if(isset($res['msg']['get_card_list'])){
                unset($res['msg']['get_card_list']);
            }
            if(isset($res['msg']['goods_card_rule'])){
                unset($res['msg']['goods_card_rule']);
            }

            return $this->setResponseError($res['msg'], $res['code'])->send();
        } else {
            if(isset($res['msg']['get_card_list'])){
                unset($res['msg']['get_card_list']);
            }
            if(isset($res['msg']['goods_card_rule'])){
                unset($res['msg']['goods_card_rule']);
            }

            return $this->setResponseData($res['msg'])->send();
        }
    }

    /**
     * @title 商品热销
     * @description 接口说明
     *
     * @param name:dlr_code type:int require:0 default:1 other: desc:店热销，不传显示热门关注
     * @param name:pageSize type:int require:0 default:1 other: desc:页数默认5
     *
     * @return 200: 成功
     * @return data:返回数据@!
     * @data commodity_id:商品id commodity_name:商品名称 cover_image:封面图 price:商品初始价格 final_price:商品最终价格 sale_num:销售数量
     *
     * <AUTHOR>
     * @url /net-small/goods/hot
     * @method GET
     *
     */
    public function goodsHot(GoodsValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("goodsHot")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $hot_key = 'goods_hot_sale_tmp' . json_encode($requestData);
        $res     = redis($hot_key);
        if (empty($res)) {
            $net_goods = new NetGoods();
            $res       = $net_goods->goodsSale($requestData);
            redis($hot_key, $res, mt_rand(100, 300));
        }

        if ($res['code'] <> 200) {
            return $this->setResponseError($res['msg'], $res['code'])->send();
        } else {
            return $this->setResponseData($res['msg'])->send();
        }
    }


    /**
     * @title 商品分类
     * @description 接口说明
     *
     * @return 200: 成功
     * @return data:返回数据@!
     * @data aname:一级分类 aid:一级分类ID b_type:二级分类列表@
     * @b_type twoname:二级分类 twoid:二级ID list:三级分类列表@
     * @list bname:三级分类 bid:三级分类ID img:三级分类图
     *
     * <AUTHOR>
     * @url /net-small/goods/class
     * @method GET
     *
     */
    public function goodsClass(GoodsValidate $validate)
    {
        $requestData  = $this->request->only(array_keys($validate->getRuleKey()));
        $channel_type = $this->channel_type;
        $key          = config('cache_prefix.catalog') . $channel_type;
        if (!empty($requestData['car_id'])) {
            $key = $key . $requestData['car_id'];
        }else{
            $requestData['car_id'] = $this->user['car_series_id'];
        }
//        $class_info = redis($key);
        $goods_ser  = new NetGoods();
//        $redis = \think\Cache::redisHandler();
//        if(!in_array($channel_type,['GWSM',"GWAPP","QCSM","QCAPP"])){
//            if (empty($class_info)) {
//                $class_info = $goods_ser->goodsClass($channel_type, 0, $requestData);
//                redis($key, $class_info, 864000);
//                $redis->sadd(config('cache_prefix.goods_class_set'), $key);
//            }
//        }else{
//            $class_info = false;
//            if (empty($class_info)) {
                $class_info = $goods_ser->newGoodsClass($channel_type, 0, $requestData);
//                redis($key, $class_info, 1*12*60*60);
//                $redis->sadd(config('cache_prefix.goods_class_set'), $key);
//            }
//        }
        return $this->setResponseData($class_info)->send();
    }


    /**
     * @title 商品详情
     * @description 接口说明
     *
     * @param name:goods_id type:int require:1 default:0 other: desc:商品ID即commodity_id
     * @param name:group_id type:int require:0 default:0 other: desc:团购ID
     * @param name:group_order_code type:int require:0 default:0 other: desc:团购订单编号从别人分享的团进来才有
     *
     * @return 200: 成功
     * @return data:返回数据@!
     * @data pre_sale:是否预售 hide_numer:是否隐藏购买数量0否1是 buy_word:购买按钮词 stop_time:购买截至时间 canot_buy:不能购买状态跟文案@ is_not_cart:不能放入购物车 can_use_card:是否能使用卡券 integral:积分 xn_kf:小能客服配置 c_service:客服id信息@ car_info:车主信息 json_data:sku_list等数据@ sp_list:所有当前所有规格@ suit:套装@ sku_list_group:团购时候sku group_order_code:当前团购号 group_info:团购信息@ sp_list_group:团购时所有当前所有规格 group_id:团购ID group_act_list:当前团购列表@ can_number:可以购买数量 sku_image:规格图片列表 goods:商品信息@ full_list:满减列表 n_dis_info:N件N折优惠信息 limit_info:限时优惠信息 g_info:团购信息 card_list:卡券列表@ shop_cart_count:购物车数量 comment:评论列表@ comment_count:商品点评数 user:用户信息@
     * @canot_buy status:状态0可以买1不能买 word:不能买的文案
     * @c_service customer_service_code:客服代码 customer_service_name:客服名称
     * @sp_list sp_id:规格组名ID sp_name:规格组名中文 sp_value_list:详细规格@
     * @sp_value_list sp_value_id:规格ID sp_value_name:规格中文
     * @json_data sku_list:当前sku以sp_list映射通过主键ID获得对应数组@
     * @group_act_list id:活动列表id
     * @goods commodity_id:商品ID commodity_name:商品名称 tyre_specification_code:轮胎规格（指列表编码） car_series_id:车系id（车系表ID） is_pure:是否纯正精品1是0否 is_integral:是否支持积分支付 comm_type_id:分类ID commodity_type:分类名 original_price_range_start:原价开始价,original_price_range_end:原价结束价,discount_price_range_star:现价开始价,discount_price_range_end:现价结束价 card_id:卡券ID is_mail:是否直邮，1是0否 factory_points:厂家积分 count_stock:总库存 commodity_attr:商品属性1热销2推荐3新品4促销 mail_price:运费 commodity_dlr_type_id:专营店分类ID point_discount:积分折扣比例为0不折扣,integral_price:积分价格 template_guid:运费模板ID detail_content:详情内容 sku_image:规格图片列表 unit:单位 cover_image:首图 commodity_class:商品类型1实物2卡券3虚拟 commodity_card_ids:商品电子卡券id逗号隔开 sale_num:销售数量 commodity_set_id:商品上架ID video:视频 is_mate:是否快递，1是0否 collected:是否收藏，1是0否 is_store:是否到店，1是0否 mail_method:1到店2快递3都有弹出用户选择 is_repeat:是否可以使用多张，1是0否 is_preview:是否预览，1是0否 home_page_index:首页索引 is_shopping_cart:是否支持购物车，1是0否 is_shop:是否商城商品，1是0否 commodity_code:商品编码 shelves_sources:上架来源，1平台自营2专营店3官微4活动 brands_id:品牌id favourable_introduction:优惠简称 favourable_detail:优惠详情 is_card_available:是否可领取卡券1是0否 user_car_series:用户车系信息 q_qz:前缀 q_hz:后缀 act_code:领取时可用
     * @suit suit_list:套装列表 suit_count:套装数量
     * @group_info id:拼团活动id title:拼团活动名称 start_time:开始时间 end_time:结束时间 people_number:参团人数 purchase_number:限购数量 buy_hour:参团时间(小时) rule:规则 commodity_id:商品id lowest_price:最低价
     * @sku_list price:价格 stock:库存 set_sku_id:上架id上架规格取这个 sku_id:规格ID image:图片 sp_value_arr:规格主键
     * @card_list id:卡券id set_type:应用类型：1平台端2专营店3集团 dlr_code:专营店编码 type:微信卡券1，商城卡券2 card_name:卡劵名称 card_id:微信卡劵id card_type:卡劵类型(1代金券2折购券3兑换券4满减券5优惠券) wx_card_type:微信优惠券类型 card_quota:卡劵额度（减免金额） card_discount:折扣，折扣券专用 least_type:最低消费类型，1金额2指定商品 least_cost:表示起用金额如果无起用门槛则填0 validity_date_start:固定日期区间专用，有效期开始 validity_date_end:固定日期区间专用，有效期结束 date_type:有效期类型：1表示固定日期区间，2表示固定时长 fixed_term:固定时长专用，领取后多少天内有效，单位为天 fixed_begin_term:固定时长专用，表示自领取后多少天开始生效，当天生效填写0，单位为天 receive_range:领取范围（1所有人2车主3指定用户4指定用户除外） count:数量 available_count:可用数量 default_detail:优惠说明字数上限300个汉字 use_des:使用须知，字数上限为1024个汉字 get_limit:每人可领券的数量限制不填写默认为1 apply_des:适用商品，展示于优惠券使用说明。长度在15个汉字或30个英文字母内 not_apply_des:不适用商品，展示于优惠券使用说明。长度在15个汉字或30个英文字母内 apply_dlr_code:适用门店 commodity_use_type:优惠券可用的商品范围，1全部商品2指定商品 act_name:活动名称 page_id:page_id is_enable:是否可用 created_date:创建时间 creator:创建人 last_updated_date:更新时间 modifier:修改人 color:颜色编码 icon_image:封面图 abstract:封面说明 brand_name:商户名称 code_type:code_type类型 center_title:卡劵顶部按钮名称 center_url:卡劵顶部按钮跳转URL custom_url_name:自定义跳转外链的入口名字 custom_url:自定义跳转的URL custom_url_sub_title:自定义跳转外链右侧提示语 promotion_url_name:营销场景自定义入口名字 promotion_url:营销场景跳转的URL promotion_url_sub_title:营销场景入口右侧提示语 off_line:是否线下使用 0 否 1是 act_status:1:未开始2:进行中3:已结束 up_down_channel_name:渠道名 up_down_channel_dlr: text NO 上架专营店编码 is_succeed:是否创券成功0否1是(第3步走完会出发更新为1) can_with:是否可与其他券共用 can_with_ids:可共同使用的优惠券id can_get_in_detail:是否可领取 0否1是 shelves_type:车生活:5
     * @comment  id:评价表id, order_code:订单编码 order_commodity_id:订单商品表主键 dlr_code:专营店编码 openid:openid, user_id:用户id commodity_id:商品编码 stars:星星1-5 images:图片url,隔开 content:评价内容 reply_content:回复呀 is_enable:是否可用 created_date:创建时间 creator:创建人 last_updated_date:更新时间 modifier:修改人 likes:点赞数 nickname:昵称 headimg:头像 comment_star:星星1-5 sku_info:sku信息,颜色等 price:实际价格 pic:商品图 commodity_name:商品名 is_comment:是否修改评价，0未修改1已修改
     * @user id:用户id phone:手机号 name:名称 car_series_id:车系id address_id:收货地址id openid:openid plat_id:平台ID bind_unionid:绑定unionid unionid:unionid member_id:member_id
     *
     * <AUTHOR>
     * @url /net-small/goods/detail
     * @method GET
     *
     */
    public function detail(GoodsValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("detail")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        //0423版本调兰俊接口
        $nvi_vin =  $requestData['nvi_vin'] ?? '';

//        $IRequestLogObj = new IRequestLog();
//        $httpurl = $_SERVER['HTTP_REFERER'] ?? '';
//        $IRequestLogObj->insertGetId(array('module'=>'net-small','action'=>'GOOD','function'=>'detail','data'=>'nvi_vin:'.$nvi_vin."==url:".$httpurl));

        if(!empty($nvi_vin)){
            $net_user = new NetUser();
            $net_user->bindUserCar($this->user, $nvi_vin);
        }



        $net_goods = new NetGoods();
        /////////////////////////////////////////

//        $r = $net_goods->getGroupCommodityPrice(4583,'TDBALSWZ52EXA--AA-');
//        return $this->setResponseData($r)->send();
        /////////////////////////////////////////
        //dd($this->user_vin);
        $res       = $net_goods->detail($requestData, $this->user, $this->channel_type,$this->brand);
        if($requestData['goods_id'] == 38512){
            $res['msg']['dlr_code'] = '';
            $res['msg']['dlr_name'] = '';
        }
        if ($res['code'] <> 200) {
            return $this->setResponseError($res['msg'], $res['code'])->send();
        } else {
            // 23-3-8活动
            if (!isset($requestData['get_dd_commodity_type']) && in_array($requestData['goods_id'], config('activity_cap.commodity_ids'))) {
                $check_draw = (new \app\common\net_service\Common())->isHaveDrawCap($requestData['goods_id'], $this->channel_type);
                if ($check_draw) {
                    $res['msg']['is_not_cart'] = 1;
                    $res['msg']['can_number']  = 1;
                    $res['msg']['hide_numer']  = 1;
                    $json_data                 = json_decode($res['msg']['json_data'], true);
                    foreach ($json_data['sku_list'] as &$v) {
                        $v['price'] = $v['limit_dis_price'] = $v['max_point_price'] = $v['max_point_limit_dis_price'] = '0.1';
                    }
                    $res['msg']['json_data'] = json_encode($json_data, true);

                    $res['msg']['goods']['discount_price_range_start'] = '0.1';
                    $res['msg']['goods']['discount_price_range_end']   = '0.1';
                }else{
                    $res['msg']['canot_buy']['status'] = 1;
                    $res['msg']['canot_buy']['word']   = '已售罄';
                    $res['msg']['is_not_cart']         = 1;
                    $res['msg']['can_number']          = 0;
                    $res['msg']['hide_numer']          = 1;
                }
            }

            // 新增：检查待支付订单占用卡券情况
            $unpaid_order_cards = $this->checkUnpaidOrderCards($requestData['goods_id']);
            $res['msg']['unpaid_order_cards'] = $unpaid_order_cards;

            return $this->setResponseHeaders(['gsecurity' => config('gsecurity')])->setResponseData($res['msg'])->send();
        }
    }



    /**
     * @title 优惠套装
     * @description 接口说明
     * @param name:goods_id type:int require:1 default:0 other: desc:商品ID
     *
     * @return 200: 成功
     * @return msg:提示信息
     * @return data:返回数据@!
     * @data list:列表包含规格等@ suit_id:套装ID suit_name:套装名称 mail_method:1到店2快递3都有
     * @list name:活动名 index_id:优惠套装主表id commodity_id:商品id sku:商品skuid price:优惠价格 cover_image:封面图 commodity_name:商品名称 sp_value_list:规格值id列表 commodity_set_id:上架id old_price:价格 stock:库存 count_stock:总库存 is_mail:是否快递，1是0否 is_store:是否门店自提，1是0否，默认是 limit_dis:限时优惠json活动信息 sp_name:规格 mail_method:1到店2快递3都有
     *
     * <AUTHOR>
     * @url /net-small/goods/suit
     * @method GET
     *
     */
    public function suit(GoodsValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("suit")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $net_goods = new NetGoods();
        $requestData['commodity_ids']  = $requestData['goods_id'];
        $suit_ids = $requestData['suit_ids']  = $requestData['suit_id']??'';
        $sku_ids = $requestData['sku_ids']??'';

        if($sku_ids && !$suit_ids){
            return $this->setResponseError('套餐ID必填', 401)->send();
        }
        $res       = $net_goods->suitList($requestData, $this->user, $this->channel_type,$this->brand);
        if ($res['code'] <> 200) {
            return $this->setResponseError($res['msg'], $res['code'])->send();
        } else {
            return $this->setResponseData($res['msg'])->send();
        }

    }
    /**
     * @title 优惠套装--修改之后实际详情
     * @description 接口说明
     * @param name:goods_id type:int require:1 default:0 other: desc:商品ID
     *
     * @return 200: 成功
     * @return msg:提示信息
     * @return data:返回数据@!
     * @data list:列表包含规格等@ suit_id:套装ID suit_name:套装名称 mail_method:1到店2快递3都有
     * @list name:活动名 index_id:优惠套装主表id commodity_id:商品id sku:商品skuid price:优惠价格 cover_image:封面图 commodity_name:商品名称 sp_value_list:规格值id列表 commodity_set_id:上架id old_price:价格 stock:库存 count_stock:总库存 is_mail:是否快递，1是0否 is_store:是否门店自提，1是0否，默认是 limit_dis:限时优惠json活动信息 sp_name:规格 mail_method:1到店2快递3都有
     *
     * <AUTHOR>
     * @url /net-small/goods/suit-price
     * @method GET
     *
     */
    public function suitPrice(GoodsValidate $validate){
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("suit_price")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $net_goods = new NetGoods();
        $requestData['commodity_ids']  = $requestData['goods_id'];
        $suit_ids = $requestData['suit_ids']  = $requestData['suit_id']??'';
        $sku_ids=$requestData['sku_ids'] = $requestData['sku_ids']??'';

        if(!$suit_ids){
            return $this->setResponseError('套餐ID必填', 401)->send();
        }
        $res       = $net_goods->suitList($requestData, $this->user, $this->channel_type,$this->brand);
        if ($res['code'] <> 200) {
            return $this->setResponseError($res['msg'], $res['code'])->send();
        } else {
            if(isset($res['msg']['list'][0])){
                $re_list = $res['msg']['list'][0];
                return $this->setResponseData(['list'=>$re_list])->send();
            }else{
                return $this->setResponseData([])->send();

            }

        }

    }

    /**
     * 套装说明描述
     * @param GoodsValidate $validate
     * @return \think\Response
     */
    public function suitDetail(GoodsValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("suit_detail")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $suit_index_model =  new BuCheapSuitIndex();
        $suit_index =  $suit_index_model->getOne(['where'=>['id'=>$requestData['suit_id']],'filed'=>'name,detail_content,id']);
        return $this->setResponseData($suit_index)->send();

    }
    /**
     * @title 通过商品ID拉取商品规格列表
     * @description 接口说明
     * @param name:goods_id type:int require:1 default:0 other: desc:商品ID
     * @param name:suit_id type:int require:1 default:0 other: desc:套装ID
     *
     * @return 200: 成功
     * @return msg:提示信息
     * @return data:返回数据@!
     * @data json_data:sku数据@ sp_list:所有当前所有规格@ goods:商品信息@
     * @json_data price:默认价格 stock:库存 limit_dis_price:限时折扣sku价格 sku_id:sku_id image:图片 sp_value_arr:规格值id列表
     * @sp_list sp_name:规格名称 sp_id:规格id sp_value_list:规格值列表@
     * @sp_value_list sp_value_id:规格值id sp_value_name:规格值名称
     * @goods id:商品ID commodity_name:商品名称 comm_type_id:分类ID commodity_type:分类名 cover_image:首图 detail_content:详情内容 is_pure:是否纯正精品1是0否 is_integral:是否支持积分支付 original_price_range_start:原价开始价,original_price_range_end:原价结束价,discount_price_range_star:现价开始价,discount_price_range_end:现价结束价 card_id:卡券ID is_mail:是否直邮 count_stock:总库存 commodity_attr:商品属性1热销2推荐3新品4促销 mail_price:运费 is_store:自取 favourable_introduction:优惠简称 favourable_detail:优惠详情 point_discount:积分折扣比例为0不折扣,integral_price:积分价格 template_guid:运费模板ID sku_image:规格图片列表 unit:单位 commodity_class:商品类型1实物2卡券3虚拟 commodity_card_ids:点子卡券ID类型==3时 sale_num:销售数量 commodity_set_id:商品上架ID video:视频  is_store:是否到店1是
     *
     * <AUTHOR>
     * @url /net-small/goods/change-suit
     * @method GET
     *
     */
    public function changeSuit(GoodsValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("change_suit")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $com   = new NetGoods();
        $id    = $requestData['goods_id'];
        $s_id    = $requestData['sub_goods_id']??0;
        $suit  = $requestData['suit_id'] ?? 0;
        $use_discount  = $requestData['use_discount'] ?? 1;
        $dd_dlr_code  = $requestData['dd_dlr_code'] ?? '';
        $cart_id  = $requestData['cart_id'] ?? '';
        $kilometer   = $requestData['kilometer'] ?? ''; //公里数

        $entry_from = $requestData['entry_from'] ?? 1;
        $nvi_vin = $requestData['nvi_vin'] ?? 1;
        $gift_card_id = $requestData['gift_card_id'] ?? '';
        $act_type_id = $requestData['act_type_id'] ?? 1; // 1限时折扣2团购3满减4全积分折扣5套装6N件N折7预售8立减9臻享服务包10秒杀
        $car_config_code = $requestData['car_config_code'] ?? '';

        $dlr_model =  new DbDlr();
        $dlr =  $dlr_model->getOne(['where'=>['dlr_code'=>$dd_dlr_code]]);

        if (!$dd_dlr_code) {
            $f_b_info = $com->getFriendBaseInfo($this->user);
            $dlr_code =$dd_dlr_code = $f_b_info['dlr_code'];
            $dlr_name = $f_b_info['dlr_name'];
            if (!$dlr_code) {
                $dlr_code = '';
            }
        }
        if($cart_id){
            $cart_model = new BuShoppingCart();
            $cart =  $cart_model->getOneByPk($cart_id);
            if($cart){
                $this->user['car_series_id'] = $cart['car_series_id'];
            }
        }
        //买赠券
        $set_sku_id_arr = [];
        if($gift_card_id){
            $goods_card_model =  new \app\common\model\db\DbCommodityCard();
            $goods_card_list =  $goods_card_model->getList(['where'=>['card_id'=>$gift_card_id,'commodity_id'=>$id]]);
            if($goods_card_list){
                $goods_card_info =  $goods_card_list[0];
                if($goods_card_info['set_sku_ids']){
                    $set_sku_id_arr = explode(',',$goods_card_info['set_sku_ids']);
                }
            }
        }

        $goods = $com->getCommodityInfo($id, $this->channel_type, '', '', '', 2, $suit,$this->user,$dd_dlr_code,$s_id,$kilometer, 0, 0, $use_discount, $entry_from, $act_type_id,0,$nvi_vin,$set_sku_id_arr,0,0,$car_config_code);
//        dd(json_encode($goods['commodity_data']['count_stock']));
        if ($goods) {
            if ($suit) {
                $sku_list = $goods['cheap_sku_list'];
                $sp_list  = $goods['cheap_sp_list'];

            } else {
                $sku_list = $goods['sku_list'];
                $sp_list  = $goods['sp_list'];

            }
            $goods_data = $goods['commodity_data'];
            unset($goods_data['group_commodity_ids_info']);
            $goods_data['dlr_code'] = $dd_dlr_code;
            $goods_data['dlr_name'] = $dlr?$dlr['dlr_name']:'';
            //mail_method 1到店2快递3都有
            if ($goods_data['is_mail'] == 1 && $goods_data['is_store'] == 1) {
                $goods_data['mail_method'] = 3;
            } elseif ($goods_data['is_mail'] == 1 && $goods_data['is_store'] == 0) {
                $goods_data['mail_method'] = 2;//到店
            } else {
                $goods_data['mail_method'] = 1;//快递
            }
            if($goods_data['mail_type']){
                $goods_data['mail_method'] = $goods_data['mail_type'];
            }
            $gift_info = [];
            if(isset($goods_data['gift_id']) && $goods_data['gift_id'] > 0){
                $gift = $com->_gift($goods_data['commodity_id'],$goods_data['gift_id'],$this->user_id,$this->channel_type);
                unset($gift['gift_info']['gift_imgs_arr']);
                $gift_info = $gift['gift_info'];
            }
            $goods_data['gift_info'] = $gift_info;
//            if ($goods_data['car_series_id']) {
//                $car_series = explode(',', $goods_data['car_series_id']);
//                if ($this->user) {
//                    if (!in_array($this->user['car_series_id'], $car_series)) {
//                        $goods_data['is_mate'] = 0;
//                    } else {
//                        $goods_data['is_mate'] = 1;
//                    }
//                } else {
//                    $goods_data['is_mate'] = 0;
//                }
//            }

            $sku_arr = array();
            if ($sku_list) {
                //处理排序
                foreach ($sku_list as $k => $v) {
                    $k_arr = explode(',', $k);
                    sort($k_arr);
                    $k_s           = implode(',', $k_arr);
                    if($cart_id){
                        $v['card_can_get']=0;
                    }
                    $sku_arr[$k_s] = $v;
                }
                $sku_list = $sku_arr;
            }
//            var_dump($sku_list);
            //处理规则只有一行的
            if (count($sp_list) == 1) {
                foreach ($sp_list as $k => $v) {
                    if ($sp_list[$k]['sp_value_list']) {
                        foreach ($sp_list[$k]['sp_value_list'] as $kk => $vv) {
                            if ($sku_list[$vv['sp_value_id']]['stock'] < 1) {
                                $sp_dis = "";
                                unset($sp_list[$k]['sp_value_list'][$kk]);
                            } else {
                                $sp_dis = "active";
                            }
                        }
                        $sp_list[$k]['sp_value_list'] = array_values($sp_list[$k]['sp_value_list']);
                        if(!$sp_list[$k]['sp_value_list']){
                            unset($sp_list[$k]);//102叫改的
                        }
                    }
                }
            }else{
                foreach ($sp_list as $k => $v) {
                    $sp_list[$k]['sp_value_list'] = array_values($v['sp_value_list']);
                }
            }
            $sp_list   = array_merge($sp_list, array());

            // 23-3-8活动
            $check_draw = (new \app\common\net_service\Common())->isHaveDrawCap($requestData['goods_id'], $this->channel_type);
            if ($check_draw) {
                foreach ($sku_list as &$v) {
                    $v['price'] = $v['limit_dis_price'] = $v['max_point_price'] = $v['max_point_limit_dis_price'] = '0.1';
                }
            }
            $json_data = json_encode_cn(array('sku_list' => $sku_list));
            return $this->setResponseData(['json_data' => $json_data, 'sp_list' => $sp_list, 'goods' => $goods_data])->send();
        }

        return $this->setResponseError("该商品数据异常")->send();
    }


    /**
     * @title 快递查询
     * @description 接口说明
     * @param name:order_id type:int require:0 default:0 other: desc:订单ID
     *
     * @return 200: 成功
     * @return msg:提示信息
     * @return data:返回数据@!
     * @data status:状态 com:承运公司 number:承运编码 phone:承运电话 list:状态明细@
     * @list status:状态 time:时间
     *
     * <AUTHOR>
     * @url /net-small/goods/waybill
     * @method GET
     *
     */
    public function waybill(GoodsValidate $validate)
    {
        // // 0：快递收件(揽件)1.在途中 2.正在派件 3.已签收 4.派送失败 5.疑难件 6.退件签收
//        $b_stat      = array(1 => '在途中', 2 => '派件中', 3 => '已签收', 4 => '派件失败', 0 => '快递收件(揽件)', 5 => '疑难件', 6 => '退件签收');
        $b_stat = BuOrderCommodity::$delivery_status_array;

//        {
//            "time": "2024-04-21 15:24:44",
//                "status": "【孝感市】 您的快递已送货上门，签收人为【门卫】。如有疑问请联系业务员：13733475986，代理点电话：17362663485，投诉电话：18140613207。感谢使用中通快递，期待再次为您服务！"
//            },
//        {
//            "code":"1000",
// "requestId":"1674890810629",
// "message":"成功",
// "data":{
//            "orderTraceList":[
//   {
//       "operateCode":"-640",
//    "operateRemark":"京东快递 已收取快件",
//    "operateTitle":"配送员完成揽收",
//    "operateTime":"2023-04-27 10:39:23",
//    "waybillNo":"JDVA12345678",
//    "operateName":"高凡",
//    "operateAddress":"北京"
//   }
//  ],
//  "relationWaybillInfo":{
//                "readdressWaybillNos":"JDVA1234567890",
//   "reverseWaybillNos":"JDVA1234567890，JDVA1234567891"
//  }
// }
//}
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("waybill")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $id     = $requestData['order_id'];//订单ID
        $afs_id = $requestData['afs_id'] ?? '';//售后单ID



        $afs_data = '';
        if (!empty($afs_id)) {
            $afs_data = (new DbAfterSaleOrders())->getOneByPk($afs_id);
            if ($afs_data['order_id'] != $id) {
                return $this->setResponseError('售后单数据错误')->send();
            }
        }
//        $redis_key = 'waybill_'.$id.'_'.$afs_id.'_order';
//        $array = redis($redis_key);//缓存1个小时
//        if(!$array){
            $order_commodity = new BuOrderCommodity();
            $order = $order_commodity->alias('a')
                ->join('t_bu_order b','a.order_code = b.order_code')
                ->join('t_db_jd_warehouse jd_ware ','jd_ware.id =a.jd_warehouse_send_id','left')
                ->join('t_db_systemvalue d','a.common_carrier = d.value_code','left')
                ->where(['b.id'=>$id,'b.user_id'=>$this->user_id,'a.mo_id'=>0])
                ->field('a.waybill_number,a.common_carrier,d.county_name,d.remark,a.third_order_id,a.jd_warehouse_send_id,jd_ware.third_waybill_number,jd_ware.third_common_carrier')
                ->group('a.waybill_number')
                ->order('a.delivery_time desc,a.waybill_number desc')
                ->select();
            if (empty($order)) {
                return $this->setResponseError('用户信息错误')->send();
            }

            $order_info = (new BuOrder())->getOne(['where' => ['id' => $id]]);
            $array = [];
            if (!empty($afs_data)) {
                $way_info        = json_decode($afs_data['platform_waybill_info'], true);
                $way_bill_number = $way_info['number'] ?? '';
                $redis_way_key = "waybill_".$way_bill_number;
                $way_bill = redis($redis_way_key);//缓存物流信息1个小时
                $value_type=13;
//                $way_bill=[];
                if(!$way_bill){
                    if($afs_data['jd_warehouse_send_id']){
                        $way_bill = JdCloudWarehouse::create('jd_cloud_warehouse')->commonQueryOrderTrace($afs_data['jd_warehouse_send_id']);
                        $value_type=29;
                    }else{
                        $way_bill        = getExpressInfo($way_info['number'] ?? '', $way_info['company'] ?? '', $order_info['phone'] ?? '');

                    }
                    redis($redis_way_key,json_encode($way_bill),1*60*60);
                }else{
                    $way_bill = json_decode($way_bill,true);
                }
                if (!$way_bill) {
                    $way_bill['list'] = [];
                } else {
                    if ($way_bill['status'] != 0) {
                        $way_bill['list'] = [];
                    } else {
                        $way_bill              = $way_bill['result'];
                        $way_bill['red_class'] = '';
                        $way_bill['on_class']  = '';
                        if ($way_bill['deliverystatus'] != 3) {
                            $way_bill['red_class'] = "f-red";
                            $way_bill['on_class']  = 'on';
                        }
                        if(isset($way_bill['result']['status_name'])){
                            $way_bill['status'] =$way_bill['result']['status_name'];
                        }else{
                            $way_bill['status'] = $b_stat[$way_bill['deliverystatus']];
                        }
                        $sys_model          = new DbSystemValue();
                        $way_com            = $sys_model->getOne(['where' => [ 'value_code' => $order[0]['common_carrier']]]);
                        $way_bill['com']    = $way_com['county_name'];
                        $way_bill['phone']  = $way_com['remark'];
                    }
                }
                $way_bill['number'] = $way_bill_number;

                $sys_model           = new DbSystemValue();
                $params = [
                    'where' => [
                        'value_code' => $order[0]['common_carrier']
                    ],
                    'field' => 'county_name,remark'
                ];
                $cy     = $sys_model->getOne($params);
                $way_bill['carrier_name'] = $cy['county_name'];
                $array[] = $way_bill;
            }else{
                foreach ($order as $key=>$value){
                    if(!empty($value['waybill_number'])){
                        $redis_way_key = "waybill_".$value['waybill_number'];
                        $way_bill = redis($redis_way_key);//缓存物流信息1个小时
                        $way_bill =[];
                        if(!$way_bill){
//                            && $value['jd_warehouse_send_id']
                            if($value['common_carrier'] == 'CYS0000010'){
                                $way_bill = JdCloudWarehouse::create('jd_cloud_warehouse')->commonQueryOrderTrace($value['jd_warehouse_send_id']);
                                $value['county_name'] = '京东';//在接口里面返回
                                $way_bill['third_waybill_number'] = $value['third_waybill_number'];

//                                if($value['third_waybill_number']){
//                                    $third_way_bill =  getExpressInfo($value['third_waybill_number'] ?? '',  '', $order_info['phone'] ?? '', $value['third_order_id']);
//                                    $way_bill['result']['third_list'] =$third_way_bill;
//                                    if($third_way_bill['status']==0){
//                                        $old_list = array_merge($third_way_bill['result']['list'],$way_bill['result']['list']);
//                                        usort($old_list, function($a, $b) {
//                                            $dateA = new \DateTime($a['time']);
//                                            $dateB = new \DateTime($b['time']);
//                                            // 返回一个整数，小于 0 表示 $a 应该排在 $b 前面，大于 0 表示 $a 应该排在 $b 后面
//                                            return $dateB->getTimestamp() - $dateA->getTimestamp();
//                                        });
////                                        usort($old_list,'time');
//                                        $way_bill['result']['list'] = $old_list;
//                                    }
//                                }
                            }else{
                                if($value['jd_warehouse_send_id']){
                                    $value['common_carrier']='';
                                }
                                $way_bill        = getExpressInfo($value['waybill_number'] ?? '', $value['common_carrier'] ?? '', $order_info['phone'] ?? '', $value['third_order_id']);

                            }
                            redis($redis_way_key,json_encode($way_bill),1*60*60);
                        }else{
                            $way_bill = json_decode($way_bill,true);
                        }
//                        $way_bill = getExpressInfo($value['waybill_number'], $value['common_carrier']);
                        if (!$way_bill) {
                            $array[$key]['list'] = [];
                        }else{
                            if ($way_bill['status'] != 0) {
                                $array[$key] = $way_bill;
                                $array[$key]['list'] = [];
                            }else{
                                $array[$key]              = $way_bill['result'];
                                $array[$key]['red_class'] = '';
                                $array[$key]['on_class']  = '';
                                if ($way_bill['result']['deliverystatus'] != 3) {
                                    $way_bill['red_class'] = "f-red";
                                    $way_bill['on_class']  = 'on';
                                }
                                if(isset($way_bill['result']['status_name'])){
                                    $array[$key]['status'] =$way_bill['result']['status_name'];
                                }else{
                                    $array[$key]['status'] = $b_stat[$way_bill['result']['deliverystatus']];
                                }
                            }
                        }
                        $array[$key]['com']    = $value['county_name'];
                        $array[$key]['expName']    = $value['county_name'];//为了适配云仓，这个也从后台获取
                        $array[$key]['phone']  = $value['remark'];
                        $array[$key]['number'] = $value['waybill_number'];
                    }else{
                        $array[$key]['status'] = 201;
                        $array[$key]['list'] = [];
                    }
                    $array[$key]['goods_img'] = $order_commodity->waybillCommodityImg($id,$value['waybill_number']);//获取订单商品首图
                }
            }
//            redis($redis_key, json_encode($array), 3600);
//        }else{
//            $array = json_decode($array,true);
//        }


        return $this->setResponseHeaders(['gsecurity' => config('gsecurity')])->setResponseData($array)->send();
    }

    /**
     * @title 运费查询
     * @description 接口说明
     * @param name:goods_id type:int require:0 default:0 other: desc:商品ID
     * @param name:address type:string require:0 default:0 other: desc:省
     *
     * @return 200:成功
     * @return data:返回数据正常运费
     *
     * <AUTHOR>
     * @url /net-small/goods/mail-price
     * @method GET
     *
     */
    public function goodsMailPrice(GoodsValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("mail_price")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $net_goods = new NetGoods();
        $res       = $net_goods->goodsMailPrice($requestData, $this->channel_type);
        if ($res['code'] <> 200) {
            return $this->setResponseError($res['msg'], $res['code'])->send();
        } else {
            return $this->setResponseData(['message' => '成功获取运费', 'mail_price' => $res['msg']])->send();
        }
    }

    /**
     * @title 获取sku信息
     * @description 接口说明
     * @param name:goods_id type:int require:0 default:1 other: desc:商品ID
     * @param name:sku_key type:int require:0 default:1 other: desc:商品sku_key
     * @param name:is_group type:int require:0 default:0 other: desc:是否团购1是0否
     * @param name:is_suit type:int require:0 default:0 other: desc:是否团购套餐1是
     *
     * @return 200:成功
     * @return data:返回数
     *
     * <AUTHOR>
     * @url /net-small/goods/sku-info
     * @method GET
     *
     */
    public function goodsSkuInfo(GoodsValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("sku_info")->check($requestData);
        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $id        = $requestData['goods_id'];
        $sku_key   = $requestData['sku_key'];
        $is_group  = $requestData['is_group'] ?? 0;
        $is_suit   = $requestData['is_suit'] ?? 0;
        $json_data = redis('net-api-goods-sku-json-is_suit' . $is_suit . $id . $this->channel_type);//在详情页就输出记录了
        if (!$json_data) {
            return $this->setResponseError("sku不存在")->send();
        }
        $data = json_decode($json_data, true);
        if ($is_group) {
            $sku_list = $data['sku_list_group'];

        } else {
            $sku_list = $data['sku_list'];
        }
        $sku_info = $sku_list[$sku_key];
        return $this->setResponseData($sku_info)->send();
    }

    /**
     * @title 保存收藏
     * @description 接口说明
     * @param name:goods_id type:int require:1 default:0 other: desc:商品ID
     *
     * @return 200:成功
     * @return data:返回数
     *
     * <AUTHOR>
     * @url /net-small/goods/collection
     * @method POST
     *
     */
    public function saveCollection(GoodsValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("save_collection")->check($requestData);
        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $id = $requestData['goods_id'];
        if (!$id) {
            return $this->setResponseError('收藏失败,请联系客服')->send();
        }

        if (!$this->user_id) {
            return $this->setResponseError('请登录!', 401)->send();
        }
        $hit_type_code = $requestData['hit_type_code'] ?? '';
        $source     = $this->source;
        $coll_model = new BuUserCollection();
//        $dlr_arr = DbDlr::channel_to_arr($this->channel_type)['dlr_code'];
        $where      = array('user_id' => $this->user_id, 'commodity_id' => $id, 'brand' => $this->brand);
        $c_res      = $coll_model->getOne(['where' => $where]);
        $car_info   = $this->_getCarer($this->user['bind_unionid'], $this->user['member_id']);
        Logger::error('goods-coll-changet',['sql'=>$coll_model->getLastSql(),'r'=>$c_res]);
        if (!$c_res) {
            $data             = array(
                'openid'        => $this->unionid,
                'user_id'       => $this->user_id,
                'commodity_id'  => $id,
                'dlr_code'      => $this->channel_type,
                'source'        => $source,
                'vin'           => isset($car_info['vin']) ? $car_info['vin'] : '',
                'license_plate' => isset($car_info['car_no']) ? $car_info['car_no'] : '',
                'name'          => isset($car_info['name']) ? $car_info['name'] : '',
                'phone'         => isset($car_info['mobile']) ? $car_info['mobile'] : '',
                'car_series_id' => $this->user['car_series_id'],
                'brand' => $this->brand,
                'hit_type_code'=>$hit_type_code,
            );
            $data['modifier']='qd_-'.rand(1000,9999);
            $res              = $coll_model->insertData($data);
            $transaction_type = 1;
            $channel_code = DbDlr::channel_to_code($this->channel_type);
            // 业务逻辑改为首次收藏发放成长值，取消收藏不扣除
            $this->behavior('GrowthValueBehavior', [
                'value_point_code' => config('growth_code.collection'),
                'member_id'        => $this->memberid,
                'user_id'          => $this->user_id,
                'bind_unionid'     => $this->user['bind_unionid'],
                'channel_code'     => $channel_code, #渠道3微信小程序 tmp, 后面追加app
                'transaction_type' => $transaction_type,
                'event_type'       => 'collection',
                'event_value'      => $id
            ]);
        } else {
            if ($c_res['is_enable'] == 1) {
                $data             = array(
                    'is_enable'         => 0,
                    'last_updated_date' => date('Y-m-d H:i:s'),
                );
                $transaction_type = 2;
            } else {
                $data             = array(
                    'is_enable'         => 1,
                    'last_updated_date' => date('Y-m-d H:i:s'),
                );
                $transaction_type = 1;
            }
            $where = array(
                'user_id'      => $this->user_id,
                'commodity_id' => $id,
            );
            $data['modifier']='qd_-'.rand(1000,9999);

            $res = $coll_model->saveData($data, $where);
        }
        if ($res  || $res===0) {
            return $this->setResponseData('ok')->send();
        } else {
            return $this->setResponseError("收藏失败")->send();
        }
    }

    /**
     * @title 车型是否匹配商品
     * @description 接口说明
     * @param name:goods_id type:int require:1 default:0 other: desc:商品ID
     * @param name:car_series_id type:int require:1 default:0 other: desc:车型ID
     *
     * @return 200:成功
     * @return data:返回数@!
     * @data is_mate:是否匹配(0不匹配1匹配)
     *
     * <AUTHOR>
     * @url /net-small/goods/mate
     * @method GET
     *
     */
    public function goodsCarMate(GoodsValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("goodsCarMate")->check($requestData);
        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $goods_id   = $requestData['goods_id'];
        $car_s_id   = $requestData['car_series_id'];
        $flat_model = new DbCommodityFlat();
        $where      = ['commodity_id' => $goods_id];
        $where[]    = ['exp', " (find_in_set('{$this->channel_type}',up_down_channel_dlr)) "];
        $goods_flat = $flat_model->getOne(['where' => $where, 'field' => "commodity_id,car_series_id"]);
        if (!$goods_flat) {
            return $this->setResponseError("商品异常")->send();
        }
        $is_mate = 1;
        if (!empty($goods_flat['relate_car_ids'])) {
            $car_s_arr = explode(",", $goods_flat['relate_car_ids']);
            if(empty($car_s_id)){
                $is_mate = 2;
            }elseif(!in_array($car_s_id, $car_s_arr)){
                $is_mate = 0;
            }

        }
        return $this->setResponseData(['is_mate' => $is_mate])->send();
    }


    //N件N折折扣信息 通过Nid获取商品列表
    public function getNDisCountNid($n_id)
    {
        $n_dis_goods_model = new DbNDiscountCommodity();
        $time              = date('Y-m-d H:i:s');
        $where             = [
            'b.start_time' => ['<=', $time], 'b.end_time' => ['>=', $time], 'b.is_enable' => 1, 'a.is_enable' => 1, "b.id" => $n_id
        ];
        $param['where']    = $where;
        $param['group']    = "b.id";
        $param['field']    = "GROUP_CONCAT(a.commodity_id SEPARATOR ',') g_ids";
        $row               = $n_dis_goods_model->getNdisInfo($param);
//        echo $n_dis_goods_model->getLastSql();
        return $row;
    }

    /**
     * @title 小I客服
     * @description 接口说明
     *
     * @return 200:成功
     * @return data:返回数@!
     * @data c_service:客服H5链接
     *
     * <AUTHOR>
     * @url /net-small/goods/xi-kf
     * @method GET
     *
     */
    public function XiKf()
    {
        if (!$this->user_id) {
            return $this->setResponseError("需要登录", 401)->send();
        }
        $net_goods = new NetGoods();
        $row       = $net_goods->XiKf($this->user, $this->channel_type);
        return $this->setResponseData(['c_service' => $row['msg']['h5']])->send();
    }



    /**
     * @title 小I客服小程序版本，首页--
     * @description 接口说明
     *
     * @return 200:成功
     * @return data:返回数@!
     * @data c_service:客服H5链接
     *
     * <AUTHOR>
     * @url /net-small/goods/xi-kf-sm
     * @method GET
     *
     */
    public function XiKfSm()
    {
//        if (!$this->user_id) {
//            return $this->setResponseError("需要登录", 401)->send();
//        }
        $net_goods = new NetGoods();
        $row       = $net_goods->XiKf($this->user, $this->channel_type);
        return $this->setResponseData(['c_service' => $row['msg']['sm'],'h5' => $row['msg']['h5']])->send();
    }



    /**
     * 获取价格区间
     * */
    public function getSection()
    {
        $where['is_enable']    = 1;
        $where[] = ['exp', " (find_in_set('{$this->channel_type}',channels)) "];
        $model = new DbSection();
        $params = [
            'field' => 'section_name,section_price_start,section_price_end',
            'where' => $where,
            'order' => 'sort_order desc',
        ];
        $key  = config('cache_prefix.goods_section') . $this->channel_type;
        $data = redis($key);
        if ($data){
            $list = $data;
        }else {
            $list = $model->getList($params);
            redis($key, $list, mt_rand(3600, 7200));
            $redis = \think\Cache::redisHandler();
            $redis->sadd(config('cache_prefix.goods_section'), $key);
        }
        return $this->setResponseData($list)->send();
    }

    /**
     * 获取商品工时价格信息
     * */
    public function getWiPrice(GoodsValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("getWiPrice")->check($requestData);
        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $dlr_code        = $requestData['dlr_code'];
        $car_config_code = $requestData['car_18n'];
        $sku_ids         = explode(',', $requestData['sku_ids']);
        $sku_num         = explode(',', $requestData['sku_num']);
        $sku_num_by_id   = [];
        foreach ($sku_ids as $k => $v){
            $sku_num_by_id[$v] = $sku_num[$k];
        }

        $net_goods = new NetGoods();
        $return    = $net_goods->getWiPriceAll($sku_ids, $sku_num_by_id, $car_config_code, $dlr_code);

        return $this->setResponseData($return)->send();
    }

    public function getGiftList(GoodsValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("giftList")->check($requestData);
        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $goods_id = $requestData['goods_id'];
        $gift_id = $requestData['activity_id'];
        $dd_dlr_code  = $requestData['dd_dlr_code'] ?? '';
        $gift_model = new DbGift();
        $gift_list = $gift_model->getGroupInfo(['where'=>['a.id'=>$gift_id],'field'=>'a.*']);
        $data = [];
        if(empty($gift_list)){
            return $this->setResponseError('非法活动')->send();
        }
        $data['id'] = $gift_id;
        $data['des'] = $gift_list['des'];
        $data['optional_number'] = $gift_list['optional_number'];
        $data['mail_method'] = 1;
        $NetGoods = new NetGoods();
//        $redis_gift = 'cache_goods_gift_'.$goods_id.$this->user['car_18n'];
//        $net_goods_list         = redis($redis_gift);
//        $net_goods_list = true;
//        if(empty($net_goods_list)){
            $net_goods_list = $NetGoods->detail(['goods_id'=>$goods_id],$this->user,$this->channel_type);
            $net_goods_list = $net_goods_list['msg'];
//            redis($redis_gift,$net_goods_list,5*60);
//        }
        if ($net_goods_list['goods']['is_mail'] == 1 && $net_goods_list['goods']['is_store'] == 1) {
            $data['mail_method'] = 3;
        } elseif ($net_goods_list['goods']['is_mail'] == 1 && $net_goods_list['goods']['is_store'] == 0) {
            $data['mail_method'] = 2; //快递
        } else {
            $data['mail_method'] = 1; //到店
        }
        if(!empty($dd_dlr_code)){
            $dlr_model =  new DbDlr();
            $dlr =  $dlr_model->getOne(['where'=>['dlr_code'=>$dd_dlr_code]]);
            $data['dlr_code'] = $dd_dlr_code;
            $data['dlr_name'] = $dlr['dlr_name'];
        }else{
            $data['dlr_code'] = $net_goods_list['dlr_code'];
            $data['dlr_name'] = $net_goods_list['dlr_name'];
        }
        $user_info = $NetGoods->getFriendBaseInfo($this->user);
        $car_type_name = isset($user_info['car_type_name']) ? $user_info['car_type_name'] : '';
        if($car_type_name === '  '){
            $car_type_name = '';
        }
        $data['user_car_series'] = $car_type_name;
        $goods_list = [];
        $p_where['b.gift_id'] = $gift_id;
        $p_where['b.is_gift'] = 1;
        $gift_info_img = $gift_model->getLiveSeckillCommodityList(['where' => $p_where, 'field' => "b.commodity_id",'group'=>'b.commodity_id']);
        $spec_model = new DbSpecValue();
        $redis_key = 'cache_goods_gift_';
        foreach ($gift_info_img as $key=>$value){
            $redis = $redis_key.$gift_id.$value['commodity_id'].$this->user['car_18n'];
            $goods         = redis($redis);
            $goods = false;
            if(empty($goods)){
                $goods = $NetGoods->detail(['goods_id'=>$value['commodity_id'],'is_gift'=>1,'gift_id'=>$gift_id], $this->user,$this->channel_type);
                redis($redis,$goods,5*3600);
            }

            if($goods['code'] == 200){
                $goods_data = $goods['msg'];
                $sp_name = '';
                $sp_value_list = '';
                $de_sku_stock = 0;
                if(!empty($goods_data)){
                    $array_data = json_decode($goods_data['json_data'],true);
                    foreach ($array_data['sku_list'] as $val){
                        if($goods_data['goods']['de_sku_id'] == $val['sku_id']){
                            $de_sku_stock = $val['stock'];
                            $sp_value_list = implode(',',$val['sp_value_arr']);
                            break;
                        }
                    }
                    if(!empty($sp_value_list)){
                        $spec = $spec_model->where(['id'=>array('in',$sp_value_list)])->field('sp_value_name')->column('sp_value_name');
                        $sp_name = implode(',',$spec);
                    }
                }
                $goods_list[] = [
                    'commodity_name' =>$goods_data['goods']['commodity_name'],
                    'commodity_id' =>$goods_data['goods']['commodity_id'],
                    'commodity_set_id' =>$goods_data['goods']['commodity_set_id'],
                    'cover_image' => $goods_data['goods']['cover_image'],
                    'commodity_class' => $goods_data['goods']['commodity_class'],
                    'de_sku_id' => $goods_data['goods']['de_sku_id'],
                    'mail_method' =>$goods_data['goods']['mail_method'],
                    'is_mate' => $goods_data['goods']['is_mate'],
                    'count_stock' => $goods_data['goods']['count_stock'],
                    'de_sku_stock' => $de_sku_stock,
                    'json_data' => $goods_data['json_data'],
                    'sp_list' => $goods_data['sp_list'],
                    'groups_data' =>$goods_data['goods']['groups_data'],
                    'sp_value_list' => $sp_value_list,
                    'sp_name' => $sp_name,
                    'user_car_series' => $car_type_name,
                ];
            }

        }
        $gift_key = array_column($goods_list,'is_mate');
        array_multisort($gift_key,SORT_DESC,$goods_list);
        $data['goods_list'] = $goods_list;

        return $this->setResponseData($data)->send();
    }


    /**
     * 获取秒杀商品付款用户
     * @param GoodsValidate $validate
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getSeckillUser(GoodsValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("seckill_user")->check($requestData);
        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $data = [];
        // 根据商品id获取秒杀id,在订单商品表里获取订单号，获取用户手机号
        $seckill_commodity_model = new DbSeckillCommodity();
        $map = ['commodity_id'=>$requestData['goods_id'], 'is_enable'=>1];
        $seckill_id = $seckill_commodity_model->where($map)->value('seckill_id');
        if (empty($seckill_id)) {
            return $this->setResponseData($data)->send();
        }
        // 判断秒杀类型
        $seckill_model = new DbSeckill();
        $seckill_info = $seckill_model->where('id', $seckill_id)->field('title,seckill_type,day_end_time')->find();
        // 查询订单
        $order_model = new BuOrder();
        $map = ['b.seckill_id'=>$seckill_id,'a.is_enable'=>1,'a.order_status'=>['not in', [1,3,8]], 'commodity_id'=>$requestData['goods_id']];

        // 循环场次
        if ($seckill_info['seckill_type'] == 2) {
            // 判断当前时间是否在秒杀活动时间内
            $time = date('H:i:s');

            if ($time > $seckill_info['day_end_time']) {
                $map['date(a.created_date)'] = date('Y-m-d', strtotime('+1 day'));
            } else {
                $map['date(a.created_date)'] = date('Y-m-d');
            }
        }
        $field = 'a.order_code,a.phone';
        $order_list = $order_model->alias('a')
            ->join('t_bu_order_commodity b', 'a.order_code=b.order_code', 'left')
            ->where($map)
            ->field($field)
            ->select();
        foreach ($order_list as $item) {
            $data[] = [
                'copy_writing' => substr_replace($item['phone'], '****', '3', '4').'已下单',
                'order_code' => $item['order_code']
            ];
        }
        return $this->setResponseData($data)->send();
    }

    /**
     * 规格对应详情
     * */
    public function skuDetailContent(GoodsValidate $validate){
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("sku_detail")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $commodity_id = $requestData['commodity_id'];
        $sku_id       = $requestData['sku_id'];
        $return_data  = (new NetGoods())->getSkuDetailContent($commodity_id, $sku_id);
        return $this->setResponseData($return_data)->send();
    }

    /**
     * 通过商品ID+价格计算前端应该显示多少积分
     * net-small/goods/point-js
     * @param GoodsValidate $validate
     * @return \think\Response
     */
    public function point_js(GoodsValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("point_js")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $net_goods =  new NetGoods();
        $point_arr =  $net_goods->goods_point_js(['commodity_id' => $requestData['goods_id'],'price'=>$requestData['price'],'channel_type'=>$this->channel_type]);
        return $this->setResponseData($point_arr)->send();

    }


    /* 最佳活动卡券优惠
    * @return void
    */
    public function bestActivityCard(GoodsValidate $validate){
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("best_activity_card")->check($requestData);
        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $net_goods = new NetGoods();
        $res = $net_goods->bestCardJs($requestData,$this->user,$this->channel_type);

        return $this->setResponseData(['estimate_price'=>$res['msg']['af_price'],"have_card"=>$res['msg']['have_card'],"have_member_price"=>$res['msg']['have_member_price']])->send();
//
//
//        $set_sku_ids = $requestData['set_sku_id'];
//        $old_price = $requestData['old_price'];
//        $activity_price = $requestData['activity_price'] ?? 0;
//        $member_price = $requestData['member_price'] ?? 0;
//        $activity_id = $requestData['activity_id'] ?? 0;
//        $activity_type = $requestData['activity_type'] ?? 0;
//        $commodity_id = $requestData['commodity_id'] ?? 0;
//        $count = $requestData['count'] ?? 1;
//
//        $best_card_price = 0;
//        //TODO 判断         canGetCards
//
//        $DbCommoditySetSkuObj = new DbCommoditySetSku();
//        //判断是否为组合商品
//        $dbCommodityObj = new DbCommodity();
//        $commodity_info = $dbCommodityObj->where(['id'=>$commodity_id,'is_enable'=>1])->find();
//        if(empty($commodity_info))   return $this->setResponseError("没有找到该商品")->send();
//        //组合商品
//        $buCardReceiveRecordObj = new BuCardReceiveRecord();
//        $delete_card_arr = [];
//        $delete_card_list = $buCardReceiveRecordObj->where(["user_id"=>$this->user_id,'status'=>['>',2]])->select();
//        foreach($delete_card_list as $delete_card_item){
//            $delete_card_arr[] = $delete_card_item['card_id'];
//        }
//
//        if($commodity_info['is_grouped'] == 1){
//            $dbCommodityObj = new \app\common\model\db\DbCommodityCard();
//            $have_card_list = $dbCommodityObj->alias("cc")->join("t_db_card card","cc.card_id=card.id")
//                                             ->where(['cc.is_enable'=>1,'card.is_enable'=>1])
//                                             ->whereIn('cc.commodity_id',$commodity_id)
//                                             ->field("card.*")->select();
//        }else{
//            $have_card_list = $DbCommoditySetSkuObj->alias("a")
//                                                   ->join("t_db_commodity_card cc","cc.commodity_id=a.commodity_id and a.commodity_set_id=cc.commodity_set_id")
//                                                   ->join("t_db_card card","cc.card_id=card.id")
//                                                   ->where(['cc.is_enable'=>1,"card.is_enable"=>1])
//                                                   ->whereIn('a.id',$set_sku_ids)
//                                                   ->group("cc.card_id")->field("card.*")->select();
//
//        }
//
//        if(!empty($delete_card_arr)){
//            foreach($have_card_list as $k=>$have_card_item){
//                if(in_array($have_card_item['id'],$delete_card_arr)){
//                    unset($have_card_list[$k]);
//                }
//            }
//        }
//
//        //可用券的最大优惠金额
//        $best_card_price_info = $this->getBestCard($have_card_list,$old_price,$best_card_price);
//        $best_card_price = $best_card_price_info['best_card_price'];
//
//        if(!empty($activity_id)){
//            $now_date = date("Y-m-d H:i:s");
//            //限时活动
//            if($activity_type == 1 || $activity_type == 8){
//                $limitObj = new DbLimitDiscount();
//                $activity_info = $limitObj->where(['id'=>$activity_id])->where("start_time <= '{$now_date}' and end_time >='{$now_date}'")->find();
//            }
//            if($activity_type == 3){
//                $fullObj = new DbFullDiscount();
//                $activity_info = $fullObj->where(['id'=>$activity_id])->where("start_time <= '{$now_date}' and end_time >='{$now_date}'")->find();
//                $full_sum_price = round(($old_price * $count) , 2);
//                $full_discount_rules_list = json_decode($activity_info['full_discount_rules'],true);
//                foreach($full_discount_rules_list as $full_discount_rules_item){
//                    if($full_sum_price >= $full_discount_rules_item[0]){
//                        $activity_price = round($old_price - $full_discount_rules_item[1],2);
//                        break;
//                    }
//                }
//            }
//            if($activity_type == 6){
//                $ndisObj = new DbNDiscount();
//                $activity_info = $ndisObj->where(['id'=>$activity_id])->where("start_time <= '{$now_date}' and end_time >='{$now_date}'")->find();
//
//            }
//            if($activity_type == 10){
//                $sekillObj = new DbSeckill();
//                $activity_info = $sekillObj->where(['id'=>$activity_id])->where("start_time <= '{$now_date}' and end_time >='{$now_date}'")->find();
//            }
//            if(empty($activity_info)){
//                $activity_id = 0;
//            }
//        }
//
//        //存在活动
//        if(!empty($activity_id)){
//            //有卡券有活动 如果是没有卡券只有活动那就取活动价就完事
//            if(!empty($have_card_list)){
//                if($activity_info['card_available'] == 0 ){
////                        //取活动价与上面的卡券比哪个更优惠
////                        $best_price = $this->getBestCard($have_card_list,$old_price);
////                        if($activity_price <  $best_price) $activity_price = $activity_price;
//                }else{//可叠加券
//                    if(!empty($activity_info['rel_card_ids'])){//指定卡券
//                        $card_ids = [];
//                        foreach($have_card_list as $have_card_item){
//                            $card_ids[] = $have_card_item['id'];
//                        }
//
//                        $rel_card_ids_arr = explode(',',$activity_info['rel_card_ids']);
//                        $add_card_ids = array_intersect($rel_card_ids_arr,$card_ids);
//
//                        //没有交集就比较活动与券就可以了
//                        if(!empty($add_card_ids)){
//                            //活动 30 块
//                            //活动的 卡券 1 2 3
//                            //上面的卡卡券 1 4 5
//                            //最终取  30 + 卡券 1 与上面的卡券比哪个更优惠
//                            $dbCardObj = new DbCard();
//                            $card_list = $dbCardObj->whereIn('id',$add_card_ids)->select();
//                            $sum_card_price = $this->getAllCardDis($card_list,$old_price,$best_card_price);
////                            var_dump($best_card_price);echo "\n";
////                            var_dump($sum_card_price);echo "===";
////                            dd($activity_price);
//                            $activity_price = $activity_price - $sum_card_price ;
//                        }
//
//                    }
//                }
//
//            }
//        }
//        $x = $activity_price;
//        //有会员价
//        if(!empty($member_price)){
//            //有活动
//            if(!empty($activity_id)){
//                $a = $old_price - $best_card_price ;
//                $b = $activity_price ;
//                $c = $member_price - $best_card_price;
//                if($a < $b && $a < $c){
//                    $x = $a;
//                }
//                if($b < $a && $b < $c){
//                    $x = $b;
//                }
//                if($c <= $b && $c <= $a){
//                    $x = $c;
//                }
//            }else{//无活动
//                $a = $old_price - $best_card_price ;
//                $b = $member_price - $best_card_price;
//                if($a < $b){
//                    $x = $a;
//                }else{
//                    $x = $b;
//                }
//            }
//        }else{//无会员价
//            //有活动
//            if(!empty($activity_id)){
//                 $a = $old_price - $best_card_price ;
//                 $b = $activity_price ;
//                 if($a < $b){
//                     $x = $a;
//                 }else{
//                     $x = $b;
//                 }
//            }else{ //无会员无活动
//                $x = $old_price - $best_card_price;
//            }
//        }
//        $have_card = empty($have_card_list) ? 0 : 1;
//        $x = $x > 0 ? $x : 0;
//        return $this->setResponseData(['estimate_price'=>sprintf("%.2f", $x),"have_card"=>$have_card])->send();
    }

    public function getGiftCard(GoodsValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("gift_card")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $card_id =  $requestData['card_id'];
        $net_goods = new NetGoods();
        $res       = $net_goods->getGiftGoods($card_id, $this->user, $this->channel_type);

        return $this->setResponseData($res)->send();

    }

    /**
     * 返回次数
     * @param GoodsValidate $validate
     * @return \think\Response|void
     */
    public function getGiftCardNum(GoodsValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("gift_card_num")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $sku_json =  $requestData['sku_json'];
        $net_goods = new NetGoods();
        $res       = $net_goods->checkGiftCard($sku_json, $this->user,[], $this->channel_type);
        $data = $res;
        return $this->setResponseData($data)->send();

    }

    /**
     * goods/check_gift_card
     * 返回次数--通过SKU查询主品
     * @param GoodsValidate $validate
     * @return \think\Response|void
     */
    public function checkGiftCard(GoodsValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("check_gift_card")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $sku_id =  $requestData['sku_id'];
        $net_goods = new NetGoods();
        $res       = $net_goods->checkGiftGoods($sku_id, $this->user);
        $data = $res;
        return $this->setResponseData($data)->send();

    }

    //找出最大的券值
    private function getBestCard($have_card_list,$old_price,$best_card_price){
        //找出共用券
        $best_card_ids = [];
        foreach($have_card_list as $have_card_item){
            if($have_card_item['can_with'] == 1){
                if($have_card_item['card_type'] == 1){
                    $new_best_card_price = $have_card_item['card_quota'];
                }elseif($have_card_item['card_type'] == 2){
                    $new_best_card_price = round(($old_price * (10 - $have_card_item['card_discount'])) / 10,2);
                }
                $best_card_price = $best_card_price + $new_best_card_price;
                $best_card_ids[] = $have_card_item['id'];
            }
        }

        foreach($have_card_list as $have_card_item){
            if($have_card_item['card_type'] == 1){
                $new_best_card_price = $have_card_item['card_quota'];
            }elseif($have_card_item['card_type'] == 2){
                $new_best_card_price = round(($old_price * (10 - $have_card_item['card_discount'])) / 10,2);
            }
            if($best_card_price > $new_best_card_price){
                $best_card_price = $best_card_price;
            }else{
                $best_card_price = $new_best_card_price;
                $best_card_ids[] = $have_card_item['id'];
            }

        }

        return ['best_card_price'=>$best_card_price,'best_card_ids'=>$best_card_ids];
    }


    private function getAllCardDis($have_card_list,$old_price,$best_card_price){
        if(empty($have_card_list)){
            $sum_card_price = $old_price;
        }else{
            $sum_card_price = 0;
            foreach($have_card_list as $have_card_item){
                if($have_card_item['card_type'] == 1){
                    $new_best_card_price = $have_card_item['card_quota'];
                }elseif($have_card_item['card_type'] == 2){
                    $new_best_card_price = round(($old_price * (10 - $have_card_item['card_discount'])) / 10,2);
                }
                $sum_card_price = $sum_card_price + $new_best_card_price;
            }
            $sum_card_price = $best_card_price > $sum_card_price ? $best_card_price : $sum_card_price;
        }
        return $sum_card_price;
    }

    /**
     * @title 检查VIN与SKU编码是否匹配
     * @description 根据车架号和公里数检查是否可以销售指定的SKU编码产品
     * @url /net-small/goods/check-vin-sku
     * @method POST
     *
     * @param string vin 车架号（必填，17位）
     * @param string sku_code 商品SKU编码（必填，多个用逗号分隔）
     * @param int kilometer 车辆公里数
     *
     * @return int 1-匹配 0-不匹配
     * <AUTHOR>
     * @date 2023-06-01
     */
    public function checkVinSku(GoodsValidate $validate)
    {
        try {
            // 获取请求参数并验证
            $requestData = $this->request->only(array_keys($validate->getRuleKey()));
            $result = $validate->scene("check_vin_sku")->check($requestData);

            // 校验失败，返回异常
            if (empty($result)) {
                return $this->setResponseError($validate->getError())->send();
            }

            // 获取参数
            $vin = $this->user['vin'];
            $sku_code = $requestData['sku_code'];
            $kilometer = isset($requestData['kilometer']) ? intval($requestData['kilometer']) : 0;

            // 将sku_code转为数组
            $sku_code_array = explode(',', $sku_code);

            // 调用服务方法检查VIN与SKU编码是否匹配
            $net_goods = new NetGoods();
            $result = $net_goods->vinGetSkuCode($vin, $sku_code_array, $kilometer);

            // 返回结果
            return $this->setResponseData($result)->send();
        } catch (\Exception $e) {
            // 记录异常日志
            Logger::error('checkVinSku异常: ' . $e->getMessage(), [
                'vin' => $vin ?? '',
                'sku_code' => $sku_code ?? '',
                'kilometer' => $kilometer ?? 0,
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);

            return $this->setResponseError('系统异常，请稍后再试')->send();
        }
    }



    /**
     * 检查商品的待支付订单占用卡券情况
     * @param int $goods_id 商品ID
     * @return array
     */
    private function checkUnpaidOrderCards($goods_id)
    {
        if (empty($goods_id)) {
            return [];
        }

        $order_model = new BuOrder();
        $card_r_model = new BuCardReceiveRecord();

        // 查询该商品的待支付订单
        $unpaid_orders = $order_model->alias('a')
            ->join('t_bu_order_commodity b', 'a.order_code = b.order_code')
            ->where([
                'a.user_id' => $this->user_id,
                'a.order_status' => 1, // 待支付状态
                'b.commodity_id' => $goods_id,
                'a.is_enable' => 1
            ])
            ->field('a.id, a.order_code, a.order_status, a.created_date, a.total_price')
            ->select();

        if (empty($unpaid_orders)) {
            return [];
        }

        $result = [];
        foreach ($unpaid_orders as $order) {
            // 检查该订单是否占用了卡券
            $used_cards = $card_r_model->where([
                'consume_order_code' => $order['order_code'],
                'user_id' => $this->user_id,
                'status' => ['in', [5, 6]] // 已冻结或已使用
            ])->select();

            if (!empty($used_cards)) {
                $order_info = [
                    'order_id' => $order['id'],
                    'order_code' => $order['order_code'],
                    'order_status' => $order['order_status'],
                    'created_date' => $order['created_date'],
                    'total_price' => $order['total_price'],
                    'used_cards' => []
                ];

                foreach ($used_cards as $card) {
                    $order_info['used_cards'][] = [
                        'card_id' => $card['card_id'],
                        'card_name' => $card['card_name'] ?? '',
                        'status' => $card['status']
                    ];
                }

                $result[] = $order_info;
            }
        }

        return $result;
    }



}
