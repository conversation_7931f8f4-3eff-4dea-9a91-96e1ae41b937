{extend name="public:base_layout" /}

{block name="content"/}
<div class="panel-body">
    <div class="alert alert-info fade in m-b-15">
        <h4>操作提示</h4>
        排序显示规则为排序小的在前，新增的在前
    </div>
    <div class="margin-bottom-65">
        <div class="col-md-2 p-l-0">
            <a class="btn btn-success btn-sm pull-left" data-toggle="modal" data-target="#add_modal"> <i class="fa fa-lg fa-plus"></i> 新增数据 </a>

            <a class="btn btn-success btn-sm pull-left" style="margin-left: 20px" href="{$go_more_url}">  售后凭证设置 </a>
        </div>
        <div class="col-md-10 p-r-0 text-right">
            <form class="form search-form" action="{:url('index')}" method="get">
                <label>
                    <input type="text" name="msg" class="form-control input-sm element" value="{$query.msg}" placeholder="请输入退款原因" aria-controls="data-table" >
                </label>
                <button type="submit" class=" btn btn-sm btn-success"><i class="fa fa-search"></i>搜索</button>
            </form>
        </div>
    </div>
    <div class="row">
        <div class="col-sm-12">
            <table  class="table table-hover table-bordered" >
                <thead>
                <tr class="active">
                    <th>
                        序号
                    </th>
                    <th >
                        原因
                    </th>
                    <th >
                        排序
                    </th>
                    <th >
                        是否可用
                    </th>
                    <th class="text-center">
                        操作
                    </th>
                </tr>
                </thead>
                <tbody>
                <?php foreach ($list as $key=>$vo):?>
                    <tr id="{$vo.id}" class="gradeA odd" role="row">
                        <td class="sorting_1" tabindex="0"><?php echo  get_number($key);?></td>
                        <td>{$vo.msg}</td>
                        <td>{$vo.sort}</td>
                        {if condition="$vo.is_enable==1"}
                        <td>可用</td>
                        {else/}
                        <td>不可用</td>
                        {/if}
                        <td class="text-center width-200 ">
                            <a data-id="{$vo.id}" class="btn updateModal btn-primary btn-sm m-r-5 m-t-0"><i class="fa fa-edit"></i>编辑</a>
                            <a data-id="{$vo.id}" class="btn deleteModal btn-danger btn-sm m-r-5 m-t-0"><i class="fa fa-edit"></i>删除</a>
                        </td>
                    </tr>
                <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
    <div class="row">
        <div class="col-sm-5">
            <div class="dataTables_info" id="data-table_info" role="status" aria-live="polite">
                共查询到 {$list->total()} 条数据
            </div>
        </div>
        <div class="col-sm-7">
            <div class="dataTables_paginate paging_simple_numbers" id="data-table_paginate">
                {$page}
            </div>
        </div>
    </div>
</div>
<!-- BEGIN 遮罩层-->
<!--新增-->
<div class="modal fade" id="add_modal" >
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                <h4 class="modal-title">新增退款原因</h4>
            </div>
            <form class="form" id="add_form" data-parsley-trigger="change">
                <div class="modal-body">
                    <div id="add_error_container" class="alert alert-danger m-b-0" style="display: none">
                        <h4><i class="fa fa-info-circle"></i> 操作失败</h4>
                        <p></p>
                    </div>
                    <label>
                        原因<i class="m-r-3 text-danger">*</i>:
                    </label>
                    <input  type="text" class="form-control" name="msg" value="" placeholder="请输入原因" data-parsley-required="true" maxlength="30">
                    <p></p>
                    <label>
                        排序<i class="m-r-3 text-danger">*</i>:
                    </label>
                    <input  type="text" class="form-control" name="sort" value="" placeholder="请输入排序" data-parsley-required="true" data-parsley-min="0" data-parsley-pattern-message="格式不正确,请输入不小于0的数值" data-parsley-pattern="/^(\d)*$/">
                    <p></p>
                    <label>
                        状态<i class="m-r-3 text-danger">*</i>:
                    </label>
                    <div>
                        <input class="switch" type="checkbox" data-input-name="is_enable" data-input-true="1" data-input-false="0"  data-size="small" data-on-text="可用" data-off-text="不可用" checked />
                    </div>
                    <input class="hidden" name="action" value="add">
                </div>
                <div class="modal-footer">
                    <a href="javascript:;" class="btn btn-sm btn-white" data-dismiss="modal">取消</a>
                    <a href="javascript:;" class="btn btn-sm btn-primary" id="add_submit">确定</a>
                </div>
            </form>
        </div>
    </div>
</div>
<!-- END 遮罩层-->

<!-- BEGIN 编辑-->
<div class="modal fade" id="update_modal" >
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                <h4 class="modal-title">编辑退款原因</h4>
            </div>
            <form class="form" id="update_form" data-parsley-trigger="change">
                <div class="modal-body">
                    <div id="add_error_container" class="alert alert-danger m-b-0" style="display: none">
                        <h4><i class="fa fa-info-circle"></i> 操作失败</h4>
                        <p></p>
                    </div>
                    <input class="form-control hidden" type="text" name="id" placeholder="" readonly>
                    <label>
                        原因<i class="m-r-3 text-danger">*</i>:
                    </label>
                    <input  type="text" class="form-control" name="msg" value="" placeholder="请输入原因" data-parsley-required="true" maxlength="30">
                    <p></p>
                    <label>
                        排序<i class="m-r-3 text-danger">*</i>:
                    </label>
                    <input  type="text" class="form-control" name="sort" value="" placeholder="请输入排序" data-parsley-required="true" data-parsley-min="0" data-parsley-pattern-message="格式不正确,请输入不小于0的数值" data-parsley-pattern="/^(\d)*$/">
                    <p></p>
                    <label>
                        状态<i class="m-r-3 text-danger">*</i>:
                    </label>
                    <div>
                        <input class="switch" type="checkbox" data-input-name="is_enable" data-input-true="1" data-input-false="0"  data-size="small" data-on-text="可用" data-off-text="不可用" checked />
                    </div>
                    <input class="hidden" name="action" value="update">
                </div>
                <div class="modal-footer">
                    <a href="javascript:;" class="btn btn-sm btn-white" data-dismiss="modal">取消</a>
                    <a href="javascript:;" class="btn btn-sm btn-primary" id="update_submit">确定</a>
                </div>
            </form>
        </div>
    </div>
</div>
<!-- END 编辑-->

<!-- BEGIN 删除-->
<div class="modal fade" id="del_modal" >
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                <h4 class="modal-title">操作提醒</h4>
            </div>
            <div class="modal-body">
                <div id="del_error_container" class="alert alert-danger m-b-0">
                    <h4><i class="fa fa-info-circle"></i> 删除操作</h4>
                    <p>您确定要删除该退款原因吗？</p>
                </div>
            </div>
            <div class="modal-footer">
                <form id="del_form" method="post" >
                    <input type="text" name="id" value="" class="hidden">
                    <a  class="btn btn-sm btn-white" data-dismiss="modal">取消</a>
                    <a  class="btn btn-sm btn-primary" type="button" id="delete_submit">确定</a>
                </form>
            </div>
        </div>
    </div>
</div>
<!-- END 删除-->
{/block}
{block name="script"}
<script>
    $("#add_submit").on("click",function () {
        var validate=$("#add_form").psly().validate();  //表单验证
        if(!validate){
            console.log(validate);
            return false;
        }
        var alert_obj = $("#add_form").find(".alert-danger");
        var data = $("#add_form").serialize();
        console.log(data);
        Custom.ajaxPost("{:url('save')}",data,alert_obj);
    })

    $("a.updateModal").on("click",function () {
        var id = $(this).attr("data-id");
        $.getJSON("{:url('getOne')}",{id:id},function (resData) {
            if(resData.error == 0){
                var data = resData.data;
                $("#update_form").find("input[name='id']").val(id);
                $("#update_form").find("input[name='msg']").val(data.msg);
                $("#update_form").find("input[name='sort']").val(data.sort);
                if(data.is_enable==1){
                    $("#update_form").find(".switch").bootstrapSwitch("state",true);
                }else {
                    $("#update_form").find(".switch").bootstrapSwitch("state",false);
                }
                $("#update_modal").modal('show');
            }else {

            }
        })
    })

    $("a.deleteModal").on("click",function () {
        var id = $(this).attr("data-id");
        $("#del_form").find("input[name='id']").val(id);
        $("#del_modal").modal("show");
    })

    $("#delete_submit").on("click",function () {
        var alert_obj = $("#del_form").find(".alert-danger");
        var data = $("#del_form").serialize();
        Custom.ajaxPost("{:url('delete')}",data,alert_obj);
    })
    $("#update_submit").on("click",function () {
        var validate=$("#update_form").psly().validate();  //表单验证
        if(!validate){
            console.log(validate);
            return false;
        }
        var alert_obj = $("#update_form").find(".alert-danger");
        var data = $("#update_form").serialize();
        Custom.ajaxPost("{:url('save')}",data,alert_obj);
    })
</script>
{/block}
