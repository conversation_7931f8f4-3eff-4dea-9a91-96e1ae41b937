<?php

namespace app\admin_v2\controller;

use app\admin_v2\validate\CardValidate;
use app\common\model\db\DbCard;
use app\common\model\db\DbCommodity;
use app\common\model\db\DbCommodityType;
use app\common\model\db\DbExports;
use app\common\model\sys\SysMenu;
use app\common\port\connectors\Pz1a;
use app\common\service\ActivityCardService;
use think\Env;
use think\Model;
use think\Queue;
use think\Validate;

class ToBApi extends Common
{


    /**
     * 跳转到前端项目页面
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function index()
    {
        $m_model = new SysMenu();
        $list    = $m_model->alias('a')
            ->join('t_sys_menu b', 'a.menu_pid = b.id')
            ->where(['a.controller' => request()->controller(), 'a.left_view' => 1, 'b.left_view' => 1])
            ->field('a.web_menu_url,b.menu_url')
            ->find();
        if (!empty($list['web_menu_url'])) {
            $username['username']  = $this->admin_info['username'];
            $username['timestamp'] = time();
            $token                 = http_build_query($username);
            $this->redirect(Env::get('TOB_URL') . '/' . $list['web_menu_url'] . '?sign=' . base64_encode($token));
        }
    }


    // 卡券列表
    public function refund_reason_list()
    {
        $id = input('id', 0);
        $model = new \app\common\model\db\DbOrderRefundReason();
        $fields = 'id,msg,sort,is_enable,is_required,commodity_class,proof_description,max_proof_images,created_date,modified_date';
        if ($id) {
            $data = $model->field($fields)->where(['id' => $id, 'is_enable' => 1])->find();
            print_json(0, 'success', $data);
        } else {
            $list = $model->field($fields)->where(['is_enable' => 1])->order('sort asc, id desc')->select();
            print_json(0, 'success', $list);
        }
    }

    // 退款原因保存/更新
    public function refund_reason_save()
    {
        $id = input('post.id', 0);
       
        $is_required = input('post.is_required', 0);
        $commodity_class = input('post.commodity_class', '');
        $proof_description = input('post.proof_description', '');
        $max_proof_images = input('post.max_proof_images', '');
        
        $model = new \app\common\model\db\DbOrderRefundReason();
        $data = [
            'is_required' => $is_required,
            'commodity_class' => $commodity_class,
            'proof_description' => $proof_description,
            'max_proof_images' => $max_proof_images,
            'modified_date' => date('Y-m-d H:i:s'),
        ];
        if ($id) {
            $res = $model->where(['id' => $id])->update($data);
        } else {
            $data['created_date'] = date('Y-m-d H:i:s');
            $res = $model->insert($data);
        }
        if ($res !== false) {
            print_json(0, '保存成功');
        } else {
            print_json(1, '保存失败');
        }
    }

    // 商品范围列表
    public function commodity_scope_list()
    {
        $list = [
            ['label' => '实物直邮', 'value' => 1],
            ['label' => '实物到店', 'value' => 199],
            ['label' => '虚拟商品', 'value' => 2],
            ['label' => '电子卡券', 'value' => 3],
            ['label' => '启辰充电桩', 'value' => 7],
            ['label' => '日产充电桩', 'value' => 8],
        ];
        print_json(0, 'success', $list);
    }

}
