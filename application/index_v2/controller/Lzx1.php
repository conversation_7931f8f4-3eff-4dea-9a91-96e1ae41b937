<?php /** @noinspection ForgottenDebugOutputInspection */

/**
 * Created by PhpStorm.
 * User: lzx
 * Date: 2017/6/8
 * Time: 下午5:31
 */

namespace app\index_v2\controller;

use api\jd_sdk\JdOrderN;
use api\lianyou\Ccs;
use api\wechat\AccessToken;
use api\wechat\Carer;
use api\wechat\CarerNew;
use api\wechat\Clue;
use api\wechat\CustomMessage;
use api\wechat\JsSdk;
use api\wechat\Card;
use api\wechat\Mail;
use api\wechat\Menu;
use api\wechat\QrCode;
use api\wechat\Ticket;
use api\wechat\UserManage;
use app\common\fuli_service\UserOrder;
use app\common\model\act\AcRenewalQrc;
use app\common\model\bu\BuCardReceiveRecord;
use app\common\model\bu\BuComponentAuth;
use app\common\model\bu\BuOrder;
use app\common\model\bu\BuOrderCommodity;
use app\common\model\bu\BuOrderErrList;
use app\common\model\bu\BuOrderJd;
use app\common\model\bu\BuOrderMoreCardPoint;
use app\common\model\bu\BuOrderPoint;
use app\common\model\bu\BuOrderRefund;
use app\common\model\bu\BuOrderSettlement;
use app\common\model\bu\BuPhoneBlacklist;
use app\common\model\bu\BuQyDepartmentAll;
use app\common\model\bu\BuQyUserAll;
use app\common\model\bu\BuTplMsg;
use app\common\model\bu\BuUserBrowsing;
use app\common\model\bu\BuVinListHhr;
use app\common\model\bu\BuWxContrastOpenid;
use app\common\model\db\DbActivityCenterLog;
use app\common\model\db\DbAdvertisement;
use app\common\model\db\DbAfterSaleOrders;
use app\common\model\db\DbAfterSalePlatformAddresses;
use app\common\model\db\DbArea;
use app\common\model\db\DbCard;
use app\common\model\db\DbCarSeries;
use app\common\model\db\DbCarUser;
use app\common\model\db\DbCommodity;
use app\common\model\db\DbCommodityFlat;
use app\common\model\db\DbCommodityJdSku;
use app\common\model\db\DbCommoditySet;
use app\common\model\db\DbCommoditySetSku;
use app\common\model\db\DbCommoditySku;
use app\common\model\db\DbConsumeRuleRecord;
use app\common\model\db\DbDlr;
use app\common\model\db\DbJdGoodsImage;
use app\common\model\db\DbJdSkuInfo;
use app\common\model\db\DbLog;
use app\common\model\db\DbLySettleOrder;
use app\common\model\db\DbSeckill;
use app\common\model\db\DbSpecialSm;
use app\common\model\db\DbSystemValue;
use app\common\model\db\DbUser;
use app\common\model\db\DbUserDrawRecord;
use app\common\model\db\DbVinRule;
use app\common\model\e3s\E3sMaintenanceProduct;
use app\common\model\e3s\E3sMaintenanceProductCarSeries;
use app\common\model\e3s\E3sPackage;
use app\common\model\e3s\E3sPartCarSeries;
use app\common\model\e3s\E3sSpecificRelationPart;
use app\common\model\jd\JdMessage;
use app\common\model\jd\JdOrder;
use app\common\model\jd\JdOrderGoods;
use app\common\model\pss\PssInventoryInout;
use app\common\model\sys\SysLogModule;
use app\common\model\zz\ZzLog;
use app\common\net_service\Common;
use app\common\net_service\Inventory;
use app\common\net_service\MaintainService;
use app\common\net_service\NetOrder;
use app\common\net_service\PaymentSystem;
use app\common\port\connectors\Idm;
use app\common\port\connectors\MarketBase as MB;
use app\common\port\connectors\Member;
use app\common\port\connectors\Payment;
use app\common\port\connectors\PzPoint;
use app\common\port\connectors\QuickWin;
use app\common\service\CarIndexService;
use app\common\service\CommodityService;
use app\net_small\command\OrderSettle;
use app\common\net_service\NetGoods;
use app\common\net_service\NetGoodsOptimized;
use think\Cache;
use think\cache\driver\Redis;
use think\Config;
use think\Controller;
use think\Db;
use think\Exception;
use think\Env;
use think\Hook;
use think\Log;
use think\Queue;
use think\Model;
use tool\Curl;
use tool\Logger;
use tool\OssUploadFile;
use function GuzzleHttp\Psr7\build_query;

class Lzx1 extends Controller
{
    public function __construct()
    {
        parent::__construct();
        $user = input('user_token');
        $data = input('user_data', '');
        if ($user != 'lzx123' || $data <> date('md')) {
            die('未经授权，不允许访问');
        }
    }

    function getCombinations($array, $length)
    {
        $result = [];
        $comb = array_fill(0, $length, 0);
        $count = count($array);
        $i = 0;

        while ($i >= 0) {
            if ($comb[$i] < $count) {
                $i++;
                if ($i < $length) {
                    $comb[$i] = $comb[$i - 1] + 1;
                } else {
                    $resultSet = [];
                    foreach ($comb as $index) {
                        $resultSet[] = $array[$index - 1];
                    }
                    $result[] = $resultSet;
                    $i--;
                    $comb[$i]++;
                }
            } else {
                $i--;
                if ($i >= 0) {
                    $comb[$i]++;
                }
            }
        }

        return $result;
    }

    function shuffleAnswers($answers)
    {
        $shuffled = $answers;
        shuffle($shuffled);
        return $shuffled;
    }

    function generateOutput($combinations, $questions, $answers)
    {
        $output = [];
        foreach ($combinations as $combination) {
            $row = [];
            $row['encodings'] = $combination;
            $row['questions'] = array_map(function ($index) use ($questions) {
                return $questions[$index - 1];
            }, $combination);

            $row['originalAnswers'] = array_map(function ($index) use ($answers) {
                return $answers[$index - 1];
            }, $combination);

            $row['shuffledAnswers'] = $this->shuffleAnswers($row['originalAnswers']);

            $order = '';
            foreach ($row['shuffledAnswers'] as $shuffledAnswer) {
                $order .= array_search($shuffledAnswer, $row['originalAnswers']) + 1;
            }

            $row['order'] = $order;
            $output[] = $row;
        }
        return $output;
    }
    public function test_sj()
    {
        $encodings = range(1, 16);
        $questions = [
            'Question1',
            'Question2',
            'Question3',
            'Question4',
            'Question5',
            'Question6',
            'Question7',
            'Question8',
            'Question9',
            'Question10',
            'Question11',
            'Question12',
            'Question13',
            'Question14',
            'Question15',
            'Question16'
        ];
        $answers = [
            'Answer1',
            'Answer2',
            'Answer3',
            'Answer4',
            'Answer5',
            'Answer6',
            'Answer7',
            'Answer8',
            'Answer9',
            'Answer10',
            'Answer11',
            'Answer12',
            'Answer13',
            'Answer14',
            'Answer15',
            'Answer16'
        ];

        $combinations = $this->getCombinations($encodings, 3);
        $output = $this->generateOutput($combinations, $questions, $answers);

        header('Content-Type: text/plain');
        foreach ($output as $row) {
            echo implode("\t", $row['encodings']) . "\t";
            echo implode("\t", $row['questions']) . "\t";
            echo implode("\t", $row['shuffledAnswers']) . "\t";
            echo $row['order'] . "\n";
        }
    }

    public function test_h3()
    {
        $num_arr = ['α0101111', 'α0101112', 'α0101113', 'α0101121', 'α0101122', 'α0101123', 'α0101124', 'α0101125', 'α0101131', 'α0101132', 'α0101141', 'α0101142', 'α0101143', 'α0101144', 'α0101211', 'α0101212'];
        $question_arr = ['导电体', '绝缘体', '价电子', '本征半导体', '载流子', '电子', '空穴', '电子空穴对', 'N型半导体', 'P型半导体', '空间电荷区', '耗尽层', '电位壁垒', '阻挡层', '二极管', '二极管的结构和分类'];
        $answer_arr = ['a1', 'a2', 'a3', 'a4', 'a5', 'a6', 'a7', 'a8', 'a9', 'a10', 'a11', 'a12', 'a13', 'a14', 'a15', 'a16'];

        $combinations = [];

        // Generate all unique combinations of 3 elements from num_arr and question_arr
        for ($i = 0; $i < count($num_arr) - 2; $i++) {
            for ($j = $i + 1; $j < count($num_arr) - 1; $j++) {
                for ($k = $j + 1; $k < count($num_arr); $k++) {
                    $selected_nums = [$num_arr[$i], $num_arr[$j], $num_arr[$k]];
                    $selected_questions = [$question_arr[$i], $question_arr[$j], $question_arr[$k]];

                    // Shuffle answers and determine the correct order
                    $selected_answers = [$answer_arr[$i], $answer_arr[$j], $answer_arr[$k]];
                    $correct_order = ['A', 'B', 'C'];
                    shuffle($correct_order);

                    // Map shuffled order to selected answers
                    $shuffled_answers = [
                        $correct_order[0] => $selected_answers[0],
                        $correct_order[1] => $selected_answers[1],
                        $correct_order[2] => $selected_answers[2],
                    ];

                    $combination = [
                        'num1' => $selected_nums[0],
                        'num2' => $selected_nums[1],
                        'num3' => $selected_nums[2],
                        'question1' => $selected_questions[0],
                        'question2' => $selected_questions[1],
                        'question3' => $selected_questions[2],
                        'ans1' => $shuffled_answers['A'],
                        'ans2' => $shuffled_answers['B'],
                        'ans3' => $shuffled_answers['C'],
                        'order' => implode('', array_keys($shuffled_answers))
                    ];

                    $combinations[] = $combination;
                }
            }
        }

        // Output the combinations
        foreach ($combinations as $combination) {
            echo implode(", ", $combination) . PHP_EOL . '<br/>';
        }
    }

    public function test_h4()
    {


        $num_arr = ['α0101111', 'α0101112', 'α0101113', 'α0101121', 'α0101122', 'α0101123', 'α0101124', 'α0101125', 'α0101131', 'α0101132', 'α0101141', 'α0101142', 'α0101143', 'α0101144', 'α0101211', 'α0101212'];
        $question_arr = ['导电体', '绝缘体', '价电子', '本征半导体', '载流子', '电子', '空穴', '电子空穴对', 'N型半导体', 'P型半导体', '空间电荷区', '耗尽层', '电位壁垒', '阻挡层', '二极管', '二极管的结构和分类'];
        $answer_arr = ['a1', 'a2', 'a3', 'a4', 'a5', 'a6', 'a7', 'a8', 'a9', 'a10', 'a11', 'a12', 'a13', 'a14', 'a15', 'a16'];

        $combinations = [];

        for ($i = 0; $i < count($num_arr); $i++) {
            for ($j = $i + 1; $j < count($num_arr); $j++) {
                for ($k = $j + 1; $k < count($num_arr); $k++) {
                    $nums = [$num_arr[$i], $num_arr[$j], $num_arr[$k]];
                    $questions = [$question_arr[$i], $question_arr[$j], $question_arr[$k]];
                    $answers = [$answer_arr[$i], $answer_arr[$j], $answer_arr[$k]];

                    // Shuffle answers and determine order
                    $shuffled_answers = $answers;
                    shuffle($shuffled_answers);
                    $order = '';
                    foreach ($shuffled_answers as $answer) {
                        $index = array_search($answer, $answers);
                        $order .= chr(65 + $index); // Convert index to A, B, C
                    }

                    $combinations[] = [
                        'nums' => $nums,
                        'questions' => $questions,
                        'shuffled_answers' => $shuffled_answers,
                        'order' => $order
                    ];
                }
            }
        }

        // Output combinations
        foreach ($combinations as $combination) {
            echo implode(',', $combination['nums']) . ',' .
                implode(',', $combination['questions']) . ',' .
                implode(',', $combination['shuffled_answers']) . ',' .
                $combination['order'] . "\n<br/>";
        }
    }

    //gpt
    public function test_card_js()
    {

        $data = $this->zx_data();
        ini_set('memory_limit', '2566M');
        // 检查组合是否合法
        function isValidCombination($combination, $dataMap)
        {
            $usedActIds = [];
            foreach ($combination as $cardId) {
                $card = $dataMap[$cardId];
                $actId = $card['act_id'];

                // 检查 can_use
                if ($card['can_use'] !== 'all') {
                    foreach ($combination as $otherCardId) {
                        if ($otherCardId !== $cardId && !in_array($otherCardId, $card['can_use'])) {
                            return false; // 无效组合
                        }
                    }
                }

                // 检查 can_no_use
                if ($card['can_no_use'] === 'all') {
                    return false; // 无效组合
                } elseif (!empty($card['can_no_use'])) {
                    foreach ($combination as $otherCardId) {
                        if (in_array($otherCardId, $card['can_no_use'])) {
                            return false; // 无效组合
                        }
                    }
                }

                // 检查 can_no_with
                if ($card['can_no_with'] === 'all') {
                    return false; // 无效组合
                } elseif (!empty($card['can_no_with'])) {
                    if (in_array($actId, $usedActIds)) {
                        return false; // 无效组合
                    }
                }

                $usedActIds[] = $actId;
            }

            return true; // 合法组合
        }

        // 使用生成器逐步生成所有组合（只包含 act_card_id）
        function generateAllCombinations($data)
        {
            $n = count($data);
            $totalCombinations = 1 << $n; // 2^n

            for ($i = 1; $i < $totalCombinations; $i++) {
                $combination = [];
                for ($j = 0; $j < $n; $j++) {
                    if ($i & (1 << $j)) { // 检查第 j 位是否为 1
                        $combination[] = $data[$j]['act_card_id'];
                    }
                }
                yield $combination; // 返回当前组合
            }
            //            for ($j = 0; $j < $count; $j++) {
//                if ($i & (1 << $j)) {
//                    // 检查新卡片是否可以与组合中的其他卡片共组
//                    foreach ($combination as $existingCard) {
//                        if (!canCombine($cards[$j], $existingCard)) {
//                            $valid = false;
//                            break 2;
//                        }
//                    }
//                    $combination[] = $cards[$j];
//                    $sum += $cards[$j]['no_rel_value'];
//                }
//            }
        }

        // 主逻辑
        $dataMap = [];
        foreach ($data as $item) {
            $dataMap[$item['act_card_id']] = $item; // 将数据映射为以 act_card_id 为键的数组
        }

        $results = [];
        foreach (generateAllCombinations($data) as $combination) {
            if (isValidCombination($combination, $dataMap)) {
                $totalValue = 0;
                foreach ($combination as $cardId) {
                    $totalValue += $dataMap[$cardId]['no_rel_value'];
                }
                $results[] = [
                    'combination' => $combination, // 只保存 act_card_id
                    'total_value' => $totalValue,
                ];
            }
        }

        // 按 total_value 从高到低排序
        usort($results, function ($a, $b) {
            return $b['total_value'] <=> $a['total_value'];
        });
        print_json($results);

        // 输出结果
        header('Content-Type: application/json');
        echo json_encode($results, JSON_PRETTY_PRINT);
    }

    private function zx_data()
    {
        $data_str = '[{"id":"33943704140022784","card_id":"33943704140022784","can_use":"all","can_no_use":[],"can_no_with":[],"value":35,"card_value":35,"no_rel_value":35,"act_id":"1877268100662333441","act_card_id":307610,"card_code":"kafkac202501091620239Hi"},{"id":"33942042462684160","card_id":"33942042462684160","can_use":"all","can_no_use":[],"can_no_with":[],"value":34.2,"card_value":34.2,"no_rel_value":34.2,"act_id":"1877260061167226881","act_card_id":307600,"card_code":"kafkac20250109154507PwH"},{"id":"33943676497462272","card_id":"33943676497462272","can_use":"all","can_no_use":[],"can_no_with":[],"value":34,"card_value":34,"no_rel_value":34,"act_id":"1877268100662333441","act_card_id":307609,"card_code":"kafkac20250109162017Aan"},{"id":"33943648889504768","card_id":"33943648889504768","can_use":"all","can_no_use":[],"can_no_with":[],"value":33,"card_value":33,"no_rel_value":33,"act_id":"1877268100662333441","act_card_id":307608,"card_code":"kafkac20250109162020G89"},{"id":"33943621873992704","card_id":"33943621873992704","can_use":"all","can_no_use":[],"can_no_with":[],"value":32,"card_value":32,"no_rel_value":32,"act_id":"1877268100662333441","act_card_id":307607,"card_code":"kafkac20250109162017ofo"},{"id":"33943579615331328","card_id":"33943579615331328","can_use":"all","can_no_use":[],"can_no_with":[],"value":31,"card_value":31,"no_rel_value":31,"act_id":"1877268100662333441","act_card_id":307606,"card_code":"kafkac20250109162012vhg"},{"id":"33943550709236736","card_id":"33943550709236736","can_use":"all","can_no_use":[],"can_no_with":[],"value":30,"card_value":30,"no_rel_value":30,"act_id":"1877268100662333441","act_card_id":307605,"card_code":"kafkac202501091620127Cl"},{"id":"33941970934072320","card_id":"33941970934072320","can_use":"all","can_no_use":[],"can_no_with":[],"value":29.8,"card_value":29.8,"no_rel_value":29.8,"act_id":"1877260061167226881","act_card_id":307599,"card_code":"kafkac20250109154505EtU"},{"id":"33943510650487808","card_id":"33943510650487808","can_use":"all","can_no_use":[],"can_no_with":[],"value":29,"card_value":29,"no_rel_value":29,"act_id":"1877268100662333441","act_card_id":307604,"card_code":"kafkac20250109162013UNj"},{"id":"33943478167700480","card_id":"33943478167700480","can_use":"all","can_no_use":[],"can_no_with":[],"value":28,"card_value":28,"no_rel_value":28,"act_id":"1877268100662333441","act_card_id":307603,"card_code":"kafkac20250109162008gir"},{"id":"33943433112486912","card_id":"33943433112486912","can_use":"all","can_no_use":[],"can_no_with":[],"value":27,"card_value":27,"no_rel_value":27,"act_id":"1877268100662333441","act_card_id":307602,"card_code":"kafkac20250109162006UnO"},{"id":"33943381931492352","card_id":"33943381931492352","can_use":"all","can_no_use":[],"can_no_with":[],"value":26,"card_value":26,"no_rel_value":26,"act_id":"1877268100662333441","act_card_id":307601,"card_code":"kafkac20250109162004c3L"},{"id":"33939090722292736","card_id":"33939090722292736","can_use":"all","can_no_use":[],"can_no_with":[],"value":25,"card_value":25,"no_rel_value":25,"act_id":"1877247024871936001","act_card_id":307595,"card_code":"kafkac20250109145515Nwv"},{"id":"33939050679272448","card_id":"33939050679272448","can_use":"all","can_no_use":[],"can_no_with":[],"value":20,"card_value":20,"no_rel_value":20,"act_id":"1877247024871936001","act_card_id":307594,"card_code":"kafkac20250109145515T1t"},{"id":"33941923866641408","card_id":"33941923866641408","can_use":"all","can_no_use":[],"can_no_with":[],"value":17.8,"card_value":17.8,"no_rel_value":17.8,"act_id":"1877260061167226881","act_card_id":307598,"card_code":"kafkac20250109154505kWN"},{"id":"33939013386667008","card_id":"33939013386667008","can_use":"all","can_no_use":[],"can_no_with":[],"value":15,"card_value":15,"no_rel_value":15,"act_id":"1877247024871936001","act_card_id":307593,"card_code":"kafkac20250109145514A7b"}]';
        $data = json_decode($data_str, true);
        return $data;
    }


    public function test_card_js_old()
    {
        $data = $this->zx_data();
        ini_set('memory_limit', '2566M');
        function generateCombinationsRecursively($cards, $start = 0, $currentCombo = [], $currentActs = [], &$result = [])
        {
            //        print_json($cards);
            for ($i = $start; $i < count($cards); $i++) {
                $newCombo = array_merge($currentCombo, [$cards[$i]]);
                $newActs = array_merge($currentActs, [$cards[$i]['act_id']]);

                // 检查组合是否有效
                if (isValidCombination($newCombo, $newActs)) {
                    //                $comboIds = array_map(function($card) { return $card['act_card_id']; }, $newCombo);
//                sort($comboIds);
//                $comboKey = implode(',', $comboIds);

                    // 获取组合的标识（使用完整的卡片数据，而不是仅基于 card_id）
                    $comboKey = array_map(function ($card) {
                        return json_encode($card);
                    }, $newCombo);
                    // 检查当前组合的所有子集是否已存在
                    $isSubset = false;
                    foreach ($result as $existingCombo) {

                        if (array_diff($comboKey, $existingCombo) === []) {
                            //暂时去掉这里，不然 比如  321的时候21就不能成组了，那么这个时候选了2，1也不能用了

                            //                        print_json($comboKey,$existingCombo);
//                        $isSubset = true;
//                        break;
                        }
                    }

                    if (!$isSubset) {
                        // 移除任何已存在的组合是新组合的子集
                        foreach ($result as $key => $existingCombo) {
                            if (array_diff($existingCombo, $comboKey) === []) {
                                unset($result[$key]);
                            }
                        }

                        // 添加新组合
                        $result[] = $comboKey;

                        // 递归生成更大的组合

                        // 递归生成更大的组合
                        generateCombinationsRecursively($cards, $i + 1, $newCombo, $newActs, $result);
                    }
                }
            }
        }

        // 检查组合是否有效
        function isValidCombination($combo, $currentActs)
        {
            $comboIds = array_map(function ($card) {
                return $card['act_card_id'];
            }, $combo);

            foreach ($combo as $card) {
                // 检查不可共用的卡券
                if ($card['can_no_use'] === 'all') {
                    if (count($combo) > 1) {
                        return false;
                    }
                } else {
                    foreach ($card['can_no_use'] as $noUseId) {
                        if (in_array($noUseId, $comboIds)) {
                            return false;
                        }
                    }
                }

                // 检查必须共用的卡券
                if (!empty($card['can_use']) && $card['can_use'] !== 'all') {
                    foreach ($comboIds as $id) {
                        if ($id !== $card['card_id'] && !in_array($id, $card['can_use'])) {
                            return false;
                        }
                    }
                }

                // 检查act层次的约束
                if ($card['can_no_with'] === 'all') {
                    if (count($currentActs) > 1) {
                        return false;
                    }
                } else {
                    foreach ($card['can_no_with'] as $noWithActId) {
                        if (in_array($noWithActId, $currentActs)) {
                            return false;
                        }
                    }
                }
            }

            return true;
        }

        // 主逻辑
        $validCombinations = [];
        generateCombinationsRecursively($data, 0, [], [], $validCombinations);
        // 计算每个组合的总价值并排序
        $sortedCombinations = [];
        foreach ($validCombinations as $combo) {
            $comboIds = array_map(function ($card) {
                return json_decode($card, true)['card_code'];
            }, $combo);
            $totalValue = array_sum(array_map(function ($card) {
                return json_decode($card, true)['card_value'];
            }, $combo));
            $notRelValue = array_sum(array_map(function ($card) {
                return json_decode($card, true)['no_rel_value'];
            }, $combo));
            $combo_arr = [];
            $sortedCombinations[] = ['combo' => $combo, 'total_value' => $totalValue, 'not_rel_value' => $notRelValue];
            //            foreach ($comboIds as $co_v){
////                $one_co_v =  json_decode($co_v,true);
//                $sortedCombinations[] = ['combo' => $combo, 'total_value' => $totalValue];
//            }

            //            $sortedCombinations[] = ['combo' => $combo_arr, 'total_value' => $totalValue,'not_rel_value'=>$notRelValue];
        }
        //

        usort($sortedCombinations, function ($a, $b) {
            return $b['not_rel_value'] - $a['not_rel_value'];
            //            return $b['total_value'] - $a['total_value'];
        });
        print_json($sortedCombinations);
    }

    //ds
    public function test_zh_ds()
    {
        $data = $this->zx_data();

        /**
         * 检查两个卡片是否可以共组
         */
        function canCombine($card1, $card2)
        {
            // 检查 card1 的 can_use 规则
            if ($card1['can_use'] !== 'all') {
                if (!in_array($card2['act_card_id'], $card1['can_use'])) {
                    return false;
                }
            }

            // 检查 card1 的 can_no_use 规则
            if ($card1['can_no_use'] === 'all') {
                return false;
            } elseif (!empty($card1['can_no_use'])) {
                if (in_array($card2['act_card_id'], $card1['can_no_use'])) {
                    return false;
                }
            }

            // 检查 card1 的 can_no_with 规则
            if ($card1['can_no_with'] === 'all') {
                return false;
            } elseif (!empty($card1['can_no_with'])) {
                if (in_array($card2['act_id'], $card1['can_no_with'])) {
                    return false;
                }
            }

            // 检查 card2 的 can_use 规则
            if ($card2['can_use'] !== 'all') {
                if (!in_array($card1['act_card_id'], $card2['can_use'])) {
                    return false;
                }
            }

            // 检查 card2 的 can_no_use 规则
            if ($card2['can_no_use'] === 'all') {
                return false;
            } elseif (!empty($card2['can_no_use'])) {
                if (in_array($card1['act_card_id'], $card2['can_no_use'])) {
                    return false;
                }
            }

            // 检查 card2 的 can_no_with 规则
            if ($card2['can_no_with'] === 'all') {
                return false;
            } elseif (!empty($card2['can_no_with'])) {
                if (in_array($card1['act_id'], $card2['can_no_with'])) {
                    return false;
                }
            }

            return true;
        }

        /**
         * 生成所有有效的组合
         */
        function generateCombinations($cards)
        {
            $combinations = [];
            $count = count($cards);

            // 遍历所有可能的组合
            for ($i = 1; $i < (1 << $count); $i++) {
                $combination = [];
                $sum = 0;
                $valid = true;

                // 检查当前组合是否有效
                for ($j = 0; $j < $count; $j++) {
                    if ($i & (1 << $j)) {
                        // 检查新卡片是否可以与组合中的其他卡片共组
                        foreach ($combination as $existingCard) {
                            if (!canCombine($cards[$j], $existingCard)) {
                                $valid = false;
                                break 2;
                            }
                        }
                        $combination[] = $cards[$j];
                        $sum += $cards[$j]['no_rel_value'];
                    }
                }

                if ($valid) {
                    $combinations[] = [
                        'cards' => $combination,
                        'sum' => $sum
                    ];
                }
            }

            return $combinations;
        }

        // 生成所有有效组合
        $combinations = generateCombinations($data);

        // 按组合的 sum 从高到低排序
        usort($combinations, function ($a, $b) {
            return $b['sum'] <=> $a['sum'];
        });

        // 输出结果
        foreach ($combinations as $combination) {
            echo "Combination Sum: " . $combination['sum'] . "\n";
            echo json_encode(array_column($combination['cards'], 'act_card_id'));
            //            foreach ($combination['cards'] as $card) {
//                echo "Card ID: " . $card['act_card_id'] . ", no_rel_value: " . $card['no_rel_value'] . "\n";
//            }
            echo "<br/>\n";
        }

    }


    public function get_card_error()
    {
        $type = input('type', 1);
        $user_id = input('user_id', '');
        $phone = input('phone', '');
        $act_log_model = new DbActivityCenterLog();
        $where = ['is_enable' => 1];
        if ($type == 1) {
            $where['request_id'] = 'mallActivityReceiveCoupon';
        }
        if ($type == 2) {
            $where['request_id'] = 'getMallActivityCouponList';
        }
        if ($user_id || $phone) {
            $user_model = new DbUser();
            if ($user_id) {
                $user_where = ['id' => $user_id];
            }
            if ($phone) {
                $user_where = ['mid_phone' => $phone];
            }
            $user = $user_model->getOne(['where' => $user_where, 'order' => 'id desc']);
            $where['oneid'] = $user['one_id'];
            $user_id = $user['id'];
        }
        $list = $act_log_model->getList(['where' => $where, 'limit' => '2000', 'order' => 'id desc']);
        $table = '<table style="background:#f0f8ff">';
        foreach ($list as $v) {
            $table .= '<tr><td>' . $v['request_id'] . '</td><td>' . $v['oneid'] . '</td><td>' . $v['created_date'] . '</td><td>' . $v['request_info'] . '</td><td>' . $v['response_info'] . '</td><td>' . $user_id . '</td></tr>';
        }
        $table .= '</table>';
        echo $table;
    }

    public function test_check_log()
    {
        $type = input('type', 'packagesb');
        $send_note = input('vin', '9999999');
        $log_model = new DbLog();
        $where = ['type' => $type];
        if (in_array($type, ['packagesb', 'jiexu'])) {
            $where['send_note'] = $send_note;
        } else {
            $where['send_note'] = ['like', '%' . $send_note . '%s'];
        }
        $list = $log_model->getList(['where' => $where, 'order' => 'id desc', 'limit' => '1000']);
        $table = '<table style="background: #dedede">';
        foreach ($list as $v) {
            $table .= '<tr><td>' . $v['type'] . '</td><td>' . $v['send_note'] . '</td><td>' . $v['created_date'] . '</td><td>' . $v['receive_note'] . '</td></tr>';
        }
        $table .= '</table>';
        echo $table;
    }

    /**
     * 生成用户模拟参数
     * 访问: http://c.com/index_v2/lzx1/sc_url?user_token=lzx123&user_data=0815&user_id=3684125
     */
    public function sc_url()
    {
        $user_id = input('user_id', 3684125);

        // 获取用户信息
        $user_model = new DbUser();
        $user = $user_model->getOne(['where' => ['id' => $user_id]]);

        if (!$user) {
            echo "用户不存在: {$user_id}";
            return;
        }

        // 生成模拟参数，修正字段名称
        $params = [
            'user_id' => $user['id'],
            'unionid' => $user['unionid'] ?? '',
            'bind_unionid' => $user['unionid'] ?? '', // 将unionid赋值给bind_unionid
            'one_id' => $user['one_id'],
            'mid_phone' => $user['mid_phone'],
            'car_series_id' => $user['car_series_id'] ?? 0,
            'vin' => $user['vin'] ?? '',
            '18_oil_type' => $user['18_oil_type'] ?? 4,
            'car_offline_date' => $user['car_offline_date'] ?? '',
            'member_id' => $user['member_id'] ?? '',
            'car_18n' => $user['car_18n'] ?? ''
        ];

        echo "<h3>用户 {$user_id} 的模拟参数：</h3>";
        echo "<pre>" . json_encode($params, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";

        // 生成测试链接
        $test_url = "http://c.com/index_v2/lzx1/testGoodsListOptimized?user_token=lzx123&user_data=" . date('md') . "&user_id={$user_id}";
        echo "<h3>测试链接：</h3>";
        echo "<a href='{$test_url}' target='_blank'>{$test_url}</a>";
    }

    /**
     * 测试优化的商品列表方法
     * 访问: http://c.com/index_v2/lzx1/testGoodsListOptimized?user_token=lzx123&user_data=0815&user_id=3684125
     */
    public function testGoodsListOptimized()
    {
        $user_id = input('user_id', 3684125);
        $test_original = input('test_original', 0); // 是否同时测试原始方法

        try {
            // 获取用户信息
            $user_model = new DbUser();
            $user = $user_model->getOne(['where' => ['id' => $user_id]]);

            if (!$user) {
                echo json_encode(['error' => "用户不存在: {$user_id}"], JSON_UNESCAPED_UNICODE);
                return;
            }

            // 模拟请求参数
            $requestData = [
                'page' => 1,
                'pageSize' => 20,
                'comm_type_id' => '',
                'search' => '',
                'card_id' => '',
                'price_start' => '',
                'price_end' => '',
                'ask_at' => 0,
                'dd_dlr_code' => 0,
                'kilometer' => '',
                'lng' => '',
                'lat' => '',
                'n_dis_id' => 0,
                'full_cut_id' => 0,
                'order_by' => '',
                'new_order' => ''
            ];

            $channel_type = 'GWNET'; // 默认渠道

            echo "<h2>测试优化的商品列表方法</h2>";
            // 修正用户数据结构，添加缺失的字段
            $user['bind_unionid'] = $user['unionid'] ?? '';
            $user['vin'] = ''; // 默认为空，实际会通过getFriendBaseInfo获取
            $user['vin_list'] = []; // 默认为空数组
            $user['user_status'] = 0; // 默认用户状态

            echo "<h3>用户信息：</h3>";
            echo "<pre>" . json_encode([
                'user_id' => $user['id'],
                'unionid' => $user['unionid'] ?? '',
                'bind_unionid' => $user['bind_unionid'],
                'car_series_id' => $user['car_series_id'] ?? 0,
                'vin' => $user['vin'],
                'user_status' => $user['user_status']
            ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";

            echo "<h3>请求参数：</h3>";
            echo "<pre>" . json_encode($requestData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";

            // 测试优化方法
            $start_time = microtime(true);
            $netGoodsOptimized = new NetGoodsOptimized();
            $optimized_result = $netGoodsOptimized->goodsListOptimized($requestData, $user, $channel_type);
            $optimized_time = microtime(true) - $start_time;

            echo "<h3>优化方法结果：</h3>";
            echo "<p><strong>执行时间：</strong>" . number_format($optimized_time, 4) . " 秒</p>";
            echo "<p><strong>商品数量：</strong>" . count($optimized_result['data'] ?? []) . "</p>";
            echo "<p><strong>总数量：</strong>" . ($optimized_result['total'] ?? 0) . "</p>";

            // 显示前3个商品的详细信息
            if (!empty($optimized_result['data'])) {
                echo "<h4>前3个商品详情：</h4>";
                $sample_goods = array_slice($optimized_result['data'], 0, 3);
                foreach ($sample_goods as $index => $goods) {
                    echo "<h5>商品 " . ($index + 1) . "：</h5>";
                    echo "<pre>" . json_encode([
                        'commodity_id' => $goods['commodity_id'] ?? '',
                        'commodity_name' => $goods['commodity_name'] ?? '',
                        'final_price' => $goods['final_price'] ?? '',
                        'count_stock' => $goods['count_stock'] ?? '',
                        'tag_name' => $goods['tag_name'] ?? [],
                        'is_grouped' => $goods['is_grouped'] ?? 0,
                        'activity_type' => $goods['activity_type'] ?? '',
                        'commodity_dis_label' => $goods['commodity_dis_label'] ?? ''
                    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
                }
            }

            // 如果请求测试原始方法
            if ($test_original) {
                echo "<hr>";
                echo "<h3>原始方法对比：</h3>";

                $start_time = microtime(true);
                $netGoods = new NetGoods();
                $original_result = $netGoods->goodsList($requestData, $user, $channel_type);
                $original_time = microtime(true) - $start_time;

                echo "<p><strong>原始方法执行时间：</strong>" . number_format($original_time, 4) . " 秒</p>";
                echo "<p><strong>原始方法商品数量：</strong>" . count($original_result['data'] ?? []) . "</p>";
                echo "<p><strong>性能提升：</strong>" . number_format(($original_time - $optimized_time) / $original_time * 100, 2) . "%</p>";

                // 对比结果一致性
                $optimized_ids = array_column($optimized_result['data'] ?? [], 'commodity_id');
                $original_ids = array_column($original_result['data'] ?? [], 'commodity_id');

                $missing_in_optimized = array_diff($original_ids, $optimized_ids);
                $extra_in_optimized = array_diff($optimized_ids, $original_ids);

                if (empty($missing_in_optimized) && empty($extra_in_optimized)) {
                    echo "<p style='color: green;'><strong>✓ 商品ID一致性检查通过</strong></p>";
                } else {
                    echo "<p style='color: red;'><strong>✗ 商品ID一致性检查失败</strong></p>";
                    if (!empty($missing_in_optimized)) {
                        echo "<p>优化版本缺失的商品ID：" . implode(', ', $missing_in_optimized) . "</p>";
                    }
                    if (!empty($extra_in_optimized)) {
                        echo "<p>优化版本多出的商品ID：" . implode(', ', $extra_in_optimized) . "</p>";
                    }
                }
            }

            // 检查是否有错误
            if (isset($optimized_result['error'])) {
                echo "<p style='color: red;'><strong>错误信息：</strong>" . $optimized_result['error'] . "</p>";
            }

            echo "<hr>";
            echo "<h3>完整结果（JSON格式）：</h3>";
            echo "<textarea style='width: 100%; height: 300px;'>" . json_encode($optimized_result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</textarea>";

        } catch (\Exception $e) {
            echo "<p style='color: red;'><strong>异常信息：</strong></p>";
            echo "<pre>" . $e->getMessage() . "</pre>";
            echo "<pre>" . $e->getTraceAsString() . "</pre>";
        }
    }

    /**
     * 商品列表性能对比测试
     * 访问: /index_v2/lzx1/goodsListPerformanceTest?user_token=lzx123&user_data=0815
     */
    public function goodsListPerformanceTest()
    {
        echo "<h2>商品列表性能对比测试</h2>";
        echo "<p>请使用以下链接进行测试：</p>";
        echo "<ul>";
        echo "<li><a href='/index_v2/lzx1/sc_url?user_token=lzx123&user_data=" . date('md') . "&user_id=3684125' target='_blank'>生成用户3684125的测试参数</a></li>";
        echo "<li><a href='/index_v2/lzx1/testGoodsListOptimized?user_token=lzx123&user_data=" . date('md') . "&user_id=3684125' target='_blank'>测试优化方法（仅优化版本）</a></li>";
        echo "<li><a href='/index_v2/lzx1/testGoodsListOptimized?user_token=lzx123&user_data=" . date('md') . "&user_id=3684125&test_original=1' target='_blank'>测试优化方法（包含原始方法对比）</a></li>";
        echo "</ul>";

        echo "<h3>测试说明：</h3>";
        echo "<ul>";
        echo "<li>sc_url: 生成指定用户ID的模拟参数</li>";
        echo "<li>testGoodsListOptimized: 测试优化的商品列表方法</li>";
        echo "<li>添加 test_original=1 参数可以同时测试原始方法并进行性能对比</li>";
        echo "</ul>";
    }

    /**
     * 直接调试优化方法
     * 访问: /index_v2/lzx1/debugOptimized?user_token=lzx123&user_data=0815
     */
    public function debugOptimized()
    {
        echo "<h2>直接调试优化的商品列表方法</h2>";

        try {
            // 获取用户信息
            $user_id = 3684125;
            $user_model = new DbUser();
            $user = $user_model->getOne(['where' => ['id' => $user_id]]);

            if (!$user) {
                echo "<p style='color: red;'>用户不存在: {$user_id}</p>";
                return;
            }

            // 修正用户数据结构，添加缺失的字段
            $user['bind_unionid'] = $user['unionid'] ?? '';
            $user['vin'] = ''; // 默认为空，实际会通过getFriendBaseInfo获取
            $user['vin_list'] = []; // 默认为空数组
            $user['user_status'] = 0; // 默认用户状态

            echo "<h3>用户信息：</h3>";
            echo "<pre>" . json_encode([
                'id' => $user['id'],
                'unionid' => $user['unionid'] ?? '',
                'bind_unionid' => $user['bind_unionid'],
                'car_series_id' => $user['car_series_id'] ?? 0,
                'vin' => $user['vin'],
                'member_id' => $user['member_id'] ?? '',
                '18_oil_type' => $user['18_oil_type'] ?? 4,
                'user_status' => $user['user_status']
            ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";

            // 简单的请求参数
            $requestData = [
                'page' => 1,
                'pageSize' => 10,
                'comm_type_id' => '',
                'search' => '',
                'card_id' => '',
                'price_start' => '',
                'price_end' => '',
                'ask_at' => 0,
                'dd_dlr_code' => 0,
                'kilometer' => '',
                'lng' => '',
                'lat' => '',
                'n_dis_id' => 0,
                'full_cut_id' => 0,
                'order_by' => '',
                'new_order' => ''
            ];

            $channel_type = 'GWNET';

            echo "<h3>开始测试优化方法...</h3>";

            // 记录开始时间
            $start_time = microtime(true);

            // 实例化优化类
            $netGoodsOptimized = new NetGoodsOptimized();

            echo "<p>✓ NetGoodsOptimized 类实例化成功</p>";

            // 调用优化方法
            $result = $netGoodsOptimized->goodsListOptimized($requestData, $user, $channel_type);

            $execution_time = microtime(true) - $start_time;

            echo "<p>✓ 方法执行完成，耗时: " . number_format($execution_time, 4) . " 秒</p>";

            // 检查结果
            if (is_array($result)) {
                echo "<h3>执行结果：</h3>";
                echo "<p><strong>状态：</strong> " . ($result['status'] ?? 'unknown') . "</p>";
                echo "<p><strong>消息：</strong> " . ($result['msg'] ?? 'no message') . "</p>";
                echo "<p><strong>商品数量：</strong> " . count($result['data'] ?? []) . "</p>";
                echo "<p><strong>总数量：</strong> " . ($result['total'] ?? 0) . "</p>";

                // 显示前2个商品
                if (!empty($result['data'])) {
                    echo "<h4>前2个商品示例：</h4>";
                    $sample_goods = array_slice($result['data'], 0, 2);
                    foreach ($sample_goods as $index => $goods) {
                        echo "<h5>商品 " . ($index + 1) . "：</h5>";
                        echo "<pre>" . json_encode([
                            'commodity_id' => $goods['commodity_id'] ?? 'N/A',
                            'commodity_name' => $goods['commodity_name'] ?? 'N/A',
                            'final_price' => $goods['final_price'] ?? 'N/A',
                            'count_stock' => $goods['count_stock'] ?? 'N/A',
                            'tag_name' => $goods['tag_name'] ?? [],
                            'is_grouped' => $goods['is_grouped'] ?? 0,
                            'activity_type' => $goods['activity_type'] ?? '',
                            'commodity_dis_label' => $goods['commodity_dis_label'] ?? ''
                        ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
                    }
                } else {
                    echo "<p style='color: orange;'>⚠ 没有返回商品数据</p>";
                }

                // 检查是否有错误信息
                if (isset($result['error'])) {
                    echo "<p style='color: red;'><strong>错误信息：</strong> " . $result['error'] . "</p>";
                }

                echo "<h4>完整结果结构：</h4>";
                echo "<pre>" . json_encode(array_keys($result), JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";

            } else {
                echo "<p style='color: red;'>✗ 返回结果不是数组格式</p>";
                echo "<pre>" . var_export($result, true) . "</pre>";
            }

        } catch (\Exception $e) {
            echo "<h3 style='color: red;'>捕获到异常：</h3>";
            echo "<p><strong>错误信息：</strong> " . $e->getMessage() . "</p>";
            echo "<p><strong>错误文件：</strong> " . $e->getFile() . ":" . $e->getLine() . "</p>";
            echo "<h4>错误堆栈：</h4>";
            echo "<pre>" . $e->getTraceAsString() . "</pre>";
        } catch (\Error $e) {
            echo "<h3 style='color: red;'>捕获到致命错误：</h3>";
            echo "<p><strong>错误信息：</strong> " . $e->getMessage() . "</p>";
            echo "<p><strong>错误文件：</strong> " . $e->getFile() . ":" . $e->getLine() . "</p>";
            echo "<h4>错误堆栈：</h4>";
            echo "<pre>" . $e->getTraceAsString() . "</pre>";
        }

        echo "<hr>";
        echo "<p><strong>调试完成时间：</strong> " . date('Y-m-d H:i:s') . "</p>";
    }

    /**
     * 按照真实流程测试优化方法
     * 访问: /index_v2/lzx1/testRealFlow?user_token=lzx123&user_data=0815&user_id=3684125&channel=GWAPP
     */
    public function testRealFlow()
    {
        $user_id = input('user_id', 3684125);
        $channel = input('channel', 'GWAPP');

        echo "<h2>按照真实流程测试优化方法</h2>";
        echo "<p><strong>用户ID：</strong>{$user_id}</p>";
        echo "<p><strong>渠道：</strong>{$channel}</p>";

        try {
            // 第一步：生成用户加签信息（模拟 sc_url 接口）
            echo "<h3>第一步：生成用户加签信息</h3>";

            $user_model = new DbUser();
            $user = $user_model->getOneByPk($user_id);

            if (!$user) {
                echo "<p style='color: red;'>用户不存在: {$user_id}</p>";
                return;
            }

            // 生成加签参数（参考 sc_url 方法）
            $sign_data = [
                'time_sm' => strtotime('+1 hour'),
                'random_sm' => '013639',
                'test' => '123456',
                'channel_sm' => $channel,
                'member_id' => urlencode(nissanEncrypt($user['plat_id'])),
                'unique_sm' => urlencode(nissanEncrypt($user['unionid'])),
                'open_sm' => urlencode(nissanEncrypt($user['openid'])),
                'one_id' => urlencode(nissanEncrypt($user['one_id'])),
            ];

            $sign_query = build_query($sign_data);
            echo "<p>✓ 用户加签信息生成成功</p>";
            echo "<details><summary>查看加签参数</summary><pre>{$sign_query}</pre></details>";

            // 第二步：模拟调用商品列表接口
            echo "<h3>第二步：调用商品列表接口</h3>";

            // 构建完整的请求URL
            $base_url = "http://c.com/net-small/goods/goodsList";
            $test_params = [
                'test_n' => '11', // 使用优化方法的开关
                'page' => 1,
                'pageSize' => 10,
                'comm_type_id' => '',
                'search' => '',
                'card_id' => '',
                'price_start' => '',
                'price_end' => '',
                'ask_at' => 0,
                'dd_dlr_code' => 0,
                'kilometer' => '',
                'lng' => '',
                'lat' => '',
                'n_dis_id' => 0,
                'full_cut_id' => 0,
                'order_by' => '',
                'new_order' => ''
            ];

            $test_query = build_query($test_params);
            $full_url = $base_url . '?' . $sign_query . '&' . $test_query;

            echo "<p><strong>完整请求URL：</strong></p>";
            echo "<textarea style='width: 100%; height: 60px;'>{$full_url}</textarea>";

            // 第三步：使用cURL发送请求
            echo "<h3>第三步：发送HTTP请求</h3>";

            $start_time = microtime(true);

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $full_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; Test Bot)');

            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $curl_error = curl_error($ch);
            curl_close($ch);

            $execution_time = microtime(true) - $start_time;

            echo "<p><strong>HTTP状态码：</strong>{$http_code}</p>";
            echo "<p><strong>执行时间：</strong>" . number_format($execution_time, 4) . " 秒</p>";

            if ($curl_error) {
                echo "<p style='color: red;'><strong>cURL错误：</strong>{$curl_error}</p>";
                return;
            }

            if ($http_code !== 200) {
                echo "<p style='color: red;'><strong>HTTP错误：</strong>状态码 {$http_code}</p>";
                echo "<pre>{$response}</pre>";
                return;
            }

            // 第四步：解析响应结果
            echo "<h3>第四步：解析响应结果</h3>";

            $result = json_decode($response, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                echo "<p style='color: red;'><strong>JSON解析错误：</strong>" . json_last_error_msg() . "</p>";
                echo "<pre>{$response}</pre>";
                return;
            }

            echo "<p><strong>响应状态：</strong>" . ($result['status'] ?? 'unknown') . "</p>";
            echo "<p><strong>响应消息：</strong>" . ($result['msg'] ?? 'no message') . "</p>";

            if (isset($result['data'])) {
                $data = $result['data'];
                echo "<p><strong>商品数量：</strong>" . count($data['data'] ?? []) . "</p>";
                echo "<p><strong>总数量：</strong>" . ($data['total'] ?? 0) . "</p>";

                // 显示前2个商品示例
                if (!empty($data['data'])) {
                    echo "<h4>前2个商品示例：</h4>";
                    $sample_goods = array_slice($data['data'], 0, 2);
                    foreach ($sample_goods as $index => $goods) {
                        echo "<h5>商品 " . ($index + 1) . "：</h5>";
                        echo "<pre>" . json_encode([
                            'commodity_id' => $goods['commodity_id'] ?? 'N/A',
                            'commodity_name' => $goods['commodity_name'] ?? 'N/A',
                            'final_price' => $goods['final_price'] ?? 'N/A',
                            'count_stock' => $goods['count_stock'] ?? 'N/A',
                            'tag_name' => $goods['tag_name'] ?? [],
                            'is_grouped' => $goods['is_grouped'] ?? 0,
                            'activity_type' => $goods['activity_type'] ?? '',
                            'commodity_dis_label' => $goods['commodity_dis_label'] ?? ''
                        ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
                    }
                } else {
                    echo "<p style='color: orange;'>⚠ 没有返回商品数据</p>";
                }

                echo "<h4>用户信息：</h4>";
                if (isset($data['user_info'])) {
                    echo "<pre>" . json_encode($data['user_info'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
                }

            } else {
                echo "<p style='color: red;'>✗ 响应中没有data字段</p>";
            }

            // 第五步：对比测试（可选）
            echo "<h3>第五步：对比测试（原始方法 vs 优化方法）</h3>";

            // 测试原始方法
            $original_params = $test_params;
            unset($original_params['test_n']); // 移除优化方法开关
            $original_query = build_query($original_params);
            $original_url = $base_url . '?' . $sign_query . '&' . $original_query;

            echo "<p><strong>原始方法URL：</strong></p>";
            echo "<textarea style='width: 100%; height: 60px;'>{$original_url}</textarea>";

            $start_time = microtime(true);

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $original_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; Test Bot)');

            $original_response = curl_exec($ch);
            $original_http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            $original_time = microtime(true) - $start_time;

            echo "<p><strong>原始方法执行时间：</strong>" . number_format($original_time, 4) . " 秒</p>";

            if ($original_http_code === 200) {
                $original_result = json_decode($original_response, true);
                if ($original_result && isset($original_result['data'])) {
                    $original_count = count($original_result['data']['data'] ?? []);
                    $optimized_count = count($result['data']['data'] ?? []);

                    echo "<p><strong>原始方法商品数量：</strong>{$original_count}</p>";
                    echo "<p><strong>优化方法商品数量：</strong>{$optimized_count}</p>";

                    if ($original_time > $execution_time) {
                        $improvement = (($original_time - $execution_time) / $original_time) * 100;
                        echo "<p style='color: green;'><strong>性能提升：</strong>" . number_format($improvement, 2) . "%</p>";
                    } else {
                        echo "<p style='color: orange;'><strong>性能对比：</strong>优化方法用时较长</p>";
                    }

                    if ($original_count === $optimized_count) {
                        echo "<p style='color: green;'><strong>✓ 商品数量一致</strong></p>";
                    } else {
                        echo "<p style='color: red;'><strong>✗ 商品数量不一致</strong></p>";
                    }
                }
            } else {
                echo "<p style='color: red;'>原始方法请求失败，状态码：{$original_http_code}</p>";
            }

        } catch (\Exception $e) {
            echo "<h3 style='color: red;'>捕获到异常：</h3>";
            echo "<p><strong>错误信息：</strong> " . $e->getMessage() . "</p>";
            echo "<p><strong>错误文件：</strong> " . $e->getFile() . ":" . $e->getLine() . "</p>";
            echo "<h4>错误堆栈：</h4>";
            echo "<pre>" . $e->getTraceAsString() . "</pre>";
        }

        echo "<hr>";
        echo "<p><strong>测试完成时间：</strong> " . date('Y-m-d H:i:s') . "</p>";
    }

    /**
     * 性能对比测试方法
     */
    private function performanceTest($netGoodsOptimized, $requestData, $user, $channel_type, $where = [], $from = '', $type = '')
    {
        $results = [];

        // 测试原版本性能
        $original_start = microtime(true);
        $original_result = $netGoodsOptimized->goodsList($requestData, $user, $channel_type, $where, $from, $type);
        $original_time = microtime(true) - $original_start;

        // 测试优化版本性能
        $optimized_start = microtime(true);
        $optimized_result = $netGoodsOptimized->goodsListOptimized($requestData, $user, $channel_type, $where, $from, $type);
        $optimized_time = microtime(true) - $optimized_start;

        // 计算性能提升
        $performance_improvement = (($original_time - $optimized_time) / $original_time) * 100;

        $results = [
            'original_time' => number_format($original_time, 4),
            'optimized_time' => number_format($optimized_time, 4),
            'performance_improvement' => number_format($performance_improvement, 2) . '%',
            'original_count' => count($original_result['data'] ?? []),
            'optimized_count' => count($optimized_result['data'] ?? []),
            'data_consistency' => $this->compareResults($original_result, $optimized_result)
        ];

        Logger::info('NetGoodsOptimized performance test completed', $results);

        return $results;
    }

    /**
     * 比较两个结果的一致性
     */
    private function compareResults($original_result, $optimized_result)
    {
        $consistency = [
            'total_match' => ($original_result['total'] ?? 0) === ($optimized_result['total'] ?? 0),
            'count_match' => count($original_result['data'] ?? []) === count($optimized_result['data'] ?? []),
            'structure_match' => true
        ];

        // 检查商品ID是否一致
        $original_ids = array_column($original_result['data'] ?? [], 'commodity_id');
        $optimized_ids = array_column($optimized_result['data'] ?? [], 'commodity_id');

        $consistency['commodity_ids_match'] = (sort($original_ids) && sort($optimized_ids)) ?
            ($original_ids === $optimized_ids) : false;

        return $consistency;
    }

    /**
     * NetGoodsOptimized调试信息
     * 访问: /index_v2/lzx1/netGoodsDebugInfo?user_token=lzx123&user_data=0814
     */
    public function netGoodsDebugInfo()
    {
        // 模拟用户数据
        $user = [
            'id' => 12345,
            'bind_unionid' => 'test_unionid_12345',
            'car_series_id' => 100,
            'member_id' => 'M12345',
            'one_id' => 'ONE12345',
            'car_18n' => 'TEST18N',
            '18_oil_type' => 4,
            'car_offline_date' => '2025-12-31'
        ];

        $channel_type = 'GWSM';

        $test_params = [
            'page' => 1,
            'pageSize' => 20,
            'search' => '',
            'comm_type_id' => '',
            'car_id' => $user['car_series_id'],
            'dd_dlr_code' => 'TEST001'
        ];

        $results = [];

        // 测试原方法
        $results['original'] = $this->testOriginalGoodsListMethod($test_params, $user, $channel_type);

        // 测试优化方法
        $results['optimized'] = $this->testOptimizedGoodsListMethod($test_params, $user, $channel_type);

        // 性能对比
        $performance_comparison = $this->compareGoodsListPerformance($results);

        return json([
            'code' => 200,
            'msg' => '商品列表性能测试完成',
            'data' => [
                'results' => $results,
                'comparison' => $performance_comparison,
                'test_params' => $test_params,
                'user_info' => $user
            ]
        ]);
    }

    /**
     * 测试原始goodsList方法
     */
    private function testOriginalGoodsListMethod($params, $user, $channel_type)
    {
        $start_time = microtime(true);
        $start_memory = memory_get_usage();

        try {
            $net_goods = new NetGoods();
            $result = $net_goods->goodsList($params, $user, $channel_type);

            $end_time = microtime(true);
            $end_memory = memory_get_usage();

            return [
                'success' => true,
                'execution_time' => round(($end_time - $start_time) * 1000, 2), // 毫秒
                'memory_usage' => round(($end_memory - $start_memory) / 1024 / 1024, 2), // MB
                'peak_memory' => round(memory_get_peak_usage() / 1024 / 1024, 2), // MB
                'data_count' => isset($result['msg']['data']) ? count($result['msg']['data']) : 0,
                'total_count' => $result['msg']['total'] ?? 0,
                'result_code' => $result['code'] ?? 0,
                'cache_hit' => false // 原方法缓存信息
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'execution_time' => round((microtime(true) - $start_time) * 1000, 2),
                'memory_usage' => round((memory_get_usage() - $start_memory) / 1024 / 1024, 2)
            ];
        }
    }

    /**
     * 测试优化后的goodsList方法
     */
    private function testOptimizedGoodsListMethod($params, $user, $channel_type)
    {
        $start_time = microtime(true);
        $start_memory = memory_get_usage();

        try {
            $net_goods_optimized = new NetGoodsOptimized();
            $result = $net_goods_optimized->goodsListOptimized($params, $user, $channel_type);

            $end_time = microtime(true);
            $end_memory = memory_get_usage();

            return [
                'success' => true,
                'execution_time' => round(($end_time - $start_time) * 1000, 2), // 毫秒
                'memory_usage' => round(($end_memory - $start_memory) / 1024 / 1024, 2), // MB
                'peak_memory' => round(memory_get_peak_usage() / 1024 / 1024, 2), // MB
                'data_count' => isset($result['msg']['data']) ? count($result['msg']['data']) : 0,
                'total_count' => $result['msg']['total'] ?? 0,
                'result_code' => $result['code'] ?? 0,
                'cache_hit' => true, // 优化方法有缓存
                'error_details' => $result['msg'] ?? 'No error details'
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'execution_time' => round((microtime(true) - $start_time) * 1000, 2),
                'memory_usage' => round((memory_get_usage() - $start_memory) / 1024 / 1024, 2)
            ];
        }
    }

    /**
     * 性能对比分析
     */
    private function compareGoodsListPerformance($results)
    {
        $original = $results['original'];
        $optimized = $results['optimized'];

        if (!$original['success'] || !$optimized['success']) {
            return [
                'status' => 'error',
                'message' => '测试过程中出现错误，无法进行对比',
                'original_error' => $original['error'] ?? '',
                'optimized_error' => $optimized['error'] ?? ''
            ];
        }

        $time_improvement = 0;
        $memory_improvement = 0;

        if ($original['execution_time'] > 0) {
            $time_improvement = round((($original['execution_time'] - $optimized['execution_time']) / $original['execution_time']) * 100, 2);
        }

        if ($original['memory_usage'] > 0) {
            $memory_improvement = round((($original['memory_usage'] - $optimized['memory_usage']) / $original['memory_usage']) * 100, 2);
        }

        return [
            'status' => 'success',
            'time_improvement_percent' => $time_improvement,
            'memory_improvement_percent' => $memory_improvement,
            'original_time_ms' => $original['execution_time'],
            'optimized_time_ms' => $optimized['execution_time'],
            'original_memory_mb' => $original['memory_usage'],
            'optimized_memory_mb' => $optimized['memory_usage'],
            'data_consistency' => $original['data_count'] === $optimized['data_count'],
            'summary' => $this->generateGoodsListSummary($time_improvement, $memory_improvement)
        ];
    }

    /**
     * 生成性能总结
     */
    private function generateGoodsListSummary($time_improvement, $memory_improvement)
    {
        $summary = [];

        if ($time_improvement > 0) {
            $summary[] = "执行时间优化了 {$time_improvement}%";
        } elseif ($time_improvement < 0) {
            $summary[] = "执行时间增加了 " . abs($time_improvement) . "%";
        } else {
            $summary[] = "执行时间基本相同";
        }

        if ($memory_improvement > 0) {
            $summary[] = "内存使用优化了 {$memory_improvement}%";
        } elseif ($memory_improvement < 0) {
            $summary[] = "内存使用增加了 " . abs($memory_improvement) . "%";
        } else {
            $summary[] = "内存使用基本相同";
        }

        return implode('，', $summary);
    }

    /**
     * 显示性能测试页面
     * 访问: /index_v2/lzx1/performanceTestPage?user_token=lzx123&user_data=0814
     */
    public function performanceTestPage()
    {
        return $this->fetch('performance_test');
    }

    /**
     * 测试卡券相关的商品列表
     * 访问: /index_v2/lzx1/testCardGoodsList?user_token=lzx123&user_data=0814&card_id=123
     */
    public function testCardGoodsList()
    {
        $card_id = $this->request->param('card_id', '');
        if (!$card_id) {
            return json([
                'code' => 400,
                'msg' => '请提供card_id参数',
                'data' => []
            ]);
        }

        // 模拟用户数据
        $user = [
            'id' => 12345,
            'bind_unionid' => 'test_unionid_12345',
            'car_series_id' => 100,
            'member_id' => 'M12345',
            'one_id' => 'ONE12345',
            'car_18n' => 'TEST18N',
            '18_oil_type' => 4,
            'car_offline_date' => '2025-12-31',
            'vin' => 'TEST_VIN_123456789',
            'vin_list' => ['TEST_VIN_123456789']
        ];

        $channel_type = 'GWSM';

        $test_params = [
            'page' => 1,
            'pageSize' => 10,
            'card_id' => $card_id,
            'car_id' => $user['car_series_id'],
            'dd_dlr_code' => 'TEST001'
        ];

        $results = [];

        // 测试原方法
        $results['original'] = $this->testOriginalGoodsListMethod($test_params, $user, $channel_type);

        // 测试优化方法
        $results['optimized'] = $this->testOptimizedGoodsListMethod($test_params, $user, $channel_type);

        // 对比分析
        $analysis = $this->analyzeCardResults($results, $card_id);

        return json([
            'code' => 200,
            'msg' => '卡券商品列表测试完成',
            'data' => [
                'card_id' => $card_id,
                'results' => $results,
                'analysis' => $analysis,
                'test_params' => $test_params,
                'user_info' => $user
            ]
        ]);
    }

    /**
     * 分析卡券测试结果
     */
    private function analyzeCardResults($results, $card_id)
    {
        $original = $results['original'];
        $optimized = $results['optimized'];

        $analysis = [
            'card_id' => $card_id,
            'data_consistency' => false,
            'performance_comparison' => [],
            'card_logic_check' => [],
            'issues' => []
        ];

        if ($original['success'] && $optimized['success']) {
            // 检查数据一致性
            $analysis['data_consistency'] = $original['data_count'] === $optimized['data_count'];

            // 性能对比
            $analysis['performance_comparison'] = [
                'time_improvement' => $original['execution_time'] > 0 ?
                    round((($original['execution_time'] - $optimized['execution_time']) / $original['execution_time']) * 100, 2) : 0,
                'memory_improvement' => $original['memory_usage'] > 0 ?
                    round((($original['memory_usage'] - $optimized['memory_usage']) / $original['memory_usage']) * 100, 2) : 0
            ];

            // 卡券逻辑检查
            $analysis['card_logic_check'] = [
                'original_has_card_data' => isset($original['error_details']['get_card_list']),
                'optimized_has_card_data' => isset($optimized['error_details']['get_card_list']),
                'original_has_no_list_tip' => isset($original['error_details']['no_list_tip']),
                'optimized_has_no_list_tip' => isset($optimized['error_details']['no_list_tip'])
            ];

            if (!$analysis['data_consistency']) {
                $analysis['issues'][] = '数据数量不一致，可能卡券过滤逻辑有问题';
            }
        } else {
            if (!$original['success']) {
                $analysis['issues'][] = '原方法执行失败: ' . ($original['error'] ?? '未知错误');
            }
            if (!$optimized['success']) {
                $analysis['issues'][] = '优化方法执行失败: ' . ($optimized['error'] ?? '未知错误');
            }
        }

        return $analysis;
    }

    /**
     * 测试遗漏的查询条件
     * 访问: /index_v2/lzx1/testMissingConditions?user_token=lzx123&user_data=0814&n_dis_id=123&price_start=100&price_end=500&sku_ids=1,2,3
     */
    public function testMissingConditions()
    {
        // 模拟用户数据
        $user = [
            'id' => 12345,
            'bind_unionid' => 'test_unionid_12345',
            'car_series_id' => 100,
            'member_id' => 'M12345',
            'one_id' => 'ONE12345',
            'car_18n' => 'TEST18N',
            '18_oil_type' => 4,
            'car_offline_date' => '2025-12-31',
            'vin' => 'TEST_VIN_123456789',
            'vin_list' => ['TEST_VIN_123456789']
        ];

        $channel_type = 'GWSM';

        // 测试各种遗漏的条件
        $test_cases = [
            'n_dis_id' => [
                'page' => 1,
                'pageSize' => 10,
                'n_dis_id' => $this->request->param('n_dis_id', ''),
                'car_id' => $user['car_series_id']
            ],
            'full_cut_id' => [
                'page' => 1,
                'pageSize' => 10,
                'full_cut_id' => $this->request->param('full_cut_id', ''),
                'car_id' => $user['car_series_id']
            ],
            'price_range' => [
                'page' => 1,
                'pageSize' => 10,
                'price_start' => $this->request->param('price_start', ''),
                'price_end' => $this->request->param('price_end', ''),
                'car_id' => $user['car_series_id']
            ],
            'sku_ids' => [
                'page' => 1,
                'pageSize' => 10,
                'sku_ids' => $this->request->param('sku_ids', ''),
                'car_id' => $user['car_series_id']
            ]
        ];

        $results = [];

        foreach ($test_cases as $test_name => $test_params) {
            // 跳过空参数的测试
            $has_param = false;
            foreach (['n_dis_id', 'full_cut_id', 'price_start', 'price_end', 'sku_ids'] as $param) {
                if (!empty($test_params[$param])) {
                    $has_param = true;
                    break;
                }
            }

            if (!$has_param) {
                continue;
            }

            $results[$test_name] = [
                'params' => $test_params,
                'original' => $this->testOriginalGoodsListMethod($test_params, $user, $channel_type),
                'optimized' => $this->testOptimizedGoodsListMethod($test_params, $user, $channel_type)
            ];
        }

        // 分析结果
        $analysis = $this->analyzeMissingConditionsResults($results);

        return json([
            'code' => 200,
            'msg' => '遗漏条件测试完成',
            'data' => [
                'results' => $results,
                'analysis' => $analysis,
                'test_info' => [
                    'n_dis_id' => 'N件N折活动ID',
                    'full_cut_id' => '满减活动ID',
                    'price_start' => '价格区间起始',
                    'price_end' => '价格区间结束',
                    'sku_ids' => 'SKU ID列表'
                ]
            ]
        ]);
    }

    /**
     * 分析遗漏条件测试结果
     */
    private function analyzeMissingConditionsResults($results)
    {
        $analysis = [
            'total_tests' => count($results),
            'passed_tests' => 0,
            'failed_tests' => 0,
            'issues' => []
        ];

        foreach ($results as $test_name => $result) {
            $original = $result['original'];
            $optimized = $result['optimized'];

            if ($original['success'] && $optimized['success']) {
                if ($original['data_count'] === $optimized['data_count']) {
                    $analysis['passed_tests']++;
                } else {
                    $analysis['failed_tests']++;
                    $analysis['issues'][] = sprintf(
                        '%s: 数据数量不一致 (原始:%d, 优化:%d)',
                        $test_name,
                        $original['data_count'],
                        $optimized['data_count']
                    );
                }
            } else {
                $analysis['failed_tests']++;
                if (!$original['success']) {
                    $analysis['issues'][] = sprintf('%s: 原方法执行失败 - %s', $test_name, $original['error'] ?? '未知错误');
                }
                if (!$optimized['success']) {
                    $analysis['issues'][] = sprintf('%s: 优化方法执行失败 - %s', $test_name, $optimized['error'] ?? '未知错误');
                }
            }
        }

        return $analysis;
    }

    /**
     * 测试组合商品处理逻辑
     * 访问: /index_v2/lzx1/testGroupedGoods?user_token=lzx123&user_data=0814&is_grouped=1
     */
    public function testGroupedGoods()
    {
        // 模拟用户数据
        $user = [
            'id' => 12345,
            'bind_unionid' => 'test_unionid_12345',
            'car_series_id' => 100,
            'member_id' => 'M12345',
            'one_id' => 'ONE12345',
            'car_18n' => 'TEST18N',
            '18_oil_type' => 6, // 大于4L，会影响1L机油商品的逻辑
            'car_offline_date' => '2025-12-31',
            'vin' => 'TEST_VIN_123456789',
            'vin_list' => ['TEST_VIN_123456789']
        ];

        $channel_type = 'GWSM';

        // 测试组合商品的参数
        $test_params = [
            'page' => 1,
            'pageSize' => 20,
            'car_id' => $user['car_series_id'],
            'dd_dlr_code' => 'TEST001'
        ];

        $results = [];

        // 测试原方法
        $results['original'] = $this->testOriginalGoodsListMethod($test_params, $user, $channel_type);

        // 测试优化方法
        $results['optimized'] = $this->testOptimizedGoodsListMethod($test_params, $user, $channel_type);

        // 分析组合商品结果
        $analysis = $this->analyzeGroupedGoodsResults($results);

        return json([
            'code' => 200,
            'msg' => '组合商品测试完成',
            'data' => [
                'results' => $results,
                'analysis' => $analysis,
                'test_params' => $test_params,
                'user_info' => $user,
                'test_info' => [
                    'focus' => '组合商品的必选/非必选商品逻辑',
                    'key_logic' => [
                        '1. 必选子商品不匹配或没库存 -> 整个组合商品不显示',
                        '2. 全部为非必选商品且没有子商品 -> 组合商品不显示',
                        '3. 1L机油商品根据用户18_oil_type动态计算initial_num',
                        '4. 车型适配过滤 relate_car_ids'
                    ]
                ]
            ]
        ]);
    }

    /**
     * 分析组合商品测试结果
     */
    private function analyzeGroupedGoodsResults($results)
    {
        $original = $results['original'];
        $optimized = $results['optimized'];

        $analysis = [
            'data_consistency' => false,
            'grouped_goods_analysis' => [],
            'performance_comparison' => [],
            'issues' => []
        ];

        if ($original['success'] && $optimized['success']) {
            // 检查数据一致性
            $analysis['data_consistency'] = $original['data_count'] === $optimized['data_count'];

            // 性能对比
            $analysis['performance_comparison'] = [
                'time_improvement' => $original['execution_time'] > 0 ?
                    round((($original['execution_time'] - $optimized['execution_time']) / $original['execution_time']) * 100, 2) : 0,
                'memory_improvement' => $original['memory_usage'] > 0 ?
                    round((($original['memory_usage'] - $optimized['memory_usage']) / $original['memory_usage']) * 100, 2) : 0
            ];

            // 组合商品特定分析
            $analysis['grouped_goods_analysis'] = [
                'original_total' => $original['data_count'],
                'optimized_total' => $optimized['data_count'],
                'difference' => $original['data_count'] - $optimized['data_count'],
                'logic_preserved' => $analysis['data_consistency'] ? '是' : '否'
            ];

            if (!$analysis['data_consistency']) {
                $analysis['issues'][] = sprintf(
                    '组合商品数量不一致：原始方法 %d 个，优化方法 %d 个，差异 %d 个',
                    $original['data_count'],
                    $optimized['data_count'],
                    abs($original['data_count'] - $optimized['data_count'])
                );
            }
        } else {
            if (!$original['success']) {
                $analysis['issues'][] = '原方法执行失败: ' . ($original['error'] ?? '未知错误');
            }
            if (!$optimized['success']) {
                $analysis['issues'][] = '优化方法执行失败: ' . ($optimized['error'] ?? '未知错误');
            }
        }

        return $analysis;
    }

    /**
     * 测试组合商品间联动逻辑 (is_sp_associated)
     * 访问: /index_v2/lzx1/testSpAssociated?user_token=lzx123&user_data=0814&is_sp_associated=1
     */
    public function testSpAssociated()
    {
        // 模拟用户数据
        $user = [
            'id' => 12345,
            'bind_unionid' => 'test_unionid_12345',
            'car_series_id' => 100,
            'member_id' => 'M12345',
            'one_id' => 'ONE12345',
            'car_18n' => 'TEST18N',
            '18_oil_type' => 4,
            'car_offline_date' => '2025-12-31',
            'vin' => 'TEST_VIN_123456789',
            'vin_list' => ['TEST_VIN_123456789']
        ];

        $channel_type = 'GWSM';

        // 测试组合商品间联动的参数
        $test_params = [
            'page' => 1,
            'pageSize' => 20,
            'car_id' => $user['car_series_id'],
            'dd_dlr_code' => 'TEST001'
        ];

        $results = [];

        // 测试原方法
        $results['original'] = $this->testOriginalGoodsListMethod($test_params, $user, $channel_type);

        // 测试优化方法
        $results['optimized'] = $this->testOptimizedGoodsListMethod($test_params, $user, $channel_type);

        // 分析组合商品间联动结果
        $analysis = $this->analyzeSpAssociatedResults($results);

        return json([
            'code' => 200,
            'msg' => '组合商品间联动测试完成',
            'data' => [
                'results' => $results,
                'analysis' => $analysis,
                'test_params' => $test_params,
                'user_info' => $user,
                'test_info' => [
                    'focus' => 'is_sp_associated 组合商品间联动逻辑',
                    'key_logic' => [
                        '1. 规格联动的2个规格值，被车型过滤了一个，则2个商品的2个规格值都不返回',
                        '2. 规格联动的商品1个存在无关联关系的规格，1个都是关联关系的规格，则须过滤掉无关联关系的规格',
                        '3. 返回联动最低价格组合',
                        '4. 处理子商品之间的复杂规格联动关系'
                    ]
                ]
            ]
        ]);
    }

    /**
     * 分析组合商品间联动测试结果
     */
    private function analyzeSpAssociatedResults($results)
    {
        $original = $results['original'];
        $optimized = $results['optimized'];

        $analysis = [
            'data_consistency' => false,
            'sp_associated_analysis' => [],
            'performance_comparison' => [],
            'issues' => []
        ];

        if ($original['success'] && $optimized['success']) {
            // 检查数据一致性
            $analysis['data_consistency'] = $original['data_count'] === $optimized['data_count'];

            // 性能对比
            $analysis['performance_comparison'] = [
                'time_improvement' => $original['execution_time'] > 0 ?
                    round((($original['execution_time'] - $optimized['execution_time']) / $original['execution_time']) * 100, 2) : 0,
                'memory_improvement' => $original['memory_usage'] > 0 ?
                    round((($original['memory_usage'] - $optimized['memory_usage']) / $original['memory_usage']) * 100, 2) : 0
            ];

            // 组合商品间联动特定分析
            $analysis['sp_associated_analysis'] = [
                'original_total' => $original['data_count'],
                'optimized_total' => $optimized['data_count'],
                'difference' => $original['data_count'] - $optimized['data_count'],
                'logic_preserved' => $analysis['data_consistency'] ? '是' : '否',
                'complex_logic' => [
                    'spec_union_filter' => '规格联动过滤',
                    'car_type_filter' => '车型过滤联动',
                    'price_optimization' => '联动最低价格组合',
                    'sku_association' => 'SKU规格关联处理'
                ]
            ];

            if (!$analysis['data_consistency']) {
                $analysis['issues'][] = sprintf(
                    '组合商品间联动数量不一致：原始方法 %d 个，优化方法 %d 个，差异 %d 个',
                    $original['data_count'],
                    $optimized['data_count'],
                    abs($original['data_count'] - $optimized['data_count'])
                );
                $analysis['issues'][] = '可能的原因：规格联动过滤逻辑、车型过滤逻辑、价格组合逻辑等';
            }
        } else {
            if (!$original['success']) {
                $analysis['issues'][] = '原方法执行失败: ' . ($original['error'] ?? '未知错误');
            }
            if (!$optimized['success']) {
                $analysis['issues'][] = '优化方法执行失败: ' . ($optimized['error'] ?? '未知错误');
            }
        }

        return $analysis;
    }

    /**
     * 测试卡券匹配逻辑 ($card_rule)
     * 访问: /index_v2/lzx1/testCardRule?user_token=lzx123&user_data=0814&card_id=123
     */
    public function testCardRule()
    {
        $card_id = $this->request->param('card_id', '');
        if (!$card_id) {
            return json([
                'code' => 400,
                'msg' => '请提供card_id参数',
                'data' => []
            ]);
        }

        // 模拟用户数据
        $user = [
            'id' => 12345,
            'bind_unionid' => 'test_unionid_12345',
            'car_series_id' => 100,
            'member_id' => 'M12345',
            'one_id' => 'ONE12345',
            'car_18n' => 'TEST18N',
            '18_oil_type' => 4,
            'car_offline_date' => '2025-12-31',
            'vin' => 'TEST_VIN_123456789',
            'vin_list' => ['TEST_VIN_123456789']
        ];

        $channel_type = 'GWSM';

        // 测试卡券匹配的参数
        $test_params = [
            'page' => 1,
            'pageSize' => 10,
            'card_id' => $card_id,
            'car_id' => $user['car_series_id'],
            'dd_dlr_code' => 'TEST001'
        ];

        $results = [];

        // 测试原方法
        $results['original'] = $this->testOriginalGoodsListMethod($test_params, $user, $channel_type);

        // 测试优化方法
        $results['optimized'] = $this->testOptimizedGoodsListMethod($test_params, $user, $channel_type);

        // 分析卡券匹配结果
        $analysis = $this->analyzeCardRuleResults($results, $card_id);

        return json([
            'code' => 200,
            'msg' => '卡券匹配逻辑测试完成',
            'data' => [
                'card_id' => $card_id,
                'results' => $results,
                'analysis' => $analysis,
                'test_params' => $test_params,
                'user_info' => $user,
                'test_info' => [
                    'focus' => '$card_rule 卡券匹配逻辑',
                    'key_logic' => [
                        '1. 根据卡券匹配逻辑与当前商品的sku进行比较匹配',
                        '2. 根据卡券匹配逻辑与当前商品的sub_goods_id进行比较匹配',
                        '3. group_card_type=1时任意匹配，group_card_type=2时全部匹配',
                        '4. 精确的SKU级别匹配，确保卡券可用性'
                    ]
                ]
            ]
        ]);
    }

    /**
     * 分析卡券匹配测试结果
     */
    private function analyzeCardRuleResults($results, $card_id)
    {
        $original = $results['original'];
        $optimized = $results['optimized'];

        $analysis = [
            'card_id' => $card_id,
            'data_consistency' => false,
            'card_rule_analysis' => [],
            'performance_comparison' => [],
            'issues' => []
        ];

        if ($original['success'] && $optimized['success']) {
            // 检查数据一致性
            $analysis['data_consistency'] = $original['data_count'] === $optimized['data_count'];

            // 性能对比
            $analysis['performance_comparison'] = [
                'time_improvement' => $original['execution_time'] > 0 ?
                    round((($original['execution_time'] - $optimized['execution_time']) / $original['execution_time']) * 100, 2) : 0,
                'memory_improvement' => $original['memory_usage'] > 0 ?
                    round((($original['memory_usage'] - $optimized['memory_usage']) / $original['memory_usage']) * 100, 2) : 0
            ];

            // 卡券匹配特定分析
            $analysis['card_rule_analysis'] = [
                'original_total' => $original['data_count'],
                'optimized_total' => $optimized['data_count'],
                'difference' => $original['data_count'] - $optimized['data_count'],
                'logic_preserved' => $analysis['data_consistency'] ? '是' : '否',
                'matching_logic' => [
                    'sku_level_matching' => 'SKU级别精确匹配',
                    'sub_goods_matching' => '子商品ID匹配',
                    'group_card_type_1' => '任意匹配模式',
                    'group_card_type_2' => '全部匹配模式'
                ]
            ];

            if (!$analysis['data_consistency']) {
                $analysis['issues'][] = sprintf(
                    '卡券匹配数量不一致：原始方法 %d 个，优化方法 %d 个，差异 %d 个',
                    $original['data_count'],
                    $optimized['data_count'],
                    abs($original['data_count'] - $optimized['data_count'])
                );
                $analysis['issues'][] = '可能的原因：SKU匹配逻辑、sub_goods_id匹配逻辑、group_card_type处理等';
            }
        } else {
            if (!$original['success']) {
                $analysis['issues'][] = '原方法执行失败: ' . ($original['error'] ?? '未知错误');
            }
            if (!$optimized['success']) {
                $analysis['issues'][] = '优化方法执行失败: ' . ($optimized['error'] ?? '未知错误');
            }
        }

        return $analysis;
    }


    /**
     * 获取调试信息
     */
    private function getDebugInfo($netGoodsOptimized, $requestData, $user, $channel_type)
    {
        $debug_info = [
            'class_version' => '3.0',
            'optimization_features' => [
                'complete_database_joins' => true,
                'batch_data_loading' => true,
                'intelligent_caching' => true,
                'comprehensive_business_logic' => true,
                'error_handling_fallback' => true
            ],
            'parsed_params' => $requestData,
            'user_info' => [
                'id' => $user['id'] ?? 0,
                'car_series_id' => $user['car_series_id'] ?? 0,
                'vin' => $user['vin'] ?? '',
                'has_gift_cards' => isset($user['card_r_gift_wjh']) && !empty($user['card_r_gift_wjh'])
            ],
            'channel_type' => $channel_type,
            'test_timestamp' => date('Y-m-d H:i:s')
        ];

        return $debug_info;
    }

}
