<?php
/**
 * Created by PhpStorm.
 * User: lzx
 * Date: 20250711
 * Time: 6:29 PM
 */

namespace app\common\net_service;

use app\common\model\bu\BuOrder;
use app\common\model\bu\BuOrderCommodity;
use app\common\model\bu\BuSupplierOrderPushLog;
use app\common\model\db\DbCard;
use app\common\model\db\DbLog;
use app\common\model\db\DbSystemValue;
use app\common\model\db\DbUser;
use app\common\port\connectors\QuickWin;
use app\common\port\connectors\Supplier;

use function PHPSTORM_META\type;

class NetSupplier extends Common
{

    private  $no_go_supplier = ['DNDC-省广', 'PV', 'PV-柏泓', '日产', '启辰','JD','省广'];
    public function __construct()
    {
        parent::__construct();
    }


    /**
     * 推送订单到对方系统（OpenAPI）并写入日志
     * 直邮实物订单，非京东、非省广、非京东云仓的订单
     * 下方的其他表也可以参考这个对应的表
     * @param string $order_code
     * @return array
     */
    public function pushOrderToPartner($order_code)
    {
        $response = '';
        $success = 0;
        $request_json = $order_code;
        if (empty($order_code)) {
            $response = '缺少订单号';
        }
        $order_model =  new BuOrder();
        // 1. 获取订单主表
        $order = $order_model->where(['order_code' => $order_code])->find();
        if (!$order) {
            $response = '订单不存在';
        }
        $order_commodity_model= new BuOrderCommodity();
        // 2. 获取订单商品明细
        $commodities = $order_commodity_model->where(['order_code' => $order_code,'supplier'=>['not in',$this->no_go_supplier]])->select();
        if (!$commodities) {
            $response = '订单商品不存在';
        }
        $delivery_callback_url = config('DOMAIN_URL').'net-small/partner-callback/delivery';//回调地址
        if(!$response){
            $user_model=  new DbUser();
            $user =  $user_model->getOneByPk($order['user_id']);
            $payment_method_name = $order_model::paymentMethod();
            // 3. 组装OpenAPI数据结构
            $vip_yh = 0 ;
            $data = [
                'orderDetailNo'         => $order['order_code'],
                'totalAmount'           => $order['total_money']*100,//不含运费，其他优惠都包含了
                'deliveryFee'           => $order['mail_price']*100,
                'actualPaymentAmount'   => $order['money']*100,
                'orderStatus'           => $order_model::orderStatus($order['order_status']),
                'receiptAddress'        => $order['receipt_address'],
                'receiptName'        => $order['name'],
                'receiptMobile'        => $order['phone'],
                'remark'                => $order['remark'] ?? '',
                'createdDate'           => $order['created_date'],
                'couponDiscountAmount'  => $order['card_money'] ?? 0,
                'paymentMethod'         =>  $order_model::mapPaymentMethodForSupplier($order['payment_method']),
                'orderPaidTime'         => $order['pay_time'] ?? '',
                'totalPaymentIntegral'  => $order['integral']/10 ?? 0,
                'userName'              => '',
                'userMobile'            => $user['mid_phone'],
                'registerMobile'        => $user['mid_phone'],
                'deliveryCallbackUrl'   => $delivery_callback_url,
                'splitTime'=>$order['settlement_time'],
                'splitStatus'=>$order['settlement_state']==1?2:1,
                'activityDiscountAmount'=>$order['all_act_yh']*100,//所有活动优惠，包含了会员折扣+所有的活动

                'detail'                => [],
            ];
            // 获取供应商名称映射
            $supplierList = (new DbSystemValue())->getNameList(22);
            // 4. 组装商品明细

            foreach ($commodities as $item) {
                $vip_yh+=$item['commodity_segment_dis_money'];//
                $act_info = $this->getOrderCommodityActivityDesc($item);
                $act_id = 0;
                $act_type = '';
                if($act_info){
                    $act_id = $act_info['act_id'];
                    $act_type = $act_info['title'];

                }
                $supplierName = '';
                if (!empty($item['supplier']) && isset($supplierList[$item['supplier']])) {
                    $supplierName = $supplierList[$item['supplier']];
                }
                // 处理卡券信息
                $couponInfo = '';
                if (!empty($item['card_ids'])) {
                    $cardIdsArr = array_filter(explode(',', $item['card_ids']));                    $cardIdsArr = array_filter(explode(',', $item['card_ids']));
                    $cardCodesArr = array_filter(explode(',', $item['card_codes']));
                    $cardYhArr = array_filter(explode(',', $item['card_yh']));

                    if ($cardIdsArr) {
                        $cardList = (new DbCard())->where(['id' => ['in', $cardIdsArr]])->field('id,card_name')->select();
                        $couponArr = [];
                        // 转为以id为key的数组
                        $cardListById = [];
                        foreach ($cardList as $card) {
                            $cardListById[$card['id']] = $card;
                        }
                        foreach ($cardIdsArr as $kk=> $cid) {
                            if (isset($cardListById[$cid])) {
                                $card = $cardListById[$cid];
                                if($card){
                                    $couponArr[] = [
                                        'couponId' => $card['id'],
                                        'couponTitle' => $card['card_name'],
                                        'couponCode' => $cardCodesArr[$kk]??'',
                                        'price' => $cardYhArr[$kk]??'',
                                    ];
                                }

                            }
                        }


                        $couponInfo = json_encode($couponArr, JSON_UNESCAPED_UNICODE);
                    }
                }
                $data['detail'][] = [
                    'orderDetailNo'             => $item['order_code'],
                    'orderCommodityId'             => $item['id'],
                    'supplierCommodityCode'     => $item['third_sku_code'] ?? '',
                    'commodityCode'             => $item['commodity_id'],
                    'commodityName'             => $item['commodity_name'],
                    'skuCode'                   => $item['third_sku_code'],
                    'skuName'                   => $item['sku_info'] ?? '',
                    'skuInfo'                   => null,//$item['sku_info'] ?? ''
                    'commoditySkuProductCategory'=>  '',
                    'commodityPrice'            => $item['price']*100,
                    'commodityQuantity'         => $item['count'],
                    'commodityTotalPrice'       => $item['price']*100 * $item['count'],
                    'couponDiscountAmount'      => $item['card_all_dis']*100,
                    'originalTotalCostAmount'   => $item['actual_use_money']*100,
                    'totalCostIntegral'         => $item['actual_point']*10,
                    'logisticsDeliveryFee'      => $item['mail_price']*100,
                    'couponInfo'                => $couponInfo,
                    'activityId'                => $act_id,
                    'activityName'              => $item['act_name'] ?? '',
                    'activityType'              => $act_type,
                    'supplierCompanyName'       => $supplierName,
                    'supplierCompanyCode'       => $item['supplier'] ?? '',
                    'commodityPurchasePrice'    => $item['cost_price']*100,
                    'commodityTaxCode'          => $item['tax_code'] ?? '',
                    'commodityTaxRate'          => $item['tax'] ?? '',
                ];
            }
            // 5. 推送到对方接口
            $request_json = json_encode($data, JSON_UNESCAPED_UNICODE);
            $response = Supplier::create('supplier')->submitSupplierOrder($data);
            if (isset($response['result']) && $response['result'] == 1) {
                $success=1;
            }

            // 6.1 写入供应商订单推送日志表（如未存在）
            $pushLogModel = new BuSupplierOrderPushLog();
            $exists = $pushLogModel->getOne(['where'=>['order_detail_no'=>$order['order_code']]]);
            $log_data =[
                'order_detail_no'            => $order['order_code'],
                'total_amount'               => $order['total_money']*100,
                'delivery_fee'               => $order['mail_price']*100,
                'actual_payment_amount'      => $order['money']*100,
                'order_status'               => $order_model::orderStatus($order['order_status']),
                'receipt_address'            => $order['receipt_address'],
                'remark'                     => $order['remark'] ?? '',
                'order_date'               => $order['created_date'],
                'coupon_discount_amount'     => $order['card_money'] ?? 0,
                'payment_method'             => $order_model::mapPaymentMethodForSupplier($order['payment_method']),
                'order_paid_time'            => $order['pay_time'] ?? '',
                'total_payment_integral'     => $order['integral']/10 ?? 0,
                'user_name'                  => $order['name'],
                'user_mobile'                => $order['phone'],
                'register_mobile'            => $user['mid_phone'],
                'delivery_callback_url'      => $delivery_callback_url,
                'split_time'                 => $order['settlement_time'],
                'split_status'               => $order['settlement_state']==1?2:1,
                'activity_discount_amount'   => $order['all_act_yh'],
                'detail'                     => json_encode($data['detail'], JSON_UNESCAPED_UNICODE),
                'push_status'                => (isset($response['result']) && $response['result'] == 1) ? 1 : 0,
                'audit_status'               => 0,
                'push_time'                  => date('Y-m-d H:i:s'),
            ];
            if (!$exists) {
                $pushLogModel->insert($log_data);
            }else{
                $pushLogModel->saveData($log_data,['id'=>$exists['id']]);
            }
        }

        // 6. 写入日志表
        $db_log_data = array(
            'type'         => 'push_order_partner',
            'is_success'   => $success,
            'send_note'    => $request_json,
            'receive_note' => json_encode($response),
            'order_code' => $order_code,

        );
        $this->in_log_data($db_log_data);

        // 7. 返回结果
        if ($success) {
            return $this->re_msg('ok');
        } else {
            return $this->re_msg('推送失败',401);
        }
    }


    /**
     * 获取订单商品的活动描述（支持9种活动类型）
     * @param array $orderCommodity 订单商品数据
     * @return array 命中的活动描述数组，如['限时优惠123','N件N折456']
     */
    public function getOrderCommodityActivityDesc($orderCommodity)
    {
        $act_yh_list = [
            1 => ['title' => '限时优惠', 'id' => 1, 'key' => 'limit_id'],
            2 => ['title' => '秒杀活动', 'id' => 2, 'key' => 'seckill_id'],
            3 => ['title' => '满优惠', 'id' => 3, 'key' => 'full_id'],
            4 => ['title' => 'N件N折', 'id' => 4, 'key' => 'n_dis_id'],
            5 => ['title' => '多人拼团', 'id' => 5, 'key' => 'group_id'],
            6 => ['title' => '优惠套装', 'id' => 6, 'key' => 'suit_id'],
            7 => ['title' => '众筹活动', 'id' => 7, 'key' => 'crowd_id'],
            8 => ['title' => '买赠活动', 'id' => 8, 'key' => 'gift_act_id'],
            // 9 => ['title' => '抽奖活动', 'id' => 9, 'key' => 'lottery_id'],
            10 => ['title' => '预售', 'id' => 10, 'key' => 'pre_sale_id'],
        ];
        $result = [];
        foreach ($act_yh_list as $act) {
            $key = $act['key'];
            if (!empty($orderCommodity[$key])) {
                $result = ['title'=>$act['title'],'act_id'=>$orderCommodity[$key]];
                break;
            }
        }
        return $result;
    }
    private function in_log_data($data)
    {

        $log_model =  new DbLog();
        $log_model->insertData($data);
    }

    /**
     * 定时任务：同步第三方订单审核与物流信息
     * @param string $order_code
     * @return array
     */
    public function updateSupplierOrderInfoForJob($order_code)
    {
        if (empty($order_code)) {
            return $this->re_msg('缺少订单号', 400);
        }
        $pushLogModel = new BuSupplierOrderPushLog();
        $where = ['order_detail_no' => $order_code];
        $push_info = $pushLogModel->getOne(['where'=>$where]);
        if (empty($push_info)) {
            return $this->re_msg('未找到该订单推送记录', 404);
        }
        // 1. 查询第三方订单信息
        $res = Supplier::create('supplier')->supplierOrderInfo(['orderDetailNo' => $order_code]);
        if (empty($res) || empty($res['result']) || empty($res['data'])) {
            return $this->re_msg('第三方接口无返回', 500);
        }
        $data = $res['data'];
        // 2. 更新BuSupplierOrderPushLog的audit_status
        $pushLogModel->where($where)->update(['audit_status' => $data['auditStatus'] ?? 0]);
        // 3. 更新商品物流信息
        if (!empty($data['commodity']) && is_array($data['commodity'])) {
            $orderCommodityModel = new BuOrderCommodity();
            foreach ($data['commodity'] as $commodity) {
                $update = [
                    'logistics_company' => $commodity['logisticsCompany'] ?? '',
                    'logistics_no' => $commodity['logisticsNo'] ?? '',
                    'logistics_time' => $commodity['logisticsTime'] ?? '',
                ];
                if (!empty($commodity['logisticsNo'])) {
                    $update['order_commodity_status'] = 4;
                }
                $orderCommodityModel->where(['id' => $commodity['order_commodity_id']])->update($update);
            }
        }
        return $this->re_msg('同步完成');
    }

    /**
     * 提交售后单到第三方供应商
     * @param int $after_sale_id
     * @return array
     */
    public function sendAfterSaleToSupplier($after_sale_id)
    {
        if (empty($after_sale_id)) {
            return $this->re_msg('缺少售后单ID', 400);
        }
        $afterSaleModel = new \app\common\model\db\DbAfterSaleOrders();
        $afterSaleCommodityModel = new \app\common\model\db\DbAfterSaleOrderCommodity();
        $order_model =  new BuOrder();
        // 主单
        $afterSale = $afterSaleModel->where(['id' => $after_sale_id])->find();
        if (!$afterSale) {
            return $this->re_msg('售后单不存在', 404);
        }
        // 商品明细
        $commodities = $afterSaleCommodityModel->where(['afs_id' => $after_sale_id])->select();
        $order =  $order_model->getOneByPk($afterSale['order_id']);
        $orderCommodityModel = new BuOrderCommodity();
        $commodityList = [];
        foreach ($commodities as $item) {
            $orderCommodity = $orderCommodityModel->where(['id' => $item['order_commodity_id']])->find();
            $commodityList[] = [
                'orderDetailNo'      => $order['order_code'],
                'commodityCode'      => $orderCommodity ? $orderCommodity['commodity_id'] : '',
                'commodityName'      => $orderCommodity ? $orderCommodity['commodity_name'] : '',
                'skuCode'            => $orderCommodity ? $orderCommodity['third_sku_code'] : '',
                'applyCount'         => $item['after_count'],
                'applyRefundPrice'   => intval($item['return_money'] * 100),
                'applyAfterSalePrice'=> $item['return_money']* 100,
                'returnStock'        => $item['in_ware_count'],
                'applyAfterIntegral' => $item['return_point']* 10,
                'refundIntegral'     => $item['return_point']* 10,
            ];
        }
        $userReturnDate = '';
        if (!empty($afterSale['user_waybill_info'])) {
            $waybill = json_decode($afterSale['user_waybill_info'], true);
            if (is_array($waybill) && !empty($waybill['time'])) {
                $userReturnDate = $waybill['time'];
            }
        }
        $param = [
            'orderDetailNo'      => $order['order_code'],
            'afterSaleNo'        => $afterSale['afs_service_id'],
            'afterSaleType'      => $this->mapAfterSaleType($afterSale['afs_type']),
            'afterSaleReason'    => $afterSale['afs_reason'],
            'remark'             => $afterSale['remark'],
            'certificatePic'     => $afterSale['afs_reason_pictures'],
            'afterSaleStatus'    => $this->mapAfterSaleStatus($afterSale['afs_status']),
            'userReturnWaybillInfo' => $afterSale['user_waybill_info']==''?null:$afterSale['user_waybill_info'],
            'userReturnDate'     => $userReturnDate,
            'auditStatus'        => 'AUDIT_PASS',
            'replyOpinions'      => $afterSale['check_remark'],
            'insideRemark'       => $afterSale['remark'],
            'afterSaleCommodityList' => $commodityList,
        ];
        $res = \app\common\port\connectors\Supplier::create('supplier')->saveSupplierAfterSale($param);
         // 6. 写入日志表
         $log_data = array(
            'type'         => 'push_oafs_partner',
            'is_success'   => 1,
            'send_note'    => json_encode($param),
            'receive_note' => json_encode($res),
            'order_code' => $order['order_code'],

        );
        $this->in_log_data($log_data);

        return $res;
    }

    /**
     * 取消售后单推送到第三方供应商
     * @param string $afs_service_id
     * @return array
     */
    public function cancelAfterSaleToSupplier($afs_service_id)
    {
        if (empty($afs_service_id)) {
            return $this->re_msg('缺少售后服务单号', 400);
        }
        $afterSaleModel = new \app\common\model\db\DbAfterSaleOrders();
        $order_model = new BuOrder();
        // 查找售后单
        $afterSale = $afterSaleModel->getOneByPk($afs_service_id);
        if (!$afterSale) {
            return $this->re_msg('售后单不存在', 404);
        }
        $order = $order_model->getOneByPk($afterSale['order_id']);
        $param = [
            'orderDetailNo'   => $order['order_code'],
            'afterSaleNo'     => $afterSale['afs_service_id'],
            'afterSaleType'   => $this->mapAfterSaleType($afterSale['afs_type']),
            'afterSaleStatus' => $this->mapAfterSaleStatus($afterSale['afs_status']),
            'auditStatus'     => 'AUDIT_PASS',
        ];
        $res = \app\common\port\connectors\Supplier::create('supplier')->cancelSupplierAfterSale($param);
        // 写入日志
        $log_data = array(
            'type'         => 'cancel_oafs_partner',
            'is_success'   => 1,
            'send_note'    => json_encode($param),
            'receive_note' => json_encode($res),
            'order_code'   => $order['order_code'],
        );
        $this->in_log_data($log_data);
        return $res;
    }


    /**
     * 更新售后单信息推送到第三方供应商
     * @param string $afs_service_id
     * @return array
     */
    public function updateAfterSaleToSupplier($afs_service_id)
    {
        if (empty($afs_service_id)) {
            return $this->re_msg('缺少售后服务单号', 400);
        }
        $afterSaleModel = new \app\common\model\db\DbAfterSaleOrders();
        $afterSaleCommodityModel = new \app\common\model\db\DbAfterSaleOrderCommodity();
        $order_model = new BuOrder();
        // 查找售后单
        $afterSale = $afterSaleModel->where(['id' => $afs_service_id])->find();
        if (!$afterSale) {
            return $this->re_msg('售后单不存在', 404);
        }
        $order = $order_model->getOneByPk($afterSale['order_id']);
        // 商品明细
        $commodities = $afterSaleCommodityModel->where(['afs_id' => $afs_service_id])->select();
        $orderCommodityModel =  new BuOrderCommodity();
        $commodityList = [];
        foreach ($commodities as $item) {
            $orderCommodity = $orderCommodityModel->where(['id' => $item['order_commodity_id']])->find();
            $commodityList[] = [
                'orderDetailNo'        => $order['order_code'],
                'commodityCode'      => $orderCommodity ? $orderCommodity['commodity_id'] : '',
                'commodityName'      => $orderCommodity ? $orderCommodity['commodity_name'] : '',
                'skuCode'            => $orderCommodity ? $orderCommodity['third_sku_code'] : '',
                'applyCount'           => $item['after_count'],
                'applyRefundPrice'     => intval($item['return_money'] * 100),
                'applyAfterSalePrice'  => intval($item['return_money'] * 100),
                'returnStock'          => $item['in_ware_count'],
                'applyAfterIntegral'   => $item['return_point']* 10,
                'refundIntegral'       => $item['return_point']* 10,
            ];
        }
        $userReturnDate = '';
        if (!empty($afterSale['user_waybill_info'])) {
            $waybill = json_decode($afterSale['user_waybill_info'], true);
            if (is_array($waybill) && !empty($waybill['time'])) {
                $userReturnDate = $waybill['time'];
            }
        }
        $param = [
            'orderDetailNo'         => $order['order_code'],
            'afterSaleNo'           => $afterSale['afs_service_id'],
            'afterSaleType'         => $this->mapAfterSaleType($afterSale['afs_type']),
            'afterSaleReason'       => $afterSale['afs_reason'],
            'remark'                => $afterSale['remark'],
            'certificatePic'        => $afterSale['afs_reason_pictures'],
            'afterSaleStatus'       => $this->mapAfterSaleStatus($afterSale['afs_status']),
            'userReturnWaybillInfo' => $afterSale['user_waybill_info']==''?null:$afterSale['user_waybill_info'],
            'userReturnDate'        => $userReturnDate,
            'auditStatus'           => 'AUDIT_PASS',
            'replyOpinions'         => $afterSale['check_remark'],
            'insideRemark'          => $afterSale['remark'],
            'afterSaleCommodityList'=> $commodityList,
        ];
        $res = \app\common\port\connectors\Supplier::create('supplier')->updateSupplierAfterSale($param);
        // 写入日志
        $log_data = array(
            'type'         => 'update_supplier_oafs_partner',
            'is_success'   => 1,
            'send_note'    => json_encode($param),
            'receive_note' => json_encode($res),
            'order_code'   => $order['order_code'],
        );
        $this->in_log_data($log_data);
        return $res;
    }

    /**
     * 更新第三方订单信息推送到供应商
     * @param array $params
     * @param int $type 1:更新订单状态（退款） 2:更新结算状态
     * @return array
     */
    public function updateSupplierOrderToSupplier($order_code,$type=1)
    {
        if (empty($order_code)) {
            return $this->re_msg('缺少 orderDetailNo', 400);
        }
        $order_model =  new BuOrder();
        $order_push_model = new BuSupplierOrderPushLog();
        $order_push_log = $order_push_model->getOne(['where'=>['order_detail_no'=>$order_code]]);
        if(empty($order_push_log)){
            return $this->re_msg('订单未推送过', 400);
        }
        $order = $order_model->getOne(['where'=>['order_code'=>$order_code]]);
        if(empty($order)){
            return $this->re_msg('订单不存在', 404);
        }
        $params['orderDetailNo'] = $order_code;
        $order_status = $order_model::orderStatus($order['order_status']);
        $params['orderStatus'] = $order_status;
        $payload = [
            'orderDetailNo' => $params['orderDetailNo'],
        ];
        if ($type== 1) {
            $orderDetailNo = $params['orderDetailNo'];
            $orderCommodityModel = new BuOrderCommodity();
            $commodities = $orderCommodityModel->where(['order_code' => $orderDetailNo])->select();
            $detail = [];
            $all_refund_money = 0;
            $all_refund_integral = 0;
            foreach ($commodities as $item) {
                $return_money = isset($item['return_money']) ? intval($item['return_money'] * 100) : 0;
                $return_point = isset($item['return_point']) ? intval($item['return_point'] * 10) : 0;
                $all_refund_money += $return_money;
                $all_refund_integral += $return_point;
                $detail[] = [
                    'orderDetailNo'           => $orderDetailNo,
                    'orderCommodityId'        => $item['id'],
                    'skuCode'                 => $item['third_sku_code'],
                    'commodityRefundMoney'    => $return_money,
                    'commodityRefundIntegral' => $return_point,
                ];

            }
            $payload['detail'] = $detail;
            $payload['refundMoney'] = $all_refund_money;
            $payload['refundIntegral'] = $all_refund_integral;
            $payload['refundDate'] = $order['last_updated_date'];
        }

        if($type==2){
            $params['splitStatus'] = $order['settlement_state']==1?2:1;
            $params['splitDate'] = $order['settlement_time'];
        }

        $fields = [
            'orderStatus', 'receiptAddress', 'splitStatus', 'splitDate',
            'refundMoney', 'refundIntegral', 'refundDate', 'detail'
        ];
        foreach ($fields as $field) {
            if (isset($params[$field]) && $params[$field] !== '' && $params[$field] !== null) {
                $payload[$field] = $params[$field];
            }
        }
        $res = \app\common\port\connectors\Supplier::create('supplier')->updateSupplierOrder($payload);
        // 写入日志
        $log_data = array(
            'type'         => 'update_supplier_order_partner',
            'is_success'   => 1,
            'send_note'    => json_encode($payload),
            'receive_note' => json_encode($res),
            'order_code'   => $params['orderDetailNo'],
        );
        $this->in_log_data($log_data);
        return $res;
    }

    /**
     * 映射本地售后类型到第三方接口类型
     * @param int|string $afs_type
     * @return string
     */
    private function mapAfterSaleType($afs_type)
    {
        $map = [
            1 => 'REFUND',
            2 => 'RETURN_GOODS',
            3 => 'EXCHANGE_GOODS',
            '1' => 'REFUND',
            '2' => 'RETURN_GOODS',
            '3' => 'EXCHANGE_GOODS',
        ];
        return $map[$afs_type] ?? '';
    }

    /**
     * 映射本地售后状态到中文
     * @param int|string $afs_status
     * @return string
     */
    private function mapAfterSaleStatus($afs_status)
    {
        $map = [
            1  => 'REFUND_IN_PROGRESS',
            2  => 'REFUND_CANCELED',//已取消
            3  => '退款拒绝',
            4  => 'REFUND_IN_PROGRESS',//退款中
            5  => '退款失败',
            6  => 'AFTER_SALE_COMPLETED',//已退款
            7  => '退货待审核',
            8  => '退货拒绝',
            9  => 'REFUND_IN_PROGRESS',//退货待收
            10 => '换货待审核',
            11 => '换货拒绝',
            12 => 'EXCHANGE_BE_SENT_OUT',//换货待收
            13 => 'EXCHANGE_BE_SENT_OUT',//换货发货
            14 => 'AFTER_SALE_COMPLETED',//换货收货
            '1'  => 'REFUND_IN_PROGRESS',
            '2'  => 'REFUND_CANCELED',//已取消
            '3'  => '退款拒绝',
            '4'  => 'REFUND_IN_PROGRESS',
            '5'  => '退款失败',
            '6'  => 'AFTER_SALE_COMPLETED',
            '7'  => '退货待审核',
            '8'  => '退货拒绝',
            '9'  => 'REFUND_IN_PROGRESS',
            '10' => '换货待审核',
            '11' => '换货拒绝',
            '12' => 'EXCHANGE_BE_SENT_OUT',//换货待收
            '13' => 'EXCHANGE_BE_SENT_OUT',//换货发货
            '14' => 'AFTER_SALE_COMPLETED',//换货收货
        ];
        return $map[$afs_status] ?? '';
    }


}
