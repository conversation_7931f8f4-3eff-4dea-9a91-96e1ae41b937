<?php
/**
 * 对应一些公共方法
 * Created by PhpStorm.
 * User: lzx
 * Date: 2020-12-22 15:08:50
 */


namespace app\common\net_service;

use api\wechat\Carer;
use app\common\model\act\AcBlacklist;
use app\common\model\act\AcSuitYourself;
use app\common\model\bu\BuOrderMoreCardPoint;
use app\common\model\bu\BuOrderPoint;
use app\common\model\bu\BuShoppingCart;
use app\common\model\bu\BuUserCollection;
use app\common\model\db\DbActivity;
use app\common\model\db\DbActivityAssSku;
use app\common\model\db\DbArea;
use app\common\model\act\AcGroup;
use app\common\model\bu\BuCardReceiveRecord;
use app\common\model\bu\BuCheapSuitIndex;
use app\common\model\bu\BuOrder;
use app\common\model\bu\BuOrderCommodity;
use app\common\model\db\DbCard;
use app\common\model\db\DbCardLog;
use app\common\model\db\DbCarSeries;
use app\common\model\db\DbCommodity;
use app\common\model\db\DbCommodityCard;
use app\common\model\db\DbCommodityFlat;
use app\common\model\db\DbCommodityPay;
use app\common\model\db\DbCommodityPayCommodity;
use app\common\model\db\DbCommoditySet;
use app\common\model\db\DbCommoditySetSku;
use app\common\model\db\DbCommoditySku;
use app\common\model\db\DbCrowdfund;
use app\common\model\db\DbCrowdfundAgreement;
use app\common\model\db\DbCrowdfundOrder;
use app\common\model\db\DbDlr;
use app\common\model\db\DbDraw;
use app\common\model\db\DbFightGroup;
use app\common\model\db\DbFullDiscount;
use app\common\model\db\DbGift;
use app\common\model\db\DbGiftCommodity;
use app\common\model\db\DbLimitDiscount;
use app\common\model\db\DbLog;
use app\common\model\db\DbNDiscount;
use app\common\model\db\DbNDiscountCommodity;
use app\common\model\db\DbNDiscountInfo;
use app\common\model\db\DbPreSale;
use app\common\model\db\DbPreSaleCommodity;
use app\common\model\db\DbSeckill;
use app\common\model\db\DbSeckillCommodity;
use app\common\model\db\DbServerBag;
use app\common\model\db\DbSystemValue;
use app\common\model\db\DbUser;
use app\common\model\db\DbUserCarSeries;
use app\common\model\db\DbUserDraw;
use app\common\model\db\DbUserDrawRecord;
use app\common\model\db\DbUserSub;
use app\common\model\e3s\E3sCarSeries;
use app\common\model\e3s\E3sPartCarSeries;
use app\common\model\wlz\WlzCrowdsLogs;
use app\common\port\connectors\Crm;
use app\common\port\connectors\E3s;
use app\common\port\connectors\E3spRefactor;
use app\common\port\connectors\HaoWan;
use app\common\port\connectors\Member;
use app\common\port\connectors\QuickWin;
use app\common\port\connectors\Third;
use app\common\port\connectors\Website;
use app\test\controller\Draw;
use ForkModules\Traits\ResponseTrait;
use think\Cache;
use think\cache\driver\Redis;
use think\Controller;
use think\Db;
use think\Exception;
use think\exception\HttpResponseException;
use think\Hook;
use tool\Logger;
use function GuzzleHttp\Psr7\str;

class Common extends Controller
{
    use ResponseTrait;

    protected $_getCarer;
    protected $user_id;
    protected $source;
    protected $channel_type;
    protected $user;
    protected $unionid;
    private $pz_dlr_code_arr = ['PZ1ASM', 'PZ1AAPP'];
    private $qc_dlr_code_arr = ['QCSM', 'QCAPP'];
    private $app_dlr_arr = ['QCAPP'];
    private $goods_card_point = 'LQDW-ShangPinXiangQingYe';
    protected $user_vin = '';
    //特殊卡券场景 '59','维修保养(新)' '60','bimc(新)'
    private $spe_receive_scene = [59,60];




    public function getCarOwner($user)
    {
        $car_cash_name = 'USER-CAR-OWNER' . $user['bind_unionid'] . $user['member_id'] . $user['one_id'] . $user['channel_type'];
        if (!Cache::has($car_cash_name)) {
            $carer = [];
            $user_car_info = $this->user_car_type_redis($user);
            if (in_array($user['channel_type'], $this->pz_dlr_code_arr)) {
                $carer['card_degree_code'] = '';
                if ($user_car_info['have_brand_car'] == 1) {
                    $carer['brand_car_owner'] = 'P';
                } else {
                    $user['brand'] = 1;
                    $nissan_user_car_info = $this->user_car_type_redis($user);
                    if ($nissan_user_car_info['have_brand_car'] == 1) {
                        $carer['brand_car_owner'] = 'N';
                    }
                }
            } else if (in_array($user['channel_type'], $this->qc_dlr_code_arr)) {
                $Car = new  Carer();
                $carer = $Car->getMemberCard($user['channel_type'], $user['one_id'], $user['bind_unionid']);
                if ($user_car_info['have_brand_car'] == 1) {
                    $carer['brand_car_owner'] = 'V';
                }
            } else {
                if ($user['channel_type'] == 'GWAPP'){
                    $appMemberInfo = QuickWin::create('e3s_dlr')->appMemberInfo(['oneid' => $user['one_id']]);
                    $crm_default_card_no = $appMemberInfo['rows'][0]['CARD_NO'] ?? '';
                }else {
                    $member = Member::create("member")->member($user['member_id']);
                    $crm_default_card_no = $member['crm_default_card_no'] ?? '';
                }
                if (!empty($crm_default_card_no)) {
                    $crm = Member::create('member')->crm(['card_no' => $crm_default_card_no]);
                    if (!empty($crm['DATA'])) {
                        $crm_data = current($crm['DATA']);
                        $carer['card_degree_code'] = $crm_data['PV_CARD_DEGREE_CODE'];
                    }
                }
                if ($user_car_info['have_brand_car'] == 1) {
                    $carer['brand_car_owner'] = 'N';
                }
            }
            Cache::remember($car_cash_name, json_encode_cn($carer), mt_rand(180, 600));
        } else {
            $carer = Cache::get($car_cash_name);
        }
        return $carer;
    }
    /**
     * 车主信息
     * @param  $channel_type 1
     * @return array|bool|mixed
     */
    public function _getCarer($bind_unionid = "", $member_id = 0, $one_id = 0, $channel_type = 'GWSM',$ref=0)
    {
        if(date('Ymd') <= '202100502'){
//            return false;
        }
        if (!$bind_unionid && !$member_id) {
//            $this->openid='oYwsZv0PtBaNqQyLH-fmhdYVnwLY';
            return [];
        }
        $car_cash_name = 'USER-INFO-OPENID' . $bind_unionid . $member_id . $one_id.$channel_type;
        $carer         = redis($car_cash_name);
        //刷新时清空缓存
        if($ref==1){
            $carer = [];
        }
        $Car = new  Carer();

        if (in_array($channel_type, $this->pz_dlr_code_arr)) {
            $car_info = $Car->pzPonit(['oneid' => $one_id]);
            if ($car_info['error'] == 0) {
                $car_data                = $car_info['data'];
                $carer                   = [
                    'ic_card_no'           => '',
                    'vin'                  => '',
                    'car_no'               => '',
                    'name'                 => '',
                    'mobile'               => '',
                    'car_series_code'      => '',
                    'car_series_name'      => '',
                    'card_degree_code'     => '',
                    'card_degree_name'     => '',
                    'relation_dlr_code'    => '',
                    'car_type_code'        => '',
                    'member_type'          => 0,//1保客2潜客3潜客车主0非会员
                    'authentication_phone' => '',
                ];
                $carer['el_point']       = $car_data['elecUsePoints'];
                $carer['oil_point']      = $car_data['oilUsePoints'];
                $carer['oil_point_list'] = $car_data['oilCardList'];
                $carer['pv_point']       = bcadd($car_data['elecUsePoints'], $car_data['oilUsePoints']);
                $carer['dlr_point']      = 0;//暂时不给用专营店积分
            } else {
                return [];
            }
        } else {
            if (!empty($carer)) {
                $carer = json_decode($carer, true);
                if(!$carer){
                    $carer=[];
                }
            } else {
                $carer=[];
                if ($member_id && !in_array($channel_type, $this->qc_dlr_code_arr)) {
                    if ($channel_type == 'GWAPP'){
                        $appMemberInfo = QuickWin::create('e3s_dlr')->appMemberInfo(['oneid' => $one_id]);
                        $crm_default_card_no = $appMemberInfo['rows'][0]['CARD_NO'] ?? '';
                    }else {
                        $member = Member::create("member")->member($member_id);
                        $crm_default_card_no = $member['crm_default_card_no'] ?? '';
                    }

//                    if (!$member || isset($member['message'])) {
//                        return [];
//                    } else {
                        //更新用户昵称之类的东西
//                        if (isset($member['user_name'])) {
                            //不做更新了，获取上一个订单数据
//                            $user_model = new DbUser();
//                            $where      = ['plat_id' => $member_id];
//                            $user_data  = [
//                                'name'  => $member['user_name'],
//                                'phone' => isset($member['phone']) ? $member['phone'] : ''
//                            ];
//                            $user_model->saveData($user_data, $where);
//                        }
//                    Logger::debug('member-info-22',$member);
//                    if(!$member['crm_default_card_no']){
                        if (empty($crm_default_card_no)) {
                            $carer=[];
                        } else {
                            $crm = Member::create('member')->crm(['card_no' => $crm_default_card_no]);
//                        echo json_encode($crm['DATA']);die();
                            if (!empty($crm['DATA'])) {
                                $crm_data = end($crm['DATA']);
                                $carer    = [
                                    'ic_card_no'           => $crm_default_card_no,
                                    'vin'                  => $crm_data['VIN'],
                                    'car_no'               => $crm_data['CAR_LICENSE'] == '' ? $crm_default_card_no : $crm_data['CAR_LICENSE'],
                                    'name'                 => $crm_data['MEMBER_NAME'],
                                    'mobile'               => $crm_data['MOBILE'],
                                    'car_series_code'      => $crm_data['CAR_SERIES_CODE'],
                                    'car_series_name'      => $crm_data['CAR_SERIES_NAME'],
                                    'card_degree_code'     => $crm_data['PV_CARD_DEGREE_CODE'],
                                    'card_degree_name'     => $crm_data['PV_CARD_DEGREE_NAME'],
                                    'relation_dlr_code'    => $crm_data['JOIN_DLR_CODE'],
                                    'car_type_code'        => $crm_data['CAR_TYPE_CODE'],
                                    'member_type'          => isset($crm_data['MEMBER_TYPE']) ? $crm_data['MEMBER_TYPE'] : 0,//1保客2潜客3潜客车主0非会员
                                    'authentication_phone' => $crm_data['AUTHENTICATION_PHONE'],
                                ];
                            }
//                        }
                    }

                } else {
                    $carer = $Car->getMemberCard($channel_type,$one_id,$bind_unionid);
//                    if($channel_type=='QCAPP'){
//                        $u_info = Member::create("member")->getUnionid(['oneid'=>$one_id]);
//                        if(isset($u_info['unionid'])){
//                            $bind_unionid = $u_info['unionid'];
//                        }
//                    }
//                    if($bind_unionid){
//                        $carer = $Car->vin(array('unionid' => $bind_unionid));
//                    }else{
//                        $carer=[];
//                    }
//                    dd($bind_unionid);
                }
//            var_dump($carer);
                if ($carer) {
                    $dlr_model = new DbDlr();
                    $dlr       = $dlr_model->getOne(['where' => ['dlr_code' => $carer['relation_dlr_code']], 'field' => 'dlr_name']);
                    if ($dlr) {
                        $carer['relation_dlr_name'] = $dlr['dlr_name'];
                    } else {
                        $carer['relation_dlr_name'] = '';
                    }
                    $carer['card_class'] = '';
                    if (strpos($carer['card_degree_name'], '金卡') != false || $carer['card_degree_name'] == '员工卡') {
                        $carer['card_class'] = 'gold';
                    } elseif (strpos($carer['card_degree_name'], '银卡') != false) {
                        $carer['card_class'] = 'silver';
                    } elseif (strpos($carer['card_degree_name'], '铂金') != false) {
                        $carer['card_class'] = 'platinum';
                    }
                    $ic_card_no          = $carer['old_ic_card_no'] = $carer['ic_card_no'];
                    $carer['ic_card_no'] = substr($carer['ic_card_no'], 0, 1) . '  ' . substr($carer['ic_card_no'], 1, 4) . '  ' . substr($carer['ic_card_no'], 5, 4) . '  ' . substr($carer['ic_card_no'], 9, 10);
                    redis($car_cash_name, json_encode_cn($carer), mt_rand(120, 180));
                }else{
                    redis($car_cash_name, json_encode_cn([]), mt_rand(120, 180));
                }
            }

            if ($carer) {
                $carer['point'] = 0;

                if (isset($carer['old_ic_card_no'])) {
                    $carer['ic_card_no'] = $carer['old_ic_card_no'];
                    $point               = $Car->point(array('card_no' => $carer['old_ic_card_no']));//$car_info['ic_card_no']
                } else {
                    $point = $Car->point(array('card_no' => $carer['ic_card_no']));//$car_info['ic_card_no']
                }
//            $point = $Car->point(array('card_no' => $carer['old_ic_card_no']));//$car_info['ic_card_no']
                if ($point) {
                    $carer['dlr_point'] = 0;//暂时不给用专营店积分
                    $carer['pv_point']  = $point['pv_point'];
                }
            }
        }


//        var_dump($carer);
        return $carer;
    }

    /**
     * 生成订单号
     */
    protected function _getOrderNo($preStr = '', $length = 7)
    {
        $chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        $str   = "";
        for ($i = 0; $i < $length; $i++) {
            $str .= substr($chars, mt_rand(0, strlen($chars) - 1), 1);
        }

        return $preStr . $str;
    }

    //专营店信息
    protected function _getDlr($dlr_code)
    {
        $dlr_model = new DbDlr();
        $dlr       = $dlr_model->getOne(['where' => ['dlr_code' => $dlr_code]]);
        return $dlr;
    }

    //获取基础数据信息
    protected function _getSysName($type, $value_code)
    {
        $sys_model = new DbSystemValue();
        $sys       = $sys_model->getOne(array('where' => array('value_type' => $type, 'value_code' => $value_code, 'is_enable' => 1)));
        if ($sys) {
            return $sys['county_name'];
        } else {
            return false;
        }
    }


    //返回是否可以续团
    protected function _checkGroup($group_id, $group_order_code = '', $order_code = '', $goods_id)
    {
        $group_model = new DbFightGroup();
        $time        = date('Y-m-d H:i:s');

        $group_info = $group_model->getGroupInfo(
            [
                'where' =>
                    ['a.id' => $group_id, 'a.is_enable' => 1,
//            'a.start_time'=>["<=",$time],
//            'a.end_time'=>[">=",$time]
                    ],
                'field' => "a.id,a.title,a.start_time,a.end_time,a.people_number,a.purchase_number,a.buy_hour,a.rule,b.commodity_id,b.lowest_price"
            ]
        );
//        echo $group_model->getLastSql();
        if ($group_info) {

            $ac_group_model = new AcGroup();
            if ($order_code) {
                $ac_where = ['group_code' => $group_order_code, 'commodity_id' => $goods_id, 'user_id' => $this->user_id, 'status' => ['in', '3,4,5']];
                $ac       = $ac_group_model->getOne(['where' => $ac_where]);
                //不能参与自己的团购
                if ($ac) {
                    return $this->re_msg('不能参自己的团', 421);
                    return false;
                }
            }

            //参与
            if ($group_order_code) {
                //状态:1下单，2待支付，3已支付,4待发货,5已发货,6已退款,7已取消
                //待发货,已发货直接fasle
                $f_where       = ['group_code' => $group_order_code, 'commodity_id' => $goods_id, 'status' => ['in', '4,5']];
                $f_group_order = $ac_group_model->getOne(['where' => $f_where]);
//                echo $ac_group_model->getLastSql();die();
                if ($f_group_order) {
                    return $this->re_msg('该团已满员', 422);
                    return false;
                }

                $where            = ['group_code' => $group_order_code, 'commodity_id' => $goods_id, 'status' => ['in', '1,2,3']];
                $group_order      = $ac_group_model->getList(['where' => $where]);
                $where['status']  = ['in', '2,3,4,5'];//2 在支付页面的状态也包含进来
                $where['user_id'] = ['<>', $this->user_id];
                $count            = $ac_group_model->getCount(['where' => $where]);
                if (!$group_order) {
                    //没有成团的订单
                    return $this->re_msg('团购活动异常', 423);
                    return false;
                } elseif ($count >= $group_info['people_number']) {
                    //参团人数
                    return $this->re_msg('该团已满员', 422);
                    return false;
                } elseif (strtotime(sprintf("%s +%s hours", $group_order[0]['group_start_time'], $group_info['buy_hour'])) < time()) {
                    //参团过期
                    return $this->re_msg('该团未开始或已结束', 424);
                    return false;
                }
            }
            return true;
        } else {
            return $this->re_msg('该团未开始或已结束', 424);
            return false;
        }
    }

    /**
     * 是否预售及预售信息
     * @param $commodity
     * @param $pre_id 预售ID
     * @param $dj 1支付定金2 支付尾款!
     * @return array|bool|false|\PDOStatement|string|\think\Model
     */
    protected function _pre_sale($commodity, $pre_id, $dj = 0)
    {
        $pre_sale_com_model = new DbPreSaleCommodity();
        $order_model        = new BuOrder();
        $today              = date('Y-m-d H:i:s');
        $where              = [
            'a.commodity_id' => $commodity,
            'b.id'           => $pre_id,
            'a.is_enable'    => 1,
            'b.is_enable'    => 1,
        ];
        if ($dj == 1) {
            $where["b.front_s_time"] = ['<=', $today];
            $where["b.front_e_time"] = ['>=', $today];
        }
        if ($dj == 2) {
            $where["b.balance_s_time"] = ['<=', $today];
            $where["b.balance_e_time"] = ['>=', $today];
        }

        $fileds   = "b.purchase_number,b.front_money,b.dedu_money,b.id,b.dec,b.front_s_time,b.front_e_time,b.balance_s_time,b.balance_e_time,b.settlement_rule_id,b.settlement_rule_type,b.settlement_rule_value,b.title act_name,e3s_activity_id,b.rel_card_ids,b.activity_image,b.act_sett_standard";
        $pre_sale = $pre_sale_com_model->GetSalePreInfo(['where' => $where, 'field' => $fileds]);
        $can_buy  = 999;
        if ($pre_sale) {
            $pre_sale['pre_status'] = 0;//0不在任何支付时间内 1定金支付时间 2尾款支付时间
            if ($today >= $pre_sale['front_s_time'] && $today <= $pre_sale['front_e_time']) {
                $pre_sale['pre_status'] = 1;
            }
            if ($today >= $pre_sale['balance_s_time'] && $today <= $pre_sale['balance_e_time']) {
                $pre_sale['pre_status'] = 2;
            }
            if ($pre_sale['purchase_number'] <> 0) {
                $o_g_where = ['b.pre_sale_id' => $pre_sale['id'], 'b.commodity_id' => $commodity, 'a.user_id' => $this->user_id, 'a.order_status' => ['NOT IN', '1,3,5,6,8']];//不包括1、已下单；3、已取消；5、已退款；6、已过期；8、未支
                $o_g_count = $order_model->getOneJoin(['where' => $o_g_where, 'field' => "sum(b.count) number"]);
                if ($o_g_count) {
                    $can_buy = $pre_sale['purchase_number'] - $o_g_count['number'];
                }
            }
            if (!$pre_sale['dec']) {
                $pre_sale['dec'] = "暂无描述";
            }
//            var_dump($pre_sale['dedu_money']);
//            var_dump($pre_sale['front_money']);
//            die();
            $pre_sale['wk_times'] = sprintf("%s~%s", date('Y.m.d', strtotime($pre_sale['balance_s_time'])), date('m.d', strtotime($pre_sale['balance_e_time'])));
            $pre_sale['yh_money'] = round(($pre_sale['dedu_money'] - $pre_sale['front_money']), 2);//优惠金额
//            $pre_sale['wk'] = $pre_sale['dedu_money'] - $pre_sale['front_money'];//优惠金额
            $pre_sale['can_buy'] = $can_buy;
            ////是否要判断能不能购买。
            $pre_sale['ap_time'] = $today;
            return $pre_sale;
        } else {
            return [];
        }
    }


    /**
     * 商品卡券
     * @param $goods_card_where
     * @param $field
     * @return bool|false|mixed|\PDOStatement|string|\think\Collection
     */
    protected function _commodityCard($goods_card_where, $field)
    {
        $key  = 'detail_commodity_card_' . json_encode($goods_card_where);
        $data = redis($key);

        if (empty($data)) {
            $goods_card_model = new DbCard();
            $goods_card       = $goods_card_model->getCommodityCard(['where' => $goods_card_where, 'field' => $field]);
            $data = $goods_card;
//            print_json($goods_card_model->getLastSql());

//            redis($key, $data, 5);
        }

        return $data;
    }

    /**
     * 订单卡券
     * @param $goods_card_where
     * @param $field
     * @return bool|false|mixed|\PDOStatement|string|\think\Collection
     */
    protected function _card_by_order($goods_card_where, $field,$order)
    {
        $key  = 'detail_commodity_card_11' . json_encode($goods_card_where);
        $goods_card = redis($key);
        $goods_card=[];
        if (empty($goods_card)) {
            $goods_card_model = new DbCard();
            //todo 后续改成对接flat表就可以了
            $goods_card = $goods_card_model->getCardByOrder(['where' => $goods_card_where, 'field' => $field]);
            Logger::error('cardorderbysql-', [$goods_card_model->getLastSql()]);
//            $fileds           = "a.*,c.commodity_set_id,c.commodity_id,c.price,c.count,c.id cid,rec.card_code,rec.user_id,rec.receive_vin";

            if($goods_card){
                foreach ($goods_card as $k=>$v){
                    if($v['select_obj']){
                        if($v['select_obj']==1 && $v['user_id']!=$order['user_id']){
                            unset($goods_card[$k]);
                            continue;
                        }
                        if($v['select_obj']==2 && $v['receive_vin']!=$order['order_vin']){
                            unset($goods_card[$k]);
                            continue;
                        }
                        if($v['select_obj']==3 && ($v['user_id']!=$order['user_id'] || $v['receive_vin']!=$order['order_vin'])){
                            unset($goods_card[$k]);
                            continue;
                        }
                    }else{
                        if($v['receive_vin'] && $v['receive_vin']!=$order['order_vin']){
                            unset($goods_card[$k]);
                            continue;
                        }
                        if($v['user_id'] && $v['receive_vin'] && ($v['user_id']!=$order['user_id'] || $v['receive_vin']!=$order['order_vin'])){
                            unset($goods_card[$k]);
                            continue;

                        }
                    }

                }
            }
            $data = $goods_card;
            redis($key, $data, 5);
        }
        return $goods_card;
    }


    protected function _checkNDisById($commodity_id, $n_dis_id = '')
    {
        $n_dis_goods_model = new DbNDiscountCommodity();
        $time              = date('Y-m-d H:i:s');
        $where             = ['b.start_time' => ['<=', $time], 'b.end_time' => ['>=', $time], 'b.is_enable' => 1, 'a.is_enable' => 1, "a.commodity_id" => $commodity_id
        ];
        if ($n_dis_id) {
            $where['b.id'] = $n_dis_id;
        }

        $param['where'] = $where;
        $param['field'] = "a.n_id,a.commodity_id,b.title,b.des";
        $row            = $n_dis_goods_model->getNdisInfo($param);
        return $row;
    }

    //N件N折折扣信息 通过Nid获取商品列表
    public function getNDisCountNid($n_id)
    {
        $n_dis_goods_model = new DbNDiscountCommodity();
        $time              = date('Y-m-d H:i:s');
        $where             = [
            'b.start_time' => ['<=', $time], 'b.end_time' => ['>=', $time], 'b.is_enable' => 1, 'a.is_enable' => 1, "b.id" => $n_id
        ];
        $param['where']    = $where;
        $param['group']    = "b.id";
        $param['field']    = "GROUP_CONCAT(a.commodity_id SEPARATOR ',') g_ids";
        $row               = $n_dis_goods_model->getNdisInfo($param);
//        echo $n_dis_goods_model->getLastSql();
        return $row;
    }


    /**
     * 运费模板公用方法
     */
    public function _mail_price($order_code, $address, $action = 'mail_ajax')
    {

        $add   = explode(' ', $address)[0];
        $pro   = mb_substr($add, 0, 2, 'utf-8');//截取前两个字符
        $model = new DbArea();
        $area  = $model->getOne(['where' => ['area_name' => ['like', $pro . "%"], 'area_parent_id' => 0], 'field' => "area_id"]);
//        echo $model->getLastSql();
        if (!$area) {
            Logger::error('mail_price-wrong-pro:', ['pros' => $address, 'pro' => $pro]);
            return ['error' => 1, 'msg' => '省市选择错误'];
//            print_json(1,'省市选择错误');
        }
        //快递类商品
        $sql = sprintf(
            "SELECT a.count,d.first_count,d.first_fee,d.continue_count,d.continue_fee,a.id,d.id did,d.level_jsons,a.price,a.actual_price,a.commodity_name,a.commodity_pic from t_bu_order_commodity a 
JOIN t_db_commodity_flat b on a.commodity_id =b.commodity_id and find_in_set(a.dlr_code,b.up_down_channel_dlr)
JOIN t_db_commodity_set c on b.commodity_set_id=c.id
LEFT  JOIN t_db_freight_template d on c.template_guid=d.guid
where a.parent_order_code='%s' and FIND_IN_SET('%s',d.regionid_list) and a.order_mail_type=2 and a.mo_sub_id=0", $order_code, $area['area_id']
        );
        $list              = $model->query($sql);
        $order_goods_model = new BuOrderCommodity();
        $price             = 0;
//        dd($list);
        $mail_goods= [];
        if ($list) {
            $tp_list = [];
            foreach ($list as $v) {
                if(!empty($v['level_jsons'])){
                    $tp_list[$v['did']]['template_type']    = 2;
                }else{
                    $tp_list[$v['did']]['template_type']    = 1;
                }
                $tp_list[$v['did']]['did']    = $v['did'];
                $tp_list[$v['did']]['level_jsons']    = $v['level_jsons'];
//                $tp_list[$v['did']]['gradient_end']    = $v['gradient_end'];
//                $tp_list[$v['did']]['gradient_start']    = $v['gradient_start'];
                $tp_list[$v['did']]['first_count']    = $v['first_count'];
                $tp_list[$v['did']]['first_fee']      = $v['first_fee'];
                $tp_list[$v['did']]['continue_count'] = $v['continue_count'];
                $tp_list[$v['did']]['continue_fee']   = $v['continue_fee'];
//                $tp_list[$v['did']]['goods_count'] += $v['count'];
                $tp_list[$v['did']]['list'][] = $v;
            }
            $one_price = 0;
            foreach ($tp_list as $tt) {
                $tt_goods_count = 0;
                $tt_goods_all_price = 0;

                foreach ($tt['list'] as $vv) {
                    if($tt['template_type']==1){
                        $tt_goods_count += $vv['count'];
                    }
                    if($tt['template_type']==2){
                        $tt_goods_all_price+=$vv['count']*$vv['actual_price'];
                    }
                }
                if($tt['template_type']==2){
                    $t_goods_mail_j = json_decode($tt['level_jsons'],true);
                    foreach ($t_goods_mail_j as $kk=> $vv) {
                        $kk_js = explode('-',$kk);
                        if($tt_goods_all_price>=$kk_js[0] && $tt_goods_all_price<=$kk_js[1]){
                            $one_price = $vv;
                        }
                    }
                }
                if($tt['template_type']==1) {
                    if ($tt_goods_count <= $tt['first_count']) {
                        $one_price = $tt['first_fee'];
                    } else {
                        $one_price = $tt['first_fee'] + ($tt_goods_count - $tt['first_count']) / $tt['continue_count'] * $tt['continue_fee'];
                    }
                }

                $price             += $one_price;
                $toEnd             = count($tt['list']);
                $ac_mail_price_all = 0;
                $og_res            = 0;
                $mail_goods[$tt['did']]['price'] = $one_price;
                $mail_goods[$tt['did']]['list'] = $tt['list'];
                $sort = array_column($mail_goods, 'price');
                array_multisort($sort, SORT_DESC, $mail_goods);
                foreach ($tt['list'] as $vv) {
                    if (0 === --$toEnd) {
                        $one_goods_mail_price = $one_price - $ac_mail_price_all;
                    } else {
                        if($tt['template_type']==1) {
                            $one_goods_mail_price = round($vv['count'] * $one_price / $tt_goods_count, 2);
                        }else{
                            $one_goods_mail_price = round($vv['count']*$vv['actual_price'] * $one_price / $tt_goods_all_price, 2);
                        }
                        $ac_mail_price_all    += $one_goods_mail_price;
                    }
                    if ($action == 'go_pay') {
                        $og_res = $order_goods_model->saveData(['mail_price' =>sprintf("%.2f", $one_goods_mail_price), 'modifier' => 'm_p_o' . date('s')], ['id' => $vv['id']]);//更新每一个运费
                    }
                    Logger::debug('og_price_save', ['action' => $action, 'op' => $one_goods_mail_price, 'id' => $vv['id'],'order_code'=>$order_code,'og_res'=>$og_res,'sql'=>$order_goods_model->getLastSql()]);
                }
            }
            //无阶梯
//            foreach ($tp_list as $tt) {
//                $tt_goods_count = 0;
//                foreach ($tt['list'] as $vv) {
//                    $tt_goods_count += $vv['count'];
//                }
//                if ($tt_goods_count <= $tt['first_count']) {
//                    $one_price = $tt['first_fee'];
//                } else {
//                    $one_price = $tt['first_fee'] + ($tt_goods_count - $tt['first_count']) / $tt['continue_count'] * $tt['continue_fee'];
//                }
//                $price             += $one_price;
//                $toEnd             = count($tt['list']);
//                $ac_mail_price_all = 0;
//                $og_res            = 0;
//                foreach ($tt['list'] as $vv) {
//                    $tt_goods_count += $vv['count'];
//                    if (0 === --$toEnd) {
//                        $one_goods_mail_price = $one_price - $ac_mail_price_all;
//                    } else {
//                        $one_goods_mail_price = round($vv['count'] * $one_price / $tt_goods_count, 2);
//                        $ac_mail_price_all    += $one_goods_mail_price;
//                    }
//                    if ($action == 'go_pay') {
//                        $og_res = $order_goods_model->saveData(['mail_price' =>sprintf("%.2f", $one_goods_mail_price), 'modifier' => 'm_p_o' . date('s')], ['id' => $vv['id']]);//更新每一个运费
//                    }
//                    Logger::debug('og_price_save', ['action' => $action, 'op' => $one_goods_mail_price, 'id' => $vv['id'],'order_code'=>$order_code,'og_res'=>$og_res,'sql'=>$order_goods_model->getLastSql()]);
//                }
//            }
            return ['error' => 0, 'msg' => 'ok', 'data' =>['price'=>sprintf("%.2f", $price),'mail_goods'=>array_values($mail_goods)]];
        } else {
            Logger::error('mail_price_error', ['pro' => $pro, 'add' => $add, 'action' => $action, 'sql' => $sql]);
            return ['error' => 1, 'msg' => '当前地区无法下单，请更换地址'];
        }
    }


    /**
     * 商品ID获取运费
     */
    protected function _mail_price_by_goods($goods_id, $address, $action = 'mail_ajax', $channel_type = 'GWNET')
    {
        $add   = explode(' ', $address)[0];
        $pro   = mb_substr($add, 0, 2, 'utf-8');//截取前两个字符
        $model = new DbArea();
        $area  = $model->getOne(['where' => ['area_name' => ['like', $pro . "%"], 'area_parent_id' => 0], 'field' => "area_id"]);
//        echo $model->getLastSql();
        if (!$area) {
            Logger::error('mail_price-wrong-pro:', ['pros' => $address, 'pro' => $pro]);
            return ['error' => 1, 'msg' => '省市选择错误'];
        }
        $sql  = sprintf(
            "SELECT d.first_count,d.first_fee,d.continue_count,d.continue_fee from  t_db_commodity_flat flat
JOIN t_db_commodity_set s on flat.commodity_set_id =s.id
LEFT  JOIN t_db_freight_template d on s.template_guid=d.guid
where flat.commodity_id='%s' and FIND_IN_SET('%s',d.regionid_list) and FIND_IN_SET('%s',flat.up_down_channel_dlr)", $goods_id, $area['area_id'], $channel_type
        );
        $list = $model->query($sql);
        if ($list) {
            $price = isset($list[0]['first_fee']) ? $list[0]['first_fee'] : 999;
            return ['error' => 0, 'msg' => 'ok', 'data' => $price];
        } else {
            Logger::error('mail_price_error', ['pro' => $pro, 'add' => $add, 'action' => $action, 'sql' => $sql]);
            return ['error' => 1, 'msg' => '运费出错，请联系客服'];
        }
    }


    // 卡券商品可领卡券
    protected function card_order_card_id($order_code, $order_id)
    {
        //获取卡券
        $orde_goods_model = new BuOrderCommodity();
        $order_goods      = $orde_goods_model->getList(['where' => ['order_code' => $order_code]]);
        $card_arr         = [];
        $card_count_arr   = [];
        foreach ($order_goods as $v) {
            //到店代金券|取送车券
            if ($v['dd_commodity_type'] == 7 || in_array($v['order_source'], [20, 21, 24])) {
                $card_arr[]       = $v['goods_card_ids'];
                $card_count_arr[] = $v['count'];
            } else {
                $card_arr = explode(',', $v['card_goods_ids']);
                foreach ($card_arr as $vv) {
                    $card_count_arr[] = $v['count'];
                }
            }
        }
        $card_id_arr = [];
        $card_id     = '';
        $dlr_code    = '';
        $card_js     = [];
        if ($card_arr) {
            foreach ($card_arr as $k => $v) {
                $card_r_model = new BuCardReceiveRecord();
                $card_model   = new DbCard();
                $card_info    = $card_model->getOneByPk($v);
                if ($card_info) {
                    $dlr_code = $card_info['dlr_code'];
                    $card_r   = $card_r_model->getCount(['where' => ['status' => ['in', "1,3,4"], 'card_id' => $v, 'act_id' => $order_id, 'user_id' => $this->user_id]]);
                    if ($card_count_arr[$k] > 0) {
                        if ($card_r < $card_count_arr[$k]) {
                            $left_card_count =  bcsub($card_count_arr[$k],$card_r);
                            for ($i = 1; $i <= $left_card_count; $i++) {
                                $card_id_arr[] = $v;
                                $card_id       .= $v . ',';
                                $card_js[]     = ['cardid' => $card_info['card_id'], 'count' => $left_card_count];
                            }
                        }
                    }
                }
            }
        }
        // 判断$card_id是否为字符串，如果是则用逗号分割成数组
        if (is_string($card_id)) {
            $card_id = explode(',', $card_id);
        }
        // 去重
        $card_id = array_unique($card_id);
        $card_id_str = implode(',', $card_id); // 用于字符串场景
        Logger::error('ordercardid:',['order_code'=>$order_code,'card_id' => $card_id_str,'cards' => $card_js]);

        return ['card_id_arr' => $card_id_arr, 'dlr_code' => $dlr_code, 'cards' => $card_js, 'card_id' => $card_id_str];//主键，专营店，生成JSSDK数组
    }

    /**
     * 计算活动优惠
     * @param $order_code
     */
    protected function yh_js($order_code)
    {
        $order_model = new BuOrder();
        $where       = ['order_code' => $order_code];
        $field       = "a.order_code,d.n_dis,d.group_dis,d.pre_sale,d.suit_dis,d.full_dis,d.point_dis,d.commodity_id,b.count,b.price";
        $list        = $order_model->getListFlat(['where' => $where, 'field' => $field]);
        if ($list) {
            foreach ($list as $k => $v) {


            }
        }


    }

    /**
     * 取消团购订单
     * @param $order_code
     * @return $this|bool
     */
    protected function cacel_group($order_code)
    {
        $order_model    = new BuOrder();
        $group_ac_model = new AcGroup();

        $where  = ['order_code' => $order_code];
        $data   = array(
            'order_status'      => 3,
            'last_updated_date' => date('Y-m-d H:i:s'),

        );
        $res    = $order_model->saveData($data, $where);
        $g_data = array(
            'status'            => 7,//1下单，2待支付，3已支付,4待发货,5已发货,6已退款(没有),7已取
            'last_updated_date' => date('Y-m-d H:i:s'),
        );
        $res    = $group_ac_model->saveData($g_data, $where);
        return $res;
    }


    //N件N折折扣信息  多少名按下单人数，订单数量折扣按照商品总数量
    public function getNDisCount($com_ids, $counts, $n_id = '')
    {
        $n_dis_goods_model = new DbNDiscountCommodity();
        $time              = date('Y-m-d H:i:s');
        $where             = ['b.start_time' => ['<=', $time], 'b.end_time' => ['>=', $time], 'b.is_enable' => 1, 'a.is_enable' => 1, "a.commodity_id" => ['in', $com_ids]
        ];
        if ($n_id) {
            $where['b.id'] = $n_id;
        }

        $segment_info = get_user_segment_info();
        $membership = $segment_info['membership_level'];
        $owner = $segment_info['brand_owner_label'];
        $_l_where   = sprintf("FIND_IN_SET('%s',b.up_down_channel_dlr) and (b.user_segment=0 or (b.user_segment=1 and FIND_IN_SET('%s',b.user_segment_options)) or (b.user_segment=2 and FIND_IN_SET('%s',b.user_segment_options)))", $this->channel_type, $membership, $owner);
        $param['_where'] = $_l_where;
        $param['where'] = $where;
        $param['group'] = "b.id";
        $param['field'] = "GROUP_CONCAT(a.commodity_id SEPARATOR ',') g_ids,b.id,b.settlement_rule_id,b.settlement_rule_type,b.settlement_rule_value,b.title act_name,e3s_activity_id,b.card_available,b.rel_card_ids,b.user_segment,b.user_segment_options,b.act_sett_standard";
        $row            = $n_dis_goods_model->getNdisInfo($param);
        $dis_info       = [];
        if ($row) {
            $com_counts     = [];
            $com_ids_arr    = $com_ids;
            $com_counts_arr = $counts;
            foreach ($com_ids_arr as $kk => $vv) {
                foreach ($com_counts_arr as $kkk => $vvv) {
                    if ($kk == $kkk) {
                        $com_counts[] = [
                            'goods' => $vv,
                            'count' => $vvv,
                        ];
                    }
                }
            }
            //判断多少折扣
            $order_model       = new BuOrder();
            $count_order_where = [
                'order_status' => ['not in', [1, 3, 5, 6, 8, 10]],
                'sale_source'  => 5,
                'n_dis_id'     => $row['id'],
                'dlr_code'     => $this->channel_type,
            ];
            $order_count       = $order_model->getCount(['where' => $count_order_where]);//统计该活动订单数
            $n_dis_info_model  = new DbNDiscountInfo();
            $ac_goods_id       = explode(',', $row['g_ids']);
            $g_count_real      = 0;//活动订单商品数
            foreach ($com_counts as $cc) {
                if (in_array($cc['goods'], $ac_goods_id)) {
                    $g_count_real += $cc['count'];
                }
            }
            $n_dis_info_list = $n_dis_info_model->getList(['where' => ['n_id' => $row['id'], 'is_enable' => 1], 'field' => 'p_number', 'group' => "p_number"]);
            if ($n_dis_info_list) {
                $where = [
                    'n_id' => $row['id'], 'piece' => ['<=', $g_count_real]
                ];

                if ($row['user_segment']) {
                    if ($row['user_segment'] == 1) {
                        $where['segment_label'] = $membership;
                    } else {
                        $where['segment_label'] = $owner;
                    }
                }

                $order_by = "p_number desc,piece desc";
                $field    = "p_number,piece,discount,n_id";
                $param    = [
                    'where' => $where,
                    'order' => $order_by,
                    'field' => $field,
                ];
                //获得购买次数最高 ，购买数量最多的折扣信息
                $dis_info = $n_dis_info_model->getOne($param);
                if ($dis_info) {
                    $dis_info['g_dis']                 = $ac_goods_id;
                    $dis_info['discount_per']          = $dis_info['discount'] / 10;
                    $dis_info['settlement_rule_id']    = $row['settlement_rule_id'];
                    $dis_info['settlement_rule_type']  = $row['settlement_rule_type'];
                    $dis_info['settlement_rule_value'] = $row['settlement_rule_value'];
                    $dis_info['act_sett_standard'] = $row['act_sett_standard'];
                    $dis_info['e3s_activity_id'] = $row['e3s_activity_id'];
                    $dis_info['act_name'] = $row['act_name'];
                    $dis_info['card_available'] = $row['card_available'];
                    $dis_info['rel_card_ids'] = $row['rel_card_ids'];
                }
//                var_dump($dis_info);die();
            }
        }
        return $dis_info;
        //todo
    }

    /**
     * 优惠套装 返回一套
     * 缓存 1 min
     * @param $id
     * @return array
     */
    protected function _suit($id, $commodity_id)
    {
        $time_now = strtotime(date("Y-m-d H:i"));

        $key           = 'detail_suit_cheap_' . $this->channel_type . $commodity_id . $time_now . '_' . $id;
        $data          = redis($key);
        $suit_price    = 0;
        $suit_yh_price = 0;
        if (empty($data)) {
            $suit_list  = [];
            $suit_count = 0;
            $suit_model = new BuCheapSuitIndex();
            $suit_where = sprintf("FIND_IN_SET('%s',a.up_down_channel_dlr) and b.commodity_id='%s' and a.s_time<='%s' and a.e_time>='%s'", $this->channel_type, $commodity_id, $time_now, $time_now);
//            echo $suit_where; die();
            $if_suit = $suit_model->getOneUsuit(['_where' => $suit_where, 'field' => "index_id"]);
            if ($if_suit) {
                $suit_where = sprintf("FIND_IN_SET('%s',flat.up_down_channel_dlr) and b.index_id='%s'", $this->channel_type, $if_suit['index_id']);
                $suit_list  = $suit_model->getListUsuitFlat(['_where' => $suit_where, 'field' => "index_id,b.commodity_id,flat.cover_image,b.price,d.price old_price", 'group' => "index_id,commodity_id"]);
//                echo $suit_model->getLastSql();die();
                if ($suit_list) {
                    foreach ($suit_list as $k => $v) {
                        $suit_price    += $v['price'];
                        $suit_yh_price += $v['old_price'] - $v['price'];
//                        $suit_list[$k]['url'] = url('suit', ['id' => $id]);
                    }
                }
//                $id_arr =  explode(',',$id);
//                $suit_count = count($id_arr);
            }
            $data = ['suit_list' => $suit_list, 'suit_count' => 0, 'suit_price' => round($suit_price, 2), 'suit_yh_price' => round($suit_yh_price, 2)];
            redis($key, $data, 60);
        }

        return $data;
    }

    /**
     * 满优惠
     * @param $commodity_set_id
     * @return bool|false|mixed|\PDOStatement|string|\think\Collection
     */
    protected function _full($full_ids, $commodity_id, $channel_type)
    {

        $time_now = date("Y-m-d H:i:00");

        $segment_info = get_user_segment_info();
        $membership = $segment_info['membership_level'];
        $owner = $segment_info['brand_owner_label'];

        $key = 'detail_full_' . $channel_type . $commodity_id . $membership . $owner . $time_now;
        $data = redis($key);

        if (empty($data)) {
            $full_model  = new DbFullDiscount();
            $full_where  = [
                'a.id' => ['in', $full_ids],
                'b.commodity_id' => $commodity_id,
                'a.is_enable'     => 1,
                'a.start_time'   => ['<=', $time_now],
                'a.end_time'     => ['>=', $time_now]
            ];
            $_l_where   = sprintf("FIND_IN_SET('%s',a.up_down_channel_dlr) and (a.user_segment=0 or (a.user_segment=1 and FIND_IN_SET('%s',a.user_segment_options)) or (a.user_segment=2 and FIND_IN_SET('%s',a.user_segment_options)))", $channel_type, $membership, $owner);
            $cut_where[] = ['exp', $_l_where];
            $field       = "a.id,a.money,a.money,a.is_preferential_money,a.is_preferential_card,a.preferential_money,a.preferential_card_id,b.commodity_id,a.activity_title,a.full_discount_rules,a.purchase_number,a.user_segment,a.user_segment_options,a.activity_image,a.start_time,a.end_time";
            $full_list   = $full_model->getListU(['where' => $full_where, '_where' => $cut_where, 'field' => $field]);
//            echo $full_model->getLastSql();die();
            if ($full_list) {
                $order_model = new BuOrder();
                foreach ($full_list as $k => $v) {
                    if ($v['purchase_number'] > 0) {
                        // 1、已下单；3、已取消；5、已退款；6、已过期；8、未支付；18、交易关闭
                        $o_g_where = ['a.user_id' => $this->user_id, 'a.order_status' => ['NOT IN', '1,3,5,6,8,18']];
                        $o_g_count = $order_model->alias('a')->whereExists(function ($query) use ($v) {
                            $query->table('t_bu_order_commodity')->alias('b')->where('a.order_code=b.order_code')->where('b.full_id', $v['id']);
                        })->where($o_g_where)->field("count(*) number")->find();
                        $full_list[$k]['can_buy_number'] = $v['purchase_number'] - $o_g_count['number'];
                    } else {
                        $full_list[$k]['can_buy_number'] = 999;
                    }
                    $full_list[$k]['word'] = $v['activity_title'];
                    $full_list[$k]['url']  = url('search', ['full_cut_id' => $v['id']], false, true);

                    $full_list[$k]['commodity_dis_act_user_segment'] = $v['user_segment'];
                    $full_list[$k]['commodity_dis_label'] =$seg_lab =  get_user_segment_label($v['user_segment']);
                    $full_list[$k]['commodity_dis_label_cn'] = get_user_segment_label_cn($full_list[$k]['commodity_dis_label']);
                    $full_rule = json_decode($v['full_discount_rules'],true);
                    if($v['user_segment']){
                        $full_rule = $full_rule[$seg_lab]??[];
                    }
                    $rule_info =  '';
                    foreach ($full_rule as $r_v){
                        $rule_info.=sprintf("满%s减%s, ",$r_v[0],$r_v[1]);
                    }
                    $full_list[$k]['rule'] = trim($rule_info,', ');
                }
            }
            $data = $full_list;
            redis($key, $data, 60);
        }

        return isset($data[0]) ? $data[0] : [];
    }

    /**
     * 满优惠-工时
     * @return bool|false|mixed|\PDOStatement|string|\think\Collection
     */
    protected function _full_wi($full_ids, $commodity_id, $channel_type)
    {

        $time_now = date("Y-m-d H:i:00");

        $key  = 'detail_full_wi_' . $channel_type . $commodity_id . $time_now;
        $data = redis($key);

        if (empty($data)) {
            $full_model  = new DbFullDiscount();
            $full_where  = [
                'a.id' => ['in', $full_ids],
                'b.commodity_id' => $commodity_id,
                'a.is_enable'     => 1,
                'a.start_time'   => ['<=', $time_now],
                'a.end_time'     => ['>=', $time_now]
            ];
            $cut_where[] = ['exp', " (find_in_set('{$channel_type}',a.up_down_channel_dlr)) "];
            $field       = "a.id,a.money,a.money,a.is_preferential_money,a.is_preferential_card,a.preferential_money,a.preferential_card_id,b.commodity_id,a.activity_title,a.full_discount_rules,a.purchase_number,a.activity_image";
            $full_list   = $full_model->getListU(['where' => $full_where, 'field' => $field, '_where' => $cut_where]);
            if ($full_list) {
                $order_model = new BuOrder();
                foreach ($full_list as $k => $v) {
                    if ($v['purchase_number'] > 0) {
                        $o_g_where = ['a.user_id' => $this->user_id, 'a.order_status' => ['NOT IN', '1,3,5,6,8,18']];
                        $o_g_count = $order_model->alias('a')->whereExists(function ($query) use ($v) {
                            $query->table('t_bu_order_commodity')->alias('b')->where('a.order_code=b.order_code')->where('b.full_id', $v['id']);
                        })->where($o_g_where)->field("count(*) number")->find();
                        $full_list[$k]['can_buy_number'] = $v['purchase_number'] - $o_g_count['number'];
                    } else {
                        $full_list[$k]['can_buy_number'] = 999;
                    }
                    $full_list[$k]['word'] = $v['activity_title'];
                    $full_list[$k]['url']  = url('search', ['full_cut_id' => $v['id']], false, true);
                }
            }
            $data = $full_list;
            redis($key, $data, 60);
        }
        return isset($data[0]) ? $data[0] : [];
    }

    /**
     * 团购信息
     * @param $id
     * @param $group_id
     * @return array|bool|false|mixed|\PDOStatement|string|\think\Model|null
     */
    protected function _group($id, $group_id, $commodity_id)
    {
        $time_now = date("Y-m-d H:i:00");

        $key  = 'detail_group_' . $this->channel_type . $commodity_id . $time_now;
        $data = redis($key);
//        $data='';
        if (empty($data)) {
            $group_model = new DbFightGroup();
            $g_where     = ['b.commodity_id' => $commodity_id, 'a.is_enable' => 1, 'a.start_time' => ["<=", $time_now], 'a.end_time' => [">=", $time_now]];
            if ($group_id) {
                $g_where['a.id'] = $group_id;
            }
            $g_info = $group_model->getGroupInfo(['where' => $g_where, 'field' => "a.id,a.title,a.activity_image"]);

            $data = $g_info;
            redis($key, $data, 60);
        }

        return $data;
    }

    /** 臻享服务包信息
     * @param $servPackId
     * @param $commodityId
     * @return array|bool|mixed|\PDOStatement|string|\think\Model|null
     */
    protected function _serv_pack($servPackId, $commodityId)
    {
        $key = 'detail_serv_pack_' . $this->channel_type . $servPackId .  $commodityId;
        $data = redis($key);

        if (empty($data)) {
            $servPackmodel = new DbServerBag();
            $now = date("Y-m-d H:i:s");
            $where = [
                'b.commodity_id' => $commodityId,
                'a.is_enable' => 1,
                'a.start_time' => ["<=", $now],
                'a.end_time' => [">=", $now]
            ];
            if ($servPackId) {
                $where['a.id'] = $servPackId;
            }
            $_where = sprintf("FIND_IN_SET('%s',a.channels) ", $this->channel_type);
            $data = $servPackmodel->getServPackInfo([
                'where' => $where,
                '_where' => $_where,
                'field' => "a.id, a.title, a.card_available, a.pay_type, b.commodity_set_id, b.counts, b.commodity_set_sku_json"
            ]);
//            echo $servPackmodel->getLastSql();die();
            if ($data) {
                $data['commodity_set_sku_arr'] = json_decode($data['commodity_set_sku_json'], true);
            }
            redis($key, $data, 60);
        }
        return $data;
    }

    /**
     * 限时折扣 通过商品ID+上下架渠道去做
     * @param $commodity_id
     * @return array|bool|mixed
     */
    public function _limit($commodity_id, $limit_id = '', $user_id = '', $channel_type = '', $goods_price = 0, $work_time_price = 0, $count = 1, $limit_sku = '', $use_commodity_dis = 1)
    {
        $order_model = new BuOrder();
        if ($user_id) {
            $this->user_id = $user_id;
        }
        if ($channel_type) {
            $this->channel_type = $channel_type;
        }

        $time_now = date("Y-m-d H:i:s");
        $limit_model = new DbLimitDiscount();
        $l_where = [
            'a.start_time' => ['<=', $time_now],
            'a.end_time' => ['>=', $time_now],
            'b.commodity_id' => $commodity_id
        ];
        if ($limit_id || $limit_id === 0) {
            $l_where['a.id'] = $limit_id;
        }

        $segment_info = get_user_segment_info();
        $membership = $segment_info['membership_level'];
        $owner = $segment_info['brand_owner_label'];
        $_l_where = sprintf("FIND_IN_SET('%s',a.up_down_channel_dlr) and (a.user_segment=0 or (a.user_segment=1 and FIND_IN_SET('%s',a.user_segment_options)) or (a.user_segment=2 and FIND_IN_SET('%s',a.user_segment_options)))", $this->channel_type, $membership, $owner);
        $limit_info = $limit_model->getGroupInfo(['where' => $l_where, 'field' => "a.id,a.title,a.start_time,a.end_time,a.purchase_number,a.des,a.discount_type,a.dis_type,a.discount,a.discount_type,a.settlement_rule_type,a.settlement_rule_value,a.settlement_rule_id,a.act_status,a.is_enable,b.sku_price,b.sku_dis,a.title act_name,a.e3s_activity_id,a.card_available,a.rel_card_ids,a.user_segment,a.user_segment_options,a.activity_image,a.act_sett_standard", '_where' => $_l_where]);
        $limit_stop_time = '';
        $can_number = 999;
        $commodity_dis_user_segment = 0;

        if ($limit_info) {

            if ($limit_info['user_segment'] == 1) {
                $segment_key = $membership;
                $commodity_dis_user_segment = 1;
            } else if ($limit_info['user_segment'] == 2) {
                $segment_key = $owner;
                $commodity_dis_user_segment = 2;
            }

            $old_price = $goods_price;
            if ($use_commodity_dis) {
                $net_goods = new NetGoods();
                $commodity_dis_info = $net_goods->getCommoditySegmentDiscount($commodity_id);
                if ($commodity_dis_info) {
                    $goods_price = $net_goods->getCommodityDisFinalPrice($commodity_dis_info, $goods_price);
                }
            }

            if ($limit_info['purchase_number'] == 0) {
                $limit_info['purchase_number'] = 999;
            }
            $o_g_where = ['b.limit_id' => $limit_info['id'], 'b.commodity_id' => $commodity_id, 'a.user_id' => $this->user_id, 'a.order_status' => ['NOT IN', '1,3,5,6,8,18']];
            $o_g_count = $order_model->alias('a')->join('t_bu_order_commodity b', 'a.order_code=b.order_code')->where($o_g_where)->field("sum(count) number")->find();

            $can_number = $limit_info['purchase_number'] - $o_g_count['number'];
            $limit_stop_time = date('Y/m/d H:i:s', strtotime($limit_info['end_time']));

            $limit_sku_dis = json_decode_assoc($limit_info['sku_dis']);
            $limit_info['discount'] = current($limit_sku_dis);

            if ($limit_info['user_segment']) {
                $limit_info['non_member_discount'] = $limit_info['discount']['NONE'] ?? 0;
            } else {
                $limit_info['non_member_discount'] = $limit_info['discount'];
            }

            if ($limit_info['user_segment']) {
                $limit_info['discount'] = $limit_info['discount'][$segment_key];
            }
            $limit_dis_mm = (10 - $limit_info['discount']) / 10;

            $limit_info['goods_after_price'] = $goods_price;
            $limit_info['goods_after_price_all'] = round_bcmul($goods_price, $count);
            $limit_info['goods_dis_all'] = 0;
            $limit_info['goods_dis'] = 0;

            $limit_info['original_goods_after_price'] = $old_price;
            $limit_info['original_goods_after_price_all'] = round_bcmul($old_price, $count);
            $limit_info['original_goods_dis_all'] = 0;
            $limit_info['original_goods_dis'] = 0;

            $limit_info['work_time_after_price'] = $work_time_price; // 工时优惠后单价
            $limit_info['work_time_after_price_all'] = round_bcmul($work_time_price, $count); // 工时优惠后单价*数量后
            $limit_info['work_dis_all'] = 0; // 工时优惠*数量后
            $limit_info['work_dis'] = 0; // 工时优惠

            if ($goods_price > 0 || $old_price > 0) {
                // 工时需要计算1仅商品；2仅工时；3商品+工时
                if (in_array($limit_info['discount_type'], [1, 3])) {
                    if ($limit_sku) {
                        if (isset($limit_sku_dis[$limit_sku])) {
                            $sku_dis = $limit_sku_dis[$limit_sku];
                            if ($limit_info['user_segment']) {
                                $sku_dis = $limit_sku_dis[$limit_sku][$segment_key];
                            }
                            $limit_dis_mm = (10 - $sku_dis) / 10;
                            if ($limit_info['dis_type'] == 1) {
                                $limit_info['goods_dis'] = round_bcmul($goods_price, $limit_dis_mm);//商品优惠金额
                                $limit_info['original_goods_dis'] = round_bcmul($old_price, $limit_dis_mm);//商品优惠金额
                            } else {
                                $limit_info['goods_dis'] = $limit_info['original_goods_dis'] = $sku_dis;
                            }
                        }
                    } else {
                        if ($limit_info['dis_type'] == 1) {
                            $limit_info['goods_dis'] = round_bcmul($goods_price, $limit_dis_mm);//商品优惠金额
                            $limit_info['original_goods_dis'] = round_bcmul($old_price, $limit_dis_mm);//商品优惠金额
                        } else {
                            $limit_info['goods_dis'] = $limit_info['original_goods_dis'] = $limit_info['discount'];
                        }
                    }
                    $limit_info['goods_after_price'] = price_fmt(max(0, round_bcsub($goods_price, $limit_info['goods_dis'])));//商品优惠后单价
                    $limit_info['goods_after_price_all'] = round_bcmul($limit_info['goods_after_price'], $count);//商品优惠价格*数量后
                    $limit_info['goods_dis_all'] = round_bcmul($limit_info['goods_dis'] ,$count);//商品优惠金额*数量后

                    $limit_info['original_goods_after_price'] = price_fmt(max(0, round_bcsub($old_price, $limit_info['original_goods_dis'])));
                    $limit_info['original_goods_after_price_all'] = round_bcmul($limit_info['original_goods_after_price'], $count);
                    $limit_info['original_goods_dis_all'] = round_bcmul($limit_info['original_goods_dis'], $count);
                }
            }

            if ($work_time_price && $work_time_price > 0) {

                if (in_array($limit_info['discount_type'], [2, 3])) {
                    if ($limit_sku) {
                        if (isset($limit_sku_dis[$limit_sku])) {
                            $sku_dis = $limit_sku_dis[$limit_sku];
                            if ($limit_info['user_segment']) {
                                $sku_dis = $limit_sku_dis[$limit_sku][$segment_key];
                            }
                            $limit_dis_mm = (10 - $sku_dis) / 10;

                            if ($limit_info['dis_type'] == 1) {
                                $limit_info['work_dis'] = round_bcmul($work_time_price, $limit_dis_mm);//商品优惠金额
                            } else {
                                $limit_info['work_dis'] = $sku_dis;
                            }
                        } else {
                            if ($limit_info['dis_type'] == 1) {
                                $limit_info['work_dis'] = round_bcmul($work_time_price, $limit_dis_mm);//商品优惠金额
                            } else {
                                $limit_info['work_dis'] = $limit_info['discount'];
                            }
                        }
                    } else {
                        if ($limit_info['dis_type'] == 1) {
                            $limit_info['work_dis'] = round_bcmul($work_time_price, $limit_dis_mm);
                        } else {
                            $limit_info['work_dis'] = $limit_info['discount'];
                        }
                    }
                    $limit_info['work_time_after_price'] = round_bcsub($work_time_price, $limit_info['work_dis']); // 工时优惠后单价
                    $limit_info['work_time_after_price_all'] = round_bcmul($limit_info['work_time_after_price'], $count); // 工时优惠后单价*数量后
                    $limit_info['work_dis_all'] = round_bcmul($limit_info['work_dis'], $count); // 工时优惠*数量后
                }
            }
        }
        return ['stop_time' => $limit_stop_time, 'can_no' => $can_number, 'limit_info' => $limit_info, 'user_segment' => $commodity_dis_user_segment];
    }

    /**
     * 秒杀
     */
    public function _seckill($commodity_id, $seckill_id = '', $user_id = '', $channel_type = '', $goods_price = 0, $work_time_price = 0, $count = 1, $seckill_sku = '', $use_commodity_dis = 1)
    {
        $time_now = date("Y-m-d H:i:00");
        $order_model = new BuOrder();
        if ($user_id) {
            $this->user_id = $user_id;
        }
        if ($channel_type) {
            $this->channel_type = $channel_type;
        }
        $kill_count = 0;
        $screening = '';
        $seckill_model = new DbSeckill();
        $l_where = [
            'b.commodity_id' => $commodity_id
        ];
        if ($seckill_id || $seckill_id === 0) {
            $l_where['a.id'] = $seckill_id;
        }
        $segment_info = get_user_segment_info();
        $membership = $segment_info['membership_level'];
        $owner = $segment_info['brand_owner_label'];
        $_l_where = sprintf("FIND_IN_SET('%s',a.up_down_channel_dlr) and (a.user_segment=0 or (a.user_segment=1 and FIND_IN_SET('%s',a.user_segment_options)) or (a.user_segment=2 and FIND_IN_SET('%s',a.user_segment_options)))", $this->channel_type, $membership, $owner);
        $field = 'a.id,a.title,a.start_time,a.end_time,a.purchase_number,a.des,a.discount_type,a.dis_type,a.discount,
            a.settlement_rule_type,a.settlement_rule_value,a.settlement_rule_id,a.act_status,a.is_enable,b.sku_price,b.sku_dis,
            a.title act_name,a.e3s_activity_id,a.card_available,a.rel_card_ids,a.seckill_type,a.day_start_time,a.day_end_time,
            a.purchase_commodity_scope,a.purchase_activity_scope,a.user_segment,a.user_segment_options,a.activity_image,a.act_sett_standard';
        $seckill_info = $seckill_model->getGroupInfo(['where' => $l_where, 'field' => $field, '_where' => $_l_where]);
        $seckill_stop_time = '';
        $can_number      = 999;
        $commodity_dis_user_segment = 0;

        if ($seckill_info) {

                if ($seckill_info['user_segment'] == 1) {
                    $segment_key = $membership;
                    $commodity_dis_user_segment = 1;
                } else if ($seckill_info['user_segment'] == 2) {
                    $segment_key = $owner;
                    $commodity_dis_user_segment = 2;
                }

                $old_price = $goods_price;
                if ($use_commodity_dis) {
                    $net_goods = new NetGoods();
                    $commodity_dis_info = $net_goods->getCommoditySegmentDiscount($commodity_id);
                    if ($commodity_dis_info) {
                        $goods_price = $net_goods->getCommodityDisFinalPrice($commodity_dis_info, $goods_price);
                    }
                }


                // 判断是否有限购数量
                if ($seckill_info['purchase_number'] == 0) {
                    // 不限购
                    $seckill_info['purchase_number'] = 999;
                }
                // 判断秒杀活动类型
                //  固定时间
                if ($seckill_info['seckill_type'] == 1) {
                    if($seckill_info['start_time']<=$time_now && $seckill_info['end_time']>=$time_now){
                        $seckill_info['act_status']=2;
                    }
                    if($seckill_info['start_time']>$time_now){
                        $seckill_info['act_status']=1;
                    }
                    if($seckill_info['end_time']<$time_now){
                        $seckill_info['act_status']=3;
                    }
                    $seckill_info['preview_time'] = date('n月j日 H:i:s', strtotime($seckill_info['start_time']));
                    $seckill_stop_time  = date('Y/m/d H:i:s', strtotime($seckill_info['end_time']));

                } else {
                    $current_time = date('Y-m-d H:i:s');
                    // 每天重复
                    // 活动开始时间
                    $start_time = date('Y-m-d ',strtotime($seckill_info['start_time'])). $seckill_info['day_start_time'];
                    // 活动结束时间
                    $end_time = date('Y-m-d ',strtotime($seckill_info['end_time'])). $seckill_info['day_end_time'];
                    // 第一天秒杀
                    if ($current_time < $start_time) {
                        $sm_start_time = $start_time;
                        $sm_end_time = date('Y-m-d ',strtotime($seckill_info['start_time'])).$seckill_info['day_end_time'];
                        $act_status=1; // 未开始

                    }elseif ($end_time < $current_time) {
                        $sm_start_time = date('Y-m-d ',strtotime($seckill_info['end_time'])).$seckill_info['day_start_time'];
                        $sm_end_time = $end_time;
                        $act_status = 3; // 已结束
                    } else {
                        $current_time = date('H:i:s');
                        if (($seckill_info['day_start_time'] < $current_time) && ($current_time < $seckill_info['day_end_time'])) {
                            $act_status = 2; // 进行中
                            $sm_start_time = date('Y-m-d ').$seckill_info['day_start_time'];
                            $sm_end_time = date('Y-m-d '). $seckill_info['day_end_time'];
                        } else {
                            $act_status = 1; // 未开始
                            // 当天
                            if ($current_time < $seckill_info['day_start_time']) {
                                $sm_start_time = date('Y-m-d ').$seckill_info['day_start_time'];
                                $sm_end_time = date('Y-m-d '). $seckill_info['day_end_time'];
                            } else {
                                // 次日
                                $sm_start_time = date('Y-m-d ', strtotime(' +1 day')).$seckill_info['day_start_time'];
                                $sm_end_time = date('Y-m-d ', strtotime(' +1 day')). $seckill_info['day_end_time'];
                            }
                        }
                    }
                    $seckill_info['act_status'] = $act_status;
                    $seckill_info['sm_start_time'] = $sm_start_time;
                    $seckill_info['sm_end_time'] = $sm_end_time;
                    $seckill_info['preview_time'] = date('n月j日 H:i:s', strtotime($sm_start_time));
                    $seckill_stop_time  = date('Y/m/d H:i:s', strtotime($sm_end_time));
                }
                $o_g_where = ['b.seckill_id' => $seckill_info['id'], 'a.user_id' => $this->user_id, 'b.mo_sub_id' => 0, // 判断主商品和普通商品
                              'a.order_status' => ['NOT IN', '1,3,5,6,8,18']];//不包括1、已下单；3、已取消；5、已退款；6、已过期；8、未支付；18、交易关闭
                // 判断限购的类型
                if ($seckill_info['purchase_number'] > 0) {
                    // 判断限购商品范围
                    // 单个商品
                    if ($seckill_info['purchase_commodity_scope'] == 1) {
                        $o_g_where['b.commodity_id'] = $commodity_id;
                    }

                    // 判断重复秒杀
                    if (($seckill_info['seckill_type'] == 2) && ($seckill_info['purchase_activity_scope'] == 1)) {
                        // 判断限购活动范围  单场
                        $o_g_where['date(b.created_date)'] = date('Y-m-d');
                    }
                }
            // 已购买数量
            $o_g_count = $order_model->alias('a')->join('t_bu_order_commodity b', 'a.order_code=b.order_code')->where($o_g_where)->field("sum(count) number")->find();
            //秒杀缓存库存
            if ($seckill_info['seckill_type'] == 2) {
                $time = date('H:i:s');
                if ($time > $seckill_info['day_end_time']) {
                    // 明天场次
                    $screening = date("Y-m-d", strtotime("+1 day"));
                } else {
                    // 当天场次
                    $screening = date('Y-m-d');
                }
            }
            $kill_count = $this->kill_count($seckill_info['id'], $commodity_id, $screening);

            //判断是否黑名单
            $back_list_model = new AcBlacklist();
            $back_info = $back_list_model->getOne(['where' => ['user_id' => $this->user_id, 'activity_type' => 1, 'is_enable' => 1]]);
            $can_number = $seckill_info['purchase_number'] - $o_g_count['number'];
            if ($back_info || $can_number < 0) {
                $can_number = 0;
            }

            $seckill_stop_time = date('Y/m/d H:i:s', strtotime($seckill_info['end_time']));
            $seckill_sku_dis = json_decode_assoc($seckill_info['sku_dis']);
            $seckill_info['discount'] = current($seckill_sku_dis);
            if ($seckill_info['user_segment']) {
                $seckill_info['non_member_discount'] = $seckill_info['discount']['NONE'] ?? 0;
            } else {
                $seckill_info['non_member_discount'] = $seckill_info['discount'];
            }

            if ($seckill_info['user_segment']) {
                $seckill_info['discount'] = $seckill_info['discount'][$segment_key];
            }
            $limit_dis_mm = (10 - $seckill_info['discount']) / 10;

            $seckill_info['goods_after_price'] = $goods_price;
            $seckill_info['goods_after_price_all'] = round_bcmul($goods_price, $count);
            $seckill_info['goods_dis_all'] = 0;
            $seckill_info['goods_dis'] = 0;

            $seckill_info['original_goods_after_price'] = $old_price;
            $seckill_info['original_goods_after_price_all'] = round_bcmul($old_price, $count);
            $seckill_info['original_goods_dis_all'] = 0;
            $seckill_info['original_goods_dis'] = 0;

            $seckill_info['work_time_after_price'] = $work_time_price; // 工时优惠后单价
            $seckill_info['work_time_after_price_all'] = round_bcmul($work_time_price, $count); // 工时优惠后单价*数量后
            $seckill_info['work_dis_all'] = 0; // 工时优惠*数量后
            $seckill_info['work_dis'] = 0; // 工时优惠

            if ($goods_price > 0 || $old_price > 0) {
                //工时需要计算1仅商品；2仅工时；3商品+工时
                if (in_array($seckill_info['discount_type'], [1, 3])) {
                    if ($seckill_sku) {
                        if (isset($seckill_sku_dis[$seckill_sku])) {
                            $sku_dis = $seckill_sku_dis[$seckill_sku];
                            if ($seckill_info['user_segment']) {
                                $sku_dis = $seckill_sku_dis[$seckill_sku][$segment_key];
                            }
                            $limit_dis_mm = (10 - $sku_dis) / 10;

                            if ($seckill_info['dis_type'] == 1) {
                                $seckill_info['goods_dis'] = round_bcmul($goods_price, $limit_dis_mm);
                                $seckill_info['original_goods_dis'] = round_bcmul($old_price, $limit_dis_mm);
                            } else if ($seckill_info['dis_type'] == 2) {
                                $seckill_info['goods_dis'] = $seckill_info['original_goods_dis'] = $sku_dis;
                            } else {
                                // 秒杀一口价
                                $seckill_info['goods_dis'] = $goods_price - $sku_dis;
                                $seckill_info['original_goods_dis'] = $old_price - $sku_dis;
                            }
                        }
                    } else {
                        if ($seckill_info['dis_type'] == 1) {
                            $seckill_info['goods_dis'] = round_bcmul($goods_price, $limit_dis_mm);
                            $seckill_info['original_goods_dis'] = round_bcmul($old_price, $limit_dis_mm);
                        } elseif ($seckill_info['dis_type'] == 2) {
                            $seckill_info['goods_dis'] = $seckill_info['original_goods_dis'] = $seckill_info['discount'];
                        } else {
                            // 秒杀一口价
                            $seckill_info['goods_dis'] = $goods_price - $seckill_info['discount'];
                            $seckill_info['original_goods_dis'] = $old_price - $seckill_info['discount'];
                        }
                    }
                    $seckill_info['goods_after_price'] = price_fmt(max(0, round_bcsub($goods_price, $seckill_info['goods_dis'])));//商品优惠后单价
                    $seckill_info['goods_after_price_all'] = round_bcmul($seckill_info['goods_after_price'], $count);//商品优惠价格*数量后
                    $seckill_info['goods_dis_all'] = round_bcmul($seckill_info['goods_dis'], $count);//商品优惠金额*数量后

                    $seckill_info['original_goods_after_price'] = price_fmt(max(0, round_bcsub($old_price, $seckill_info['original_goods_dis'])));
                    $seckill_info['original_goods_after_price_all'] = round_bcmul($seckill_info['original_goods_after_price'], $count);
                    $seckill_info['original_goods_dis_all'] = round_bcmul($seckill_info['original_goods_dis'], $count);
                }
            }
            if ($work_time_price && $work_time_price > 0) {
                if (in_array($seckill_info['discount_type'], [2])) {
                    if ($seckill_sku) {
                        if (isset($seckill_sku_dis[$seckill_sku])) {
                            $sku_dis = $seckill_sku_dis[$seckill_sku];
                            if ($seckill_info['user_segment']) {
                                $sku_dis = $seckill_sku_dis[$seckill_sku][$segment_key];
                            }
                            $limit_dis_mm = (10 - $sku_dis) / 10;
                        }
                    }
                    $seckill_info['work_dis'] = round_bcmul($work_time_price, $limit_dis_mm);

                    $seckill_info['work_time_after_price'] = round_bcsub($work_time_price, $seckill_info['work_dis']); // 工时优惠后单价
                    $seckill_info['work_time_after_price_all'] = round_bcmul($seckill_info['work_time_after_price'], $count); // 工时优惠后单价*数量后
                    $seckill_info['work_dis_all'] = round_bcmul($seckill_info['work_dis'], $count); // 工时优惠*数量后
                }
            }

            //活动未开始、已结束 改为999
            if (in_array($seckill_info['act_status'], [1, 3]) || ($kill_count == 0 && in_array($seckill_info['act_status'], [1, 2]))) {
                $can_number = 999;
            }
        }
        return ['stop_time' => $seckill_stop_time, 'can_no' => $can_number, 'seckill_info' => $seckill_info, 'kill_count' => $kill_count, 'screening' => $screening, 'user_segment' => $commodity_dis_user_segment];
    }


    public function _gift_act($act_id, $user_id = '', $channel_type = '')
    {
        $order_model = new BuOrder();
        if ($user_id) {
            $this->user_id = $user_id;
        }
        if ($channel_type) {
            $this->channel_type = $channel_type;
        }
        $key = 'detail_gift_act_' . $this->channel_type . $this->user_id . $act_id;
        $data = redis($key);

        if (empty($data)) {
            $gift_act_model = new DbGift();
            $now = date("Y-m-d H:i:s");
            $l_where = [
                'id' => $act_id,
                'is_enable' => 1,
                'start_time' => ["<=", $now],
                'end_time' => [">=", $now]
            ];
            $_l_where = sprintf("FIND_IN_SET('%s',a.up_down_channel_dlr) ", $this->channel_type);
            $gift_info = $gift_act_model->getActInfo(['where' => $l_where, '_where' => $_l_where]);
            $gift_act_com_model = new DbGiftCommodity();
            $gift_commodities = $gift_act_com_model->getList(['where' => ['gift_id' => $act_id]]);
            $gift_commodities_attr = [];
            foreach ($gift_commodities as $gift_c) {
                $gift_commodities_attr[$gift_c['commodity_id'] .'_'. $gift_c['is_gift']] = $gift_c;
            }

            $can_number = 999;
            if ($gift_info) {
                $o_g_where = [
                    'b.gift_act_id' => $gift_info['id'],
                    'b.is_gift' => 0,
                    'a.user_id' => $this->user_id,
                    'a.order_status' => ['NOT IN', '1,3,5,6,8,18']];//不包括1、已下单；3、已取消；5、已退款；6、已过期；8、未支付；18、交易关闭
                $o_g_count = $order_model->alias('a')
                    ->join('t_bu_order_commodity b', 'a.order_code=b.order_code')
                    ->where($o_g_where)
                    ->field("sum(count) number")->find();

                if ($gift_info['purchase_number'] != 0) {
                    $can_number = $gift_info['purchase_number'] - $o_g_count['number'];
                }
            }

            $data = [
                'gift_info' => $gift_info,
                'gift_commodities' => $gift_commodities_attr,
                'can_number' => $can_number
            ];
            redis($key, $data, 5);
        }
        return $data;
    }

    /**
     * 买赠
     */
    public function _gift($commodity_id, $seckill_id = '', $user_id = '', $channel_type = '')
    {
        $time_now    = date("Y-m-d H:i:00");
        $order_model = new BuOrder();
        if ($user_id) {
            $this->user_id = $user_id;
        }
        if ($channel_type) {
            $this->channel_type = $channel_type;
        }

        $gift_model = new DbGift();
        $l_where     = [
//                'a.start_time'   => ['<=', $time_now],
//                'a.end_time'     => ['>=', $time_now],
            'b.commodity_id' => $commodity_id
        ];
        if ($seckill_id || $seckill_id === 0) {
            $l_where['a.id'] = $seckill_id;
        }
        $gift_stop_time = '';
        $segment_info = get_user_segment_info();
        $membership = $segment_info['membership_level'];
        $owner = $segment_info['brand_owner_label'];
        $_l_where   = sprintf("FIND_IN_SET('%s',a.up_down_channel_dlr) and (a.user_segment=0 or (a.user_segment=1 and FIND_IN_SET('%s',a.user_segment_options)) or (a.user_segment=2 and FIND_IN_SET('%s',a.user_segment_options)))", $this->channel_type, $membership, $owner);
        $gift_info = $gift_model->getGroupInfo(['where' => $l_where, 'field' => "a.id,a.title,a.start_time,a.end_time,a.purchase_number,a.des,a.act_status,a.is_enable,a.optional_number,b.sku_price,a.title act_name,a.user_segment,a.user_segment_options,a.activity_image", '_where' => $_l_where]);
        $can_number      = 999;
        if ($gift_info) {
            $gift_info['rule'] = $gift_info['des'];
            if ($gift_info['purchase_number'] == 0) {
                $gift_info['purchase_number'] = 999;
            }
            if($gift_info['start_time']<=$time_now && $gift_info['end_time']>=$time_now){
                $gift_info['act_status']=2;
            }
            if($gift_info['start_time']>$time_now){
                $gift_info['act_status']=1;
            }
            if($gift_info['end_time']<$time_now){
                $gift_info['act_status']=3;
            }
            $o_g_where                               = [
                'b.gift_act_id' => $gift_info['id'],
//                'b.commodity_id' => $commodity_id,
                'b.is_gift' => 0,
                'a.user_id' => $this->user_id,
                'a.order_status' => ['NOT IN', '1,3,5,6,8,18']];//不包括1、已下单；3、已取消；5、已退款；6、已过期；8、未支付；18、交易关闭
            $o_g_count = $order_model->alias('a')
                ->join('t_bu_order_commodity b', 'a.order_code=b.order_code')
                ->where($o_g_where)
                ->field("sum(count) number")->find();

            if ($gift_info['purchase_number'] != 0) {
                $can_number = $gift_info['purchase_number'] - $o_g_count['number'];
            }
            $gift_info['is_participate'] = 0;
            if($can_number <= 0){
                $gift_info['purchase_number'] = 0;
                $gift_info['is_participate'] = 1;
            }
            $gift_stop_time                       = date('Y/m/d H:i:s', strtotime($gift_info['end_time']));
            $p_where['b.gift_id'] = $gift_info['id'];
            $p_where['b.is_gift'] = 1;
            $p_where[] = ['exp',sprintf(" FIND_IN_SET('%s',a.up_down_channel_dlr)",$this->channel_type)];


            $gift_info_img = $gift_model->getLiveSeckillCommodityList(['where' => $p_where, 'field' => "c.cover_image,b.commodity_id",'group'=>'b.commodity_id']);
            $gift_info_img = $gift_info_img->toArray();
            $gift_arr = [];
            $optional_number = 0;
            foreach ($gift_info_img['data'] as $key=>$val){
                $optional_number +=1;
                $gift_arr[$key]['commodity_id'] = $val['commodity_id'];
                $gift_arr[$key]['cover_image'] = $val['cover_image'];

            }
            if($gift_info['optional_number'] > $optional_number){
                $gift_info['optional_number'] = $optional_number;
            }
            $gift_info['gift_imgs_arr'] = $gift_arr;
            $gift_info['start_time'] = date("Y-m-d",strtotime($gift_info['start_time']));
            $gift_info['end_time'] = date("Y-m-d",strtotime($gift_info['end_time']));
        }
        return ['stop_time' => $gift_stop_time, 'can_no' => $can_number, 'gift_info' => $gift_info];
    }



    /**
     * 限时折扣-工时 通过商品ID+上下架渠道去做
     * @param $commodity_id
     * @return array|bool|mixed
     */
    public function _limit_wi($commodity_id, $limit_id = '', $user_id = '', $channel_type = '', $work_time_price = 0, $count = 1, $limit_sku = '')
    {
        $time_now = date("Y-m-d H:i:00");
        $order_model = new BuOrder();
        if ($user_id) {
            $this->user_id = $user_id;
        }
        if ($channel_type) {
            $this->channel_type = $channel_type;
        }

        $limit_model = new DbLimitDiscount();
        $l_where = [
            'a.start_time' => ['<=', $time_now],
            'a.end_time' => ['>=', $time_now],
            'b.commodity_id' => $commodity_id
        ];
        if ($limit_id || $limit_id === 0) {
            $l_where['a.id'] = $limit_id;
        }
        $_l_where = sprintf("FIND_IN_SET('%s',a.up_down_channel_dlr) ", $this->channel_type);
        $limit_info = $limit_model->getGroupInfo(['where' => $l_where, 'field' => "a.id,a.title,a.start_time,a.end_time,a.purchase_number,a.des,a.discount_type,a.dis_type,a.discount,a.discount_type,a.settlement_rule_type,a.settlement_rule_value,a.settlement_rule_id,b.sku_price,b.sku_dis,a.title act_name,a.e3s_activity_id,a.card_available,a.activity_image", '_where' => $_l_where]);

        $limit_stop_time = '';
        $can_number = 999;

        if ($limit_info) {
            if ($limit_info['purchase_number'] == 0) {
                $limit_info['purchase_number'] = 999;
            }
            $o_g_where = ['b.limit_wi_id' => $limit_info['id'], 'b.commodity_id' => $commodity_id, 'a.user_id' => $this->user_id, 'a.order_status' => ['NOT IN', '1,3,5,6,8,18']];//不包括1、已下单；3、已取消；5、已退款；6、已过期；8、未支付；18、交易关闭
            $o_g_count = $order_model->alias('a')->join('t_bu_order_commodity b', 'a.order_code=b.order_code')->where($o_g_where)->field("sum(count) number")->find();

            $can_number = $limit_info['purchase_number'] - $o_g_count['number'];
            $limit_stop_time = date('Y/m/d H:i:s', strtotime($limit_info['end_time']));

            $limit_sku_dis = json_decode($limit_info['sku_dis'], true);
            if (!empty($limit_sku_dis)) {
                $limit_info['discount'] = current($limit_sku_dis);
            }
            $limit_dis_mm = (10 - $limit_info['discount']) / 10;

            $limit_info['work_time_after_price'] = $work_time_price; // 工时优惠后单价
            $limit_info['work_time_after_price_all'] = $work_time_price * $count; // 工时优惠后单价*数量后
            $limit_info['work_dis_all'] = 0; // 工时优惠*数量后
            $limit_info['work_dis'] = 0; // 工时优惠

            $limit_sku_price = json_decode($limit_info['sku_price'], true);

            $enable_sku = 1;
            if ($limit_sku && !isset($limit_sku_price[$limit_sku])) {
                $enable_sku = 0;
            }

            if ($work_time_price > 0 && $enable_sku) {
                if ($limit_info['dis_type'] == 1) {
                    if (isset($limit_sku_dis[$limit_sku])) {
                        $limit_dis_mm = (10 - $limit_sku_dis[$limit_sku]) / 10;
                    }
                    $limit_info['work_dis'] = round($work_time_price * $limit_dis_mm, 2);
                } else {
                    if (isset($limit_sku_dis[$limit_sku])) {
                        $limit_info['work_dis'] = $limit_sku_dis[$limit_sku];
                    } else {
                        $limit_info['work_dis'] = $limit_info['discount'];
                    }
                }
                $limit_info['work_time_after_price'] = round($work_time_price - $limit_info['work_dis'], 2); // 工时优惠后单价
                $limit_info['work_time_after_price_all'] = round($limit_info['work_time_after_price'] * $count, 2); // 工时优惠后单价*数量后
                $limit_info['work_dis_all'] = round($limit_info['work_dis'] * $count, 2); // 工时优惠*数量后
            }
        }
        return ['stop_time' => $limit_stop_time, 'can_no' => $can_number, 'limit_info' => $limit_info];
    }

    /**
     * NN促销
     * @param $com_set_id
     * @return array|bool|false|mixed|\PDOStatement|string|\think\Model|null
     */
    protected function _nn($com_id)
    {
        $time_now = date("Y-m-d H:i:00");

        $segment_info = get_user_segment_info();
        $membership = $segment_info['membership_level'];
        $owner = $segment_info['brand_owner_label'];

        $key  = 'detail_nn_' . $this->channel_type . $com_id . $membership . $owner . $time_now;
        $data = redis($key);

        if (empty($data)) {
            $n_dis_goods_model = new DbNDiscountCommodity();
            $where             = [
                'b.start_time'   => ['<=', $time_now],
                'b.end_time'     => ['>=', $time_now],
                'b.is_enable'    => 1,
                'a.is_enable'    => 1,
                "a.commodity_id" => $com_id
            ];
            $_l_where   = sprintf("FIND_IN_SET('%s',b.up_down_channel_dlr) and (b.user_segment=0 or (b.user_segment=1 and FIND_IN_SET('%s',b.user_segment_options)) or (b.user_segment=2 and FIND_IN_SET('%s',b.user_segment_options)))", $this->channel_type, $membership, $owner);
            $param['_where'] = $_l_where;
            $param['where'] = $where;
            $param['field'] = "a.n_id,a.commodity_id,b.title,b.des,b.user_segment,b.user_segment_options,b.activity_image,b.start_time,b.end_time";
            $row            = $n_dis_goods_model->getNdisInfo($param);
            if ($row) {
                $row['commodity_dis_act_user_segment'] = $row['user_segment'];
                $row['commodity_dis_label'] = get_user_segment_label($row['user_segment']);
                $row['commodity_dis_label_cn'] = get_user_segment_label_cn($row['commodity_dis_label']);

                $n_dis_info_model =  new DbNDiscountInfo();
                $n_dis_info_where = ['n_id'=>$row['n_id'],'is_enable'=>1];
                if($row['user_segment']==1){
                    $n_dis_info_where['segment_label'] = $owner;
                }elseif($row['user_segment']==2){
                    $n_dis_info_where['segment_label'] = $membership;
                }
                $n_dis_info_list =  $n_dis_info_model->getList(['where'=>$n_dis_info_where,'order'=>'piece asc']);
                $rule_info =  '';
                if($n_dis_info_list){
                    foreach ($n_dis_info_list as $n_v){
                        $rule_info.=sprintf("%s件%s折，",$n_v['piece'],$n_v['discount']);
                    }
                    $rule_info = trim($rule_info,"，");
                }
                $row['rule'] = $rule_info;
                $row['description'] = $row['des'];



            }
            $data           = $row;
            redis($key, $data, 60);
        }

        return $data;
    }


    /**
     * 选择优惠券对应商品优惠信息
     * @param $cards 多个
     * @param $order_code
     * @return false|\PDOStatement|string|\think\Collection
     */
    protected function _goods_card_info($cards, $order_code)
    {
        $order_goods_model = new BuOrderCommodity();
        $where             = ['a.order_code' => $order_code, 'b.id' => ['in', $cards]];
        $where[]           = ['exp', sprintf("FIND_IN_SET('%s',apply_dlr_code)", $this->channel_type)];
        $field             = "a.commodity_id,a.count,c.card_type,c.card_quota,c.card_discount,c.least_cost,a.price,c.id card_id";
        $list              = $order_goods_model->orderGoodsCard(['where' => $where, "field" => $field]);
        $list_group        = $order_goods_model->orderGoodsCard(['where' => $where, "field" => "c.id card_id,sum(c.price*a.count) as s_money", 'group' => "c.id"]);
        if ($list) {
            foreach ($list as $k => $v) {
                if ($v['type'] == 1) {
                    foreach ($list_group as $vv) {
                        if ($vv['card_id'] == $v['card_id']) {
//                            $list[$k]['all_money'] =$vv['s_money'];
                            $list[$k]['yh_money'] = round($v['price'] / ($vv['s_money']) * $v['card_quota'], 2);
//                            $list[$k]['yh_dj_money'] =  round($v['price']/($vv['s_money'])*$v['card_quota'],2);
                        }
                    }
                } else {
                    $list[$k]['yh_money'] = round($v['price'] * (1 - $v['card_discount']), 2);
                }
            }
        }
        return $list;
    }

    /**
     * 订单号对应能使用哪些卡券
     * @param $order_code
     * @return false|\PDOStatement|string|\think\Collection
     */
    protected function _order_goods_use_card($order_code)
    {
        $order_goods_model = new BuOrderCommodity();
        $where             = ['a.order_code' => $order_code, 'e.user_id' => $this->user_id];
        $where[]           = ['exp', sprintf("FIND_IN_SET('%s',apply_dlr_code)", $this->channel_type)];
        $field             = "c.*";
        $list              = $order_goods_model->orderGoodsUseCard(['where' => $where, "field" => $field]);
        $card_list         = [];
        if ($list) {
            $card_list = $this->_checkUserCard($list, 2);
            if ($card_list) {
                foreach ($card_list as $k => $v) {
                    if ($v['available_count'] < 1) {
                        unset($card_list[$k]);
                    } else {
                        $card_list[$k]['card_date'] = $v['validity_date_start'] . '~' . $v['validity_date_end'];
                    }
                }
            }
        }
        return $card_list;
    }


    /**
     * 商品对应领取那些卡券,隔开多个
     * @param $commodity_ids
     * @param int $is_get 增加一个2为了不返回库存《1的的，计算不至于异常
     * @return array
     */
    protected function _goods_can_get_card($commodity_ids, $user_id, $channel_type, $is_get = 1,$sku_ids=[],$sub_goods_id='')
    {
        //卡券领取部分，要改成通过活动中心接口了
        //8.商品卡券信息
        $goods_card_where = [
            "b.commodity_set_id" => ['in', $commodity_ids],
        ];
        if ($is_get == 1) {
            $goods_card_where['b.is_can_receive'] = 1;
        }
        if($sub_goods_id){
//            $goods_card_where['b.group_sub_commodity_id'] = $sub_goods_id;
            $goods_card_where[] = ['exp',sprintf("b.group_sub_commodity_id=%s or b.group_sub_commodity_id=''",$sub_goods_id)];
        }


        //领取卡券增加渠道  20220110 CP&TCP
        $goods_card_where[] = ['exp', sprintf("FIND_IN_SET('%s', a.up_down_channel_dlr)", $channel_type)];

        $goods_card = $this->_commodityCard($goods_card_where, "a.*,b.is_can_receive,GROUP_CONCAT(b.group_sub_commodity_id) as group_sub_commodity_id,GROUP_CONCAT(b.set_sku_ids) as set_sku_ids,b.group_card_type");
//        print_json($goods_card);


        if($sku_ids){
            foreach ($goods_card as $card_k=> $card_v){
                if($card_v['set_sku_ids']){
                    $card_sku_id =  explode(",",$card_v['set_sku_ids']);
                    if(!is_array($sku_ids)){
                        $sku_ids=[$sku_ids];
                    }
                    if(!array_intersect($card_sku_id,$sku_ids)){
                        unset($goods_card[$card_k]);
                    }
                }
            }
        }
        //组合商品时也要将sku拿出来


        $card_list  = [];
        if ($goods_card && $user_id) {
            if (in_array($channel_type, $this->pz_dlr_code_arr)) {
                $card_list = $this->_checkUserCard($goods_card, 1, $user_id);
            } else {
                $card_list = $this->_checkUserCardV1($goods_card, 1, $user_id);
            }
//            print_json($card_list);
            if ($card_list) {
                foreach ($card_list as $k => $v) {
                    $card_list[$k]['can_get']=0;
                    $card_list[$k]['id'] = (string)$v['id'];
                    if ($v['available_count'] < 1 && (in_array($channel_type, $this->pz_dlr_code_arr) || $is_get==2)) {
                        unset($card_list[$k]);
                    } else {
//                        if(($v['is_can_receive']==1 && strtotime($v['validity_date_end'])>time())  || $v['can_use_card']>0){
                        if(($v['is_can_receive']==1)  || $v['can_use_card']>0){
                            $card_list[$k]['card_date'] = $v['validity_date_start'] . '~' . $v['validity_date_end'];
                            if($v['available_quantity']>0 && $v['available_count']>0){
                                $card_list[$k]['can_get'] =1;
                            }
                        }else{
                            unset($card_list[$k]);
                        }
                    }
                }
            }
        }
        $card_list = array_values($card_list);

        return $card_list;
    }

    /**
     * @param $user_id
     * @return array
     * 用户所有领取的卡券，不关联任何其他原因。
     */
    public function _userAllCard($user_id, $card_ids = [],$channel_type='',$card_codes = [],$chose_card_codes=[],$order_vin='')
    {
        $card_r_model = new BuCardReceiveRecord();
        //((rec.user_id=od.user_id and rec.user_id>0) or (rec.user_id=0 and rec.receive_vin = od.order_vin))
        $where        = [ 'a.validity_date_end'=>['>',date('Y-m-d H:i:s')],'a.coupon_code'=>['<>','']];
//        $where[]      = ['exp', sprintf("FIND_IN_SET('%s',b.up_down_channel_dlr)", $channel_type)];
        //就会过滤跟人跟车
        if(!$user_id){
            return  [];
        }
        if($order_vin){
//            $where[]      = ['exp', sprintf("(a.user_id=%s  and a.user_id>0) or ( a.receive_vin in ('%s') )", $user_id,trim($order_vin,"'"))];
//            $where[]      = ['exp', sprintf("(a.user_id=%s  and a.user_id>0 and  (a.receive_vin='' or a.receive_vin is null)) or ( a.receive_vin in ('%s') and (a.user_id=%s || a.user_id=0) )", $user_id,trim($order_vin,"'"),$user_id)];
            $where[]      = ['exp', sprintf("(a.user_id=%s or a.receive_vin in ('%s') )", $user_id,trim($order_vin,"'"))];

        }else{
//            $where[]      = ['exp', sprintf("(a.user_id=%s  and a.user_id>0 and  (a.receive_vin='' or a.receive_vin is null))", $user_id)];
            $where[]      = ['exp', sprintf("(a.user_id=%s )", $user_id)];
        }
//        $where[]      = ['exp', sprintf("(a.user_id=%s )", $user_id)];
        if ($card_ids) {
            $where['a.card_id'] = ['in', $card_ids];
        }
        $user = session("net-api-user-info");
        $user_vin = '';
        //TMD要在不可用卡券里
//        if($user){
//            $user_vin = $user['vin'];
//        }
//        $where[]      = ['exp', sprintf(" a.use_vin='%s' or a.use_vin='' ", $user_vin)];
//        if($channel_type){
//            $where[]      = ['exp', sprintf("FIND_IN_SET('%s',b.up_down_channel_dlr)", $channel_type)];
//
//        }
        $where_or = [];

//        print_json($card_codes,$chose_card_codes);
        if (!empty($card_codes)) {
            //,'a.validity_date_end'=>['>',date('Y-m-d H:i:s')] 上面已经有这个了
            //这个地方是冻结卡券用
            if($chose_card_codes){
                $card_codes =  array_intersect($card_codes,$chose_card_codes);
            }
            if($card_codes){
//                $where_or     = ['a.card_code' => ['in', $card_codes]];// 写在下面where all
            }
//            $where['a.card_code'] = ['in', $card_codes];
        }
        if($card_codes){
            $where[]      = ['exp', sprintf("a.status=1 or a.card_code in ('%s') ",implode("','",array_unique($card_codes)))];//买赠券新增冻结也可以用  or (a.status in (1,7) and b.is_gift_card=1)
        }else{
            $where[]      = ['exp', sprintf("a.status=1 ")];//买赠券新增冻结也可以用  or (a.status in (1,7) and b.is_gift_card=1)
        }
        if(!empty($chose_card_codes)) {
            $where['a.card_code'] = ['in', $chose_card_codes];
        }


        $card_list = $card_r_model->getJoinCardList(['where' => $where, 'where_or' => $where_or, 'field' => 'a.openid,a.vin,a.card_id,a.card_code,a.status,a.validity_date_start,a.validity_date_end,b.id,b.get_limit,b.card_type,b.card_quota,b.card_discount,b.least_type,b.least_cost,b.available_count,b.default_detail,b.use_des,b.settlement_rule_type,b.settlement_rule_value,b.can_with,b.card_name,b.e3s_activity_id,b.settlement_rule_id,b.e3s_activity_name,b.settlement_rule_name,b.quick_win_card_id,b.card_desc,b.receive_scene,a.use_vin,a.id rec_id,a.activity_id rec_act_id,a.receive_vin,b.is_gift_card,a.user_id']);

        Logger::error('userGetCard.'.$card_r_model->getLastSql());
//        print_json($card_list,$card_r_model->getLastSql());
        $card_res  = [];
        if ($card_list) {
            $card_arr = [];
            foreach ($card_list as $k => $v) {
                $v['id'] = (string)$v['id'];
                $order_vin_str =  sprintf('%s',trim($order_vin,"'"));
                $order_vin_arr =  explode(',',$order_vin_str);
                if($v['receive_vin'] && $order_vin){
                    if(!in_array($v['receive_vin'],$order_vin_arr)){
                        unset($card_list[$k]);
                        continue;
                    }
                }else{
                    if($v['user_id'] && $v['user_id']!=$user_id){
                        unset($card_list[$k]);
                        continue;
//                    if(!in_array($v['receive_vin'],$order_vin_arr) || $v['user_id']!=$user_id ){
//                        unset($card_list[$k]);
//                        continue;
//                    }
                    }
                }

                if (!in_array($v['id'], $card_arr)) {
                    //date('Y-m-d H:i:s') > $v['validity_date_start'] && 小于结束时间即可
                    $card_res[] = $v;
//                    if ( date('Y-m-d') <= date('Y-m-d',strtotime($v['validity_date_end']))) {
////                        $card_res[] = $v;
//                    }
//                    if ($v['date_type'] == 2) {
//                        //加入判断领取多少天之后起效多少天失效
//                        $fixed_end_date   = date('Y-m-d H:i:s', strtotime(sprintf("%s +%s day", $v['created_date'], $v['fixed_term'])));
//                        $fixed_begin_date = date('Y-m-d H:i:s', strtotime(sprintf("%s +%s day", $v['created_date'], $v['fixed_begin_term'])));
//
//                        if (date('Y-m-d H:i:s') > $fixed_begin_date && date('Y-m-d H:i:s') < $fixed_end_date) {
//                            $card_res[] = $v;
//                        }
//                    } elseif ($v['date_type'] == 1 && ($v['validity_date_start'] || $v['validity_date_end'])) {
//                        if ((!$v['validity_date_start'] || date('Y-m-d') >= $v['validity_date_start']) && (!$v['validity_date_end'] || $v['validity_date_end'] >= date('Y-m-d'))) {
//                            $card_res[] = $v;
//                        }
//                    } else {
//                        $card_res[] = $v;
//                    }
                }
                if(!in_array($v['receive_scene'], $this->spe_receive_scene)){
                    $card_arr[] = $v['id'];//验证是否已经插入过了过滤重复的卡券--一张卡券只显示一次

                }

            }
        }
        return $card_res;


    }

    protected function _checkUserCardV1($cards, $type, $user_id,$channel_type='',$use_gift_card=0)
    {
        if (!$cards) {
            return false;
        }
        if (!$user_id) {
            return false;
        }
        $card_res = array();

        $card_id_arr =  array_column($cards,'id');
        $user = session("net-api-user-info");

        $card_id_re_arr = [];
        $card_id_can_arr = [];
        $ch_card_res = $this->_checkCardEvent($card_id_arr, $user_id, $type,'',$channel_type,$user['vin']);
        if($ch_card_res){
            foreach ($ch_card_res as $ch_v){
                $card_id_re_arr[$ch_v['card_id']] =$ch_v['cc'];
            }
        }

        $can_use_res =  $this->_checkCardEventCanU($card_id_arr, $user_id,2,$user['vin'],$channel_type,$use_gift_card);
        if($can_use_res){
            foreach ($can_use_res as $can_v){
                if($can_v['user_id'] && $can_v['receive_vin'] && ($can_v['user_id']!=$user_id || $can_v['receive_vin']!=$user['vin'])){

                }else{
                    // 增加一个
                    if(($can_v['receive_vin'] && $can_v['receive_vin']==$user['vin']) || !$can_v['receive_vin'] ){
                        $card_id_can_arr[$can_v['card_id']] =$can_v;
                    }
                }

            }
        }
        foreach ($cards as $key => $v) {
            $hava_get =0;
            if(isset($card_id_re_arr[$v['id']])){
                $hava_get = $card_id_re_arr[$v['id']];
            }
            $v['have_get'] = $hava_get;
            if(!$v['get_limit']){
                $v['get_limit'] = 999;
            }
            $v['available_quantity1'] =$v['available_quantity'] = $v['get_limit'] - $hava_get; //按照 接口返回
            $v['get_status_name'] =  "已使用";
            if(isset($card_id_can_arr[$v['id']])){
                $card_id_can_one = $card_id_can_arr[$v['id']];
                $v['can_use_card'] =1; // 可用券ID
                $v['all_cc'] =$card_id_can_one['cc']; // 可用券ID
                $v['g_status'] =$card_id_can_one['g_status']; // 所领券状态、
                if($card_id_can_one['g_status']){
                    $one_card_status = explode(',',$card_id_can_one['g_status']);
                    if(in_array(1,$one_card_status)){
                        $v['get_status_name'] =  "已领取";
                        $v['status'] =  1;
                    }elseif (in_array(7,$one_card_status)){
                        $v['get_status_name'] =  "未激活";
                        $v['status'] =  7;
                        // 新增：待激活券的扩展信息
                        $v['is_pending_activation'] = true;
                        $v['show_switch_vehicle'] = $this->shouldShowSwitchVehicle($card_id_can_one, $user);
                        $v['activation_scenes'] = $this->parseActivationScenes($v['coupon_activate_scene_list'] ?? '');
                        $v['activation_button_text'] = '去激活';
                    }
                }

                $v['receive_vin'] =$card_id_can_one['receive_vin']; // 领券vin
                $v['card_time_word'] = sprintf("%s ~ %s",$this->dataFYmdpoint($card_id_can_one['min_start']),$this->dataFYmdpoint($card_id_can_one['min_end']));
            }else{
                $v['can_use_card']=0;

            }
//            $res = $this->_checkCardEvent($v['id'], $user_id, $type);
//            $can_use_card = $this->_checkCardEventCanU($v['id'], $user_id,2);
////            echo $can_use_card.'--'.$v['id'].'++';
//            $v['available_quantity'] = $v['get_limit'] - $res; // 超出领券限制数就不能领
//            if($can_use_card){
//                $v['can_use_card'] =1; // 可用券ID
//                $v['card_time_word'] = sprintf("%s ~ %s",$this->dataFYmdpoint($can_use_card[0]['min_start']),$this->dataFYmdpoint($can_use_card[0]['min_end']));
//            }else{
//                $v['can_use_card']=0;
//
//            }

            $card_res[$key] = $v;
        }
//        die();
        return $card_res;
    }


    protected function _checkCardEventCanU($card_id,$user_id,$back_count=1,$user_vin='',$channel_type='',$use_gift_card=0){
//        $redis_name = "_checkCardEventCanU-".$card_id.$user_id.$back_count;
//        $card_event =  \redis($redis_name);
        $event_model = new BuCardReceiveRecord();
        //,'validity_date_end'=>['>=',date('Y-m-d H:i:s')
        $user = session("net-api-user-info");
        if (is_string($card_id)) {
            $card_id = explode(',', $card_id);
        }
        // 去重
        $card_id = array_unique($card_id);
        $where      = ['card_id' => ['in',$card_id], 'user_id' => $user_id, 'is_enable' => 1, 'status' => 1];
        if ($back_count == 1) {
            $where['validity_date_end'] = ['>=',date('Y-m-d H:i:s')];
            $card_event = $event_model->getCount(['where' => $where]);
        } else {
            $where      = ['rec.card_id' => ['in',$card_id], 'rec.is_enable' => 1];//, 'rec.status' => 1
            $where['rec.validity_date_end'] = ['>=',date('Y-m-d H:i:s')];
            if($user_vin){
                //就是有下单vin就判断下单vin？不需要管是不是那个用户领的？ 林茹说的
//                $where[] = ['exp',sprintf(" (rec.receive_vin='%s' ) || (rec.user_id='%s'  and (rec.receive_vin='' or rec.receive_vin is null))",$user_vin,$user_id)];//这个太慢了
                $where[] = ['exp',sprintf(" (rec.receive_vin='%s' ) || (rec.user_id='%s' )",$user_vin,$user_id)];/// todo 要在下面判断user+vin
            }else{
                $where[] = ['exp',sprintf(" rec.user_id='%s' and (rec.receive_vin='' or rec.receive_vin is null)",$user_id)];
            }
            if(!$use_gift_card){
                $where[] = ['exp',sprintf(" rec.status=1 or (rec.status in (1,7) and a.is_gift_card=1)")];
            }else{
                $where[] = ['exp',sprintf(" rec.status=1")];

            }
            if($channel_type){
                $where[]      = ['exp', sprintf("(rec.activity_id>0 and  FIND_IN_SET('%s',act.up_down_channel_dlr)) or (rec.activity_id=0 and  FIND_IN_SET('%s',a.up_down_channel_dlr))", $channel_type, $channel_type)];
            }
//            $where[]=['exp',['']]
            $card_event = $event_model->alias('rec')->join('t_db_card a',"a.id=rec.card_id")
                ->join('t_db_activity act',"act.activity_id=rec.activity_id and rec.activity_id>0",'left')->where($where)->field('rec.*,min(rec.validity_date_start) min_start,min(rec.validity_date_end) min_end,count(rec.card_id) cc,GROUP_CONCAT(rec.status) g_status')->order('validity_date_end asc')->group('rec.card_id')->select();
            Logger::error('user-get-card-error',$event_model->getLastSql());
//            $card_event = $event_model->getList(['where' => $where,'order'=>'validity_date_end asc','group'=>'card_id','field'=>'*,min(validity_date_start) min_start,min(validity_date_end) min_end,count(card_id) cc']);
        }
//        \redis($redis_name,$card_event,10);
//        \redis($redis_name.'lock',1,10);
//        echo $event_model->getLastSql();die();
        return $card_event;
    }

    /**
     * 查询用户已领卡券及可领取卡券
     * STATE卡券状态：0正常 1核销 2删除 3转赠,9可领取
     * $type 1领取，2使用
     * $v['validity_date_start'] . '--' . $v['validity_date_end'] 必须重新处理
     */

    protected function _checkUserCard($cards, $type, $user_id,$order_vin='')
    {
        if (!$cards) {
            return false;
        }
//        var_dump($cards);
//        var_dump($user_id);
//        die();
        $card_res = array();
        $card_arr = array();

        foreach ($cards as $key => $v) {
            $card_can_r = 1;

            $res = $this->_checkCardEvent($v['id'], $user_id, $type,$v['card_code']??'','',$order_vin);
            if ($res) {
//                //2或者1都加入判断
//                if ($res['status'] < 3) {
//                    $res['status'] = 0;
//                }
                if ($type == 1) {//1的时候返回了领券数
                    if ($res >= $v['get_limit']) {
                        $card_can_r = 0;//超出领券限制数就不能领
                    }
                } else {
                    $v['card_code']    = $res['card_code'];
                    $v['get_dlr_code'] = $res['get_dlr_code'];//专营店使用卡券只允许那个专营店使用

                }

            }
            if ($type == 2) {
                if ($res && in_array($res['status'], [1, 5,7])) {
                    //&& date('Y-m-d H:i:s') > $res['validity_date_start'] 未开始也记录
                    if (!in_array($v['id'], $card_arr)  &&  date('Y-m-d') <= date('Y-m-d',strtotime($res['validity_date_end']))) {
                        $card_res[$key] = $v;
                    }
//                    if ($v['date_type'] == 2) {
//                        //加入判断领取多少天之后起效多少天失效
//                        $fixed_end_date   = date('Y-m-d H:i:s', strtotime(sprintf("%s +%s day", $res['created_date'], $v['fixed_term'])));
//                        $fixed_begin_date = date('Y-m-d H:i:s', strtotime(sprintf("%s +%s day", $res['created_date'], $v['fixed_begin_term'])));
//
//                        if (!in_array($v['id'], $card_arr) && date('Y-m-d H:i:s') > $fixed_begin_date && date('Y-m-d H:i:s') < $fixed_end_date) {
//                            $card_res[$key] = $v;
//                        }
//                    } else {
//                        if (!in_array($v['id'], $card_arr)) {
//                            $card_res[$key] = $v;
//                        }
//                    }
                    $card_arr[] = $v['id'];//验证是否已经插入过了过滤重复的卡券
                }
            } elseif ($type == 1) {//可领取卡券
                if ($card_can_r == 1) {
                    $card_res[$key] = $v;
                    //这里不做判断是否能领取，关联了就能领
                }
            }
        }
//        var_dump($card_res);die();
        return $card_res;
    }

    //查看用户领券记录表中是否领取了
    private function _checkCardEvent($card_id, $user_id, $type = 2,$card_code="",$channel_type='',$order_vin='')
    {
        $event_model = new BuCardReceiveRecord();
        if ($type == 1) {
            $where      = ['rec.card_id' => ['in',$card_id], 'rec.is_enable' => 1];
            if($order_vin){
                $where[]=['exp',sprintf("rec.user_id=%s or rec.receive_vin='%s'",$user_id,$order_vin)];

            }else{
                $where['rec.user_id']=$user_id;

            }
            $where[] =  ['exp',sprintf("rec.status in (1,3,5) or (rec.status in (1,3,5,7) and  a.is_gift_card=1)")];

            $card_event = $event_model->alias('rec')->join('t_db_card a','a.id=rec.card_id')->where($where)->group('rec.card_id')->field("rec.card_id,count(1) cc")->select();
            if(!is_array($card_id) && $card_event){
                return $card_event[0]['cc'];
            }
        } else {
            $card_event = $event_model->getCardEventByUserId($card_id, $user_id,$card_code,$channel_type,$order_vin);
            if(!is_array($card_id) && $card_event){
                return $card_event[0];
            }
        }
//        echo $event_model->getLastSql();
//        die();
        return $card_event;
    }

    /**
     * 通过订单号查询订单商品得优惠，满减,N件N折 子商品也不计入计算
     * @param $order_code
     */
    protected function order_goods_yh($order, $is_change_db)
    {
        $order_code = $order['parent_order_code'];
        $disabled_card_sku_ids = [];
        $sku_rel_cards = [];
        $order_goods_model = new BuOrderCommodity();
        $full_model = new DbFullDiscount();
        $order_model = new BuOrder();
        $net_goods = new NetGoods();
        $order_goods       = $order_goods_model->getList(['where' => ['parent_order_code' => $order_code, 'mo_sub_id' => 0, 'is_enable' => 1]]);
        $yh_goods          = [];
        $wi_yh_goods = [];
        $full_dis_goods    = [];
        $act_full_info[] = [];
        //limit_id' , 'suit_id', 'full_id' , 'n_dis_id' ,'group_id' , 'pre_sale_id'
        if ($order_goods) {
            // 商品会员价
            foreach ($order_goods as $key => $v) {
                if ($v['commodity_use_discount'] == 1 && in_array($order['promotion_source'], [0 ,4]) && empty($v['pre_sale_id']) && empty($v['group_id']) && empty($v['suit_id'])) {
                    $commodity_dis_info = $net_goods->getCommoditySegmentDiscount($v['commodity_id']);
                    if ($commodity_dis_info) {
                        $member_price = $net_goods->getCommodityDisFinalPrice($commodity_dis_info, $v['price']);
                        $commodity_dis_data['actual_price'] = $order_goods[$key]['actual_price'] = $member_price;
                        $commodity_dis_data['commodity_segment'] = $commodity_dis_info['user_segment'];
                        $commodity_dis_data['commodity_segment_membership'] = $commodity_dis_info['membership_level'];
                        $commodity_dis_data['commodity_segment_owner'] = $commodity_dis_info['brand_owner_label'];
                        $commodity_dis_data['commodity_segment_dis_money'] = bcmul(($v['price'] - $member_price), $v['count'], 2);
                        $order_goods_model->saveData($commodity_dis_data, ['id' => $v['id']]);
                    }
                }
            }

            foreach ($order_goods as $v) {
                if ($v['full_id']) {
                    //
                    $work_time_price = $v['work_time_money'];
//                    if ($v['work_time_json']) {
//                        $work_time       = json_decode($v['work_time_json'], true);
//                        $work_time_price = $work_time['work_time_number'] * $work_time['work_time_price'];
//                    }
                    if (empty($act_full_info[$v['full_id']])) {
                        $act_full_info[$v['full_id']] = $full_model->getOneByPk($v['full_id']);
                    }

                    $can_buy = 999;
                    if ($act_full_info[$v['full_id']]['purchase_number'] > 0) {
                        $o_g_where = ['a.user_id' => $this->user_id, 'a.order_status' => ['NOT IN', '1,3,5,6,8,18']];
                        $o_g_count = $order_model->alias('a')->whereExists(function ($query) use ($v) {
                            $query->table('t_bu_order_commodity')->alias('b')->where('a.order_code=b.order_code')->where('b.full_id', $v['full_id']);
                        })->where($o_g_where)->field("count(*) number")->find();
                        $can_buy = $act_full_info[$v['full_id']]['purchase_number'] - $o_g_count['number'];
                    }
                    if ($can_buy > 0) {
                        $yh_goods[$v['full_id']]['full_id'] = $v['full_id'];
                        $yh_goods[$v['full_id']]['full_list'][] = [
                            'full_id' => $v['full_id'],
                            'commodity_id' => $v['commodity_id'],
                            'sku_id' => $v['sku_id'],
                            'count' => $v['count'],
                            'price' => $v['actual_price'],
                            'goods_prices' => $v['count'] * $v['actual_price'],
                            'work_time_price' => $work_time_price,//工时价格
                        ];
                    } else {
                        $order_goods_model->saveData(['full_id' => 0], ['id' => $v['id']]);
                    }
                }

                if ($v['full_wi_id'] && ($v['full_id'] != $v['full_wi_id'])) {
                    $work_time_price = $v['work_time_money'];

                    if (empty($act_full_info[$v['full_wi_id']])) {
                        $act_full_info[$v['full_wi_id']] = $full_model->getOneByPk($v['full_id']);
                    }

                    $can_buy = 999;
                    if ($act_full_info[$v['full_wi_id']]['purchase_number'] > 0) {
                        $o_g_where = ['b.full_id' => $v['full_id'], 'b.commodity_id' => $v['commodity_id'], 'a.user_id' => $this->user_id, 'a.order_status' => ['NOT IN', '1,3,5,6,8,18']];
                        $o_g_count = $order_model->alias('a')->join('t_bu_order_commodity b', 'a.order_code=b.order_code')->where($o_g_where)->field("sum(count) number")->find();
                        $can_buy = $act_full_info[$v['full_wi_id']]['purchase_number'] - $o_g_count['number'];
                    }

                    if ($can_buy - $v['count'] > 0) {
                        $wi_yh_goods[$v['full_wi_id']]['full_id'] = $v['full_wi_id'];
                        $wi_yh_goods[$v['full_wi_id']]['full_list'][] = [
                            'full_id' => $v['full_wi_id'],
                            'commodity_id' => $v['commodity_id'],
                            'sku_id' => $v['sku_id'],
                            'count' => $v['count'],
                            'price' => $v['price'],
                            'goods_prices' => $v['count'] * $v['price'],
                            'work_time_price' => $work_time_price, // 工时价格
                        ];
                    } else {
                        $order_goods_model->saveData(['full_wi_id' => 0], ['id' => $v['id']]);
                    }
                }
//                if ($v['n_dis_id']) {
//                    $yh_goods[$v['n_dis_id']]['n_dis_id']     = $v['n_dis_id'];
//                    $yh_goods[$v['n_dis_id']]['n_dis_list'][] = [
//                        'n_dis_id'     => $v['n_dis_id'],
//                        'commodity_id' => $v['commodity_id'],
//                        'count'        => $v['count'],
//                        'price'        => $v['price'],
//                        'goods_prices' => $v['count'] * $v['price'],
//                    ];
//                }
            }

            if ($yh_goods) {
                foreach ($yh_goods as $k => $vv) {
                    $full_all_price = 0;
                    $n_dis_count    = 0;
                    if ($vv['full_id']) {
                        foreach ($vv['full_list'] as $vvv) {
                            $full_all_price += $vvv['goods_prices'] + bcmul($vvv['work_time_price'],$vvv['count'],2);
                        }
                        $yh_goods[$k]['full_all_price'] = $full_all_price;
                        $mj_yj                          = $this->mj_yj($vv['full_id'], $full_all_price, $vv['full_list']);
                        if (!$mj_yj) {
                            // 清空订单商品关联的满减活动信息
                            foreach ($order_goods as $v) {
                                foreach ($vv['full_list'] as $vvv) {
                                    if ($vvv['sku_id'] == $v['sku_id'] && $vvv['full_id'] == $v['full_id']) {
                                        $order_goods_model->saveData(['full_id' => 0, 'forward_act_json' => ''], ['id' => $v['id']]);
                                        break;
                                    }
                                }
                            }
                            unset($yh_goods[$k]);
                        } else {
                            array_push($disabled_card_sku_ids, ...$mj_yj['disabled_card_sku_ids']);
                            $yh_goods[$k]['all_mj']    = $mj_yj['mg'];
                            $yh_goods[$k]['full_list'] = $mj_yj['list'];
                        }

                    }
                }
            }

            if ($wi_yh_goods) {
                foreach ($wi_yh_goods as $k => $vv) {
                    if ($vv['full_id']) {
                        $mj_yj = $this->mj_yj($vv['full_id'], 0, $vv['full_list']);
                        if (!$mj_yj) {
                            unset($wi_yh_goods[$k]);
                        } else {
                            $wi_yh_goods[$k]['all_mj'] = $mj_yj['mg'];
                            $wi_yh_goods[$k]['full_list'] = $mj_yj['list'];
                        }
                    }
                }
            }
            if ($is_change_db) {
                if ($yh_goods || $wi_yh_goods) {
                    $order_goods = $order_goods_model->getList(['where' => ['parent_order_code' => $order_code]]);
                    foreach ($order_goods as $v) {
                        $order_goods_data = [];
                        if ($v['full_id']) {
                            if (isset($yh_goods[$v['full_id']])) {
                                foreach ($yh_goods[$v['full_id']]['full_list'] as $vv) {
                                    if ($vv['sku_id'] == $v['sku_id']) {
                                        if ($vv['yh_money'] == 0) {
                                            $actual_price = $vv['price'];
                                        } else {
                                            $actual_price = $vv['price'] - round($vv['yh_money'] / $v['count'], 2);
                                        }
                                        $all_dis = $vv['yh_money'] + $vv['work_yh_money'];
//                                        $list[$k]['settlement_rule_id'] = $full_info['settlement_rule_id'];
//                $list[$k]['settlement_rule_type'] = $full_info['settlement_rule_type'];
//                $list[$k]['settlement_rule_value'] = $full_info['settlement_rule_value'];
                                        //结算规则 1固定金额 2比例结算
                                        $settlement_value = 0;
                                        $rule = [];
                                        $sett_value = [];
                                        if ($vv['settlement_rule_value']) {

                                            $set_info =  $this->act_card_set_rule($all_dis,$v['cost_price'],$vv['price'],0,$vv['settlement_rule_type'],$vv['settlement_rule_value'],$vv['act_sett_standard'],$v['count']);
                                            //每个商品只有一个活动
                                            $rule   = $set_info['rule'];
                                            $sett_value   = $set_info['sett_value'];
//                                            if ($vv['settlement_rule_type'] == 1) {
//                                                $settlement_value = $vv['settlement_rule_value'];
//                                            } else {
//                                                //2022-07-15 15:46:43 Tzl ((100 - $vv['settlement_rule_value']) 改成$vv['settlement_rule_value']
//                                                $settlement_value = round($all_dis * ($vv['settlement_rule_value'] / 100), 2);
//                                            }
                                        }


                                        $sku_rel_cards[$vv['sku_id']] = $vv['rel_card_ids'];
                                        $order_goods_data = [
//                                            $_od_data['act_type'] = $limit_info['discount_type'];
                                            'act_type'    => $vv['discount_type'],
                                            'full_dis_money'    => $vv['yh_money'],
                                            'act_name'          => $vv['act_name'],
                                            'actual_price'      => $actual_price,
                                            'all_dis'           => $all_dis,
                                            'last_updated_date' => date('Y-m-d H:i:s'),
                                            'modifier'          => 'f_d_sy',
                                            'act_sett_rule_id'  => $vv['settlement_rule_id'],
                                            'e3s_activity_id'  => $vv['e3s_activity_id'],
                                            'act_sett_money'    => $sett_value,
                                            'act_sett_rule'    => $rule,
                                            'act_sett_standard'    => $vv['act_sett_standard'],

                                        ];

                                        //sy todo 这里再计算工时费;  可以不在写入JSON，已经在进入confirm的时候做了 暂保留json
                                        if ($v['work_time_json'] && $vv['work_yh_money'] &&$v['work_time_money']>0) {
                                            $data_work_time                             = json_decode($v['work_time_json'], true);
                                            $data_work_time['work_full_dis']            = $vv['work_yh_money'];
                                            $order_goods_data['work_time_actual_money'] = $v['work_time_money'] - round($vv['work_yh_money'] / $v['count'], 2);
                                            $order_goods_data['work_time_dis']          = $vv['work_yh_money'];
                                            $order_goods_data['work_time_json']         = json_encode($data_work_time);
                                        }
//                                        $order_goods_model->saveData($order_goods_data, ['id' => $v['id']]);//更新满减优惠金额
//                                    echo $order_goods_model->getLastSql();
                                    }
                                }
                            } else {
                                $order_goods_model->saveData(['full_id' => 0], ['id' => $v['id']]);
                            }
                        }

                        if ($v['full_wi_id']) {
                            if (isset($wi_yh_goods[$v['full_wi_id']])) {
                                foreach ($wi_yh_goods[$v['full_wi_id']]['full_list'] as $vv) {
                                    if ($vv['sku_id'] == $v['sku_id']) {

                                        $actual_price = $vv['price'];
                                        $all_dis = $vv['work_yh_money'];

                                        //结算规则 1固定金额 2比例结算
                                        $settlement_value = 0;
                                        if ($vv['settlement_rule_value']) {
                                            if ($vv['settlement_rule_type'] == 1) {
                                                $settlement_value = $vv['settlement_rule_value'];
                                            } else {
                                                $settlement_value = round($all_dis * ($vv['settlement_rule_value'] / 100), 2);
                                            }
                                        }

                                        if (!empty($order_goods_data)) {
                                            $order_goods_data['act_type'] = 3;
                                            $order_goods_data['act_name'] = $order_goods_data['act_name'] . ',' . $vv['act_name'];
                                            $order_goods_data['all_dis'] = $order_goods_data['all_dis'] + $vv['work_yh_money'];
                                            $order_goods_data['last_updated_date'] = date('Y-m-d H:i:s');
                                            $order_goods_data['modifier'] = 'f_d_wi_sy';
                                        } else {
                                            $order_goods_data = [
                                                'act_type' => $vv['discount_type'],
                                                'full_dis_money' => 0,
                                                'act_name' => $vv['act_name'],
                                                'actual_price' => $actual_price,
                                                'all_dis' => $all_dis,
                                                'last_updated_date' => date('Y-m-d H:i:s'),
                                                'modifier' => 'f_d_sy',
                                                'act_sett_rule_id' => $vv['settlement_rule_id'],
                                                'e3s_activity_id' => $vv['e3s_activity_id'],
                                                'act_sett_money' => $settlement_value * $v['count'],
                                            ];
                                        }

                                        if ($v['work_time_json'] && $vv['work_yh_money'] && $v['work_time_money'] > 0) {
                                            $data_work_time = json_decode($v['work_time_json'], true);
                                            $data_work_time['work_full_dis'] = $vv['work_yh_money'];
                                            $order_goods_data['work_time_actual_money'] = $v['work_time_money'] - round($vv['work_yh_money'] / $v['count'], 2);
                                            $order_goods_data['work_time_dis'] = $vv['work_yh_money'];
                                            $order_goods_data['work_time_json'] = json_encode($data_work_time);
                                        }
                                    }
                                }
                            } else {
                                $order_goods_model->saveData(['full_wi_id' => 0], ['id' => $v['id']]);
                            }
                        }

                        if (!empty($order_goods_data)) {
                            $order_goods_model->saveData($order_goods_data, ['id' => $v['id']]);//更新满减优惠金额
                        }
                    }
                }
            }
        }
        $data = ['disabled_card_sku_ids'=>$disabled_card_sku_ids,'sku_rel_cards'=>$sku_rel_cards];
        return $data;
    }

    /**
     * @param $mj_id 满减ID
     * @param $all_price 订单总价 可以不写，会重新计算
     * @param $list 订单列表
     * @return array mj:满减金额,list:添加了各个商品得优惠
     */

    public function mj_yj($mj_id, $all_price, $list)
    {
        $full_model = new DbFullDiscount();
        $full_info  = $full_model->getOneByPk($mj_id);
//        full_id,commodity_id,sku_id,count,price,goods_prices,work_time_price
        //满减full_discount_rules按照满减额度从高到底排序，循环到得最先一个为满减金额`
        if (!$full_info) {
            return false;
        }
        //速赢版本把总额计算放在这里
        //满减也要算工时+商品计算           优惠类型：1仅商品；2仅工时；3商品+工时 discount_type
        $full_all_price  = 0;
        $goods_all_price = 0;
        foreach ($list as $vvv) {
            $goods_price         = $vvv['price'] * $vvv['count'];
            $work_time_all_price = $vvv['work_time_price'] * $vvv['count'];
            if ($full_info['discount_type'] == 1) {
                $full_all_price += $goods_price;
//                $full_all_price += $vvv['goods_prices']+$vvv['work_time_price'];
            } elseif ($full_info['discount_type'] == 2) {
                $full_all_price += $work_time_all_price;
            } else {
                $full_all_price += $goods_price + $work_time_all_price;
            }
            $goods_all_price += $goods_price + $work_time_all_price;

        }
        $all_price = $full_all_price;
        $mj        = 0;
        if ($full_info) {
            $full_rule = $full_info['full_discount_rules'];
            if ($full_rule) {
                $full_rule = json_decode($full_rule, true);
                if ($full_info['user_segment']) {
                    $segment_info = get_user_segment_info();
                    $membership = $segment_info['membership_level'];
                    $owner = $segment_info['brand_owner_label'];
                    if ($full_info['user_segment'] == 1) {
                        $full_rule = $full_rule[$membership];
                    } else {
                        $full_rule = $full_rule[$owner];
                    }
                }
                foreach ($full_rule as $v) {
                    if ($all_price >= $v[0]) {
                        $mj = $v[1];
                        break;
                    }
                }
            }
        }
        $goods_mj     = 0;
        $work_time_mj = 0;
        $disabled_card_sku_ids = [];
        if ($mj) {
            $toEnd  = count($list);
            $i      = 1;//
            $y_yh_m = 0;//已优惠


            //满减以整个商品去计算满减额，不精细到除以数量
            foreach ($list as $k => $v) {
                $work_yh_money = 0;
                $yh_money      = 0;
                if (in_array($full_info['discount_type'], [2, 3])) {
                    $work_yh_money = round($v['work_time_price'] * $v['count'] * $mj / ($all_price), 2);
                }
                if (in_array($full_info['discount_type'], [1, 3])) {
                    $yh_money = round($v['goods_prices'] * $mj / ($all_price), 2);

                }
                if (0 === --$toEnd) {
                    $last_mj = $mj - $y_yh_m;
                    if ($full_info['discount_type'] == 1) {
                        $yh_money = $last_mj;
                    } elseif ($full_info['discount_type'] == 2) {
                        $work_yh_money = $last_mj;
                    } else {
                        $work_yh_money = $last_mj - $yh_money;
                    }
//                    $yh_money = ($mj - $y_yh_m)/$v['count'];
                } else {
                    if ($full_info['discount_type'] == 1) {
                        $work_yh_money = 0;
                        $y_yh_m        += round($yh_money, 2);
                    } elseif ($full_info['discount_type'] == 2) {
                        $yh_money = 0;
                        $y_yh_m   += round($work_yh_money, 2);
                    } else {
                        $y_yh_m += round($work_yh_money, 2) + round($yh_money, 2);
                    }
//                    $yh_money         = round($v['goods_prices'] * $mj / ($all_price), 2);
//                    $one_all_yh_money = round($work_yh_money, 2)+round($yh_money, 2);
//                    $y_yh_m           += $one_all_yh_money;
                }
                $i++;
                $work_yh_money= round($work_yh_money, 2);
                $list[$k]['yh_money']              = $yh_money;
                $list[$k]['work_yh_money']         = $work_yh_money;
                $list[$k]['settlement_rule_id']    = $full_info['settlement_rule_id'];
                $list[$k]['settlement_rule_type']  = $full_info['settlement_rule_type'];
                $list[$k]['settlement_rule_value'] = $full_info['settlement_rule_value'];
                $list[$k]['act_sett_standard'] = $full_info['act_sett_standard'];
                $list[$k]['e3s_activity_id'] = $full_info['e3s_activity_id'];
                $list[$k]['act_name'] = $full_info['activity_title'];
                $list[$k]['discount_type'] = $full_info['discount_type'];
                $list[$k]['rel_card_ids'] = $full_info['rel_card_ids'];
                $goods_mj                          += $yh_money;
                $work_time_mj                      += $work_yh_money;

                if (empty($full_info['card_available'])) {
                    $disabled_card_sku_ids[] = $v['sku_id'];
                }
            }
        } else {
            return false;
        }
        return ['mg' => $mj, 'list' => $list, 'good_mj' => $goods_mj, 'work_time_mj' => $work_time_mj, 'goods_all_price' => $goods_all_price, 'disabled_card_sku_ids' => $disabled_card_sku_ids,'full_info'=>$full_info];
    }


    /**
     * 实际使用积分分摊计算  主账户分账ID  DNDC001
     * 1到店2快递
     * @param $list
     * @param $all_point
     * @param $res_point
     * @return mixed
     */
    public function share_point($list)
    {
        $order_goods_model = new BuOrderCommodity();
        $order_model       = new BuOrder();
        $mail_type         = 0;
        $order_code        = '';
        $total_money       = 0;
        $money             = 0;
        $integral          = 0;
        $mail_price        = 0;
        $b_act_goods_price = 0;
        $yj_goods_price    = 0;
        $new_order_code    = '';

        foreach ($list as $k => $v) {
            $o_g_data       = [
                'order_code' => $v['order_code'] . '-' . $v['order_mail_type'],//修改订单号
            ];
            $new_order_code = $o_g_data['order_code'];
            $order_code     = $v['order_code'];
            $mail_type      = $v['order_mail_type'];

            $b_act_goods_price += $v['price'] * $v['count'];//活动前商品总价
            $yj_goods_price    += $v['actual_price'] * $v['count'];//活动前商品总价
            if ($mail_type == 2) {
                $mail_price += $v['mail_price'];//运费
            } else {
                $mail_price = 0;//到店类运费0
            }
            $integral    += $v['actual_point'];//使用厂家积分
            $money       += $v['actual_use_money'];//实际支付金钱
            $total_money += $v['h_price_count'];//实际总额=钱+积分
            $order_goods_model->saveData($o_g_data, ['id' => $v['id']]);
        }
        $time = date('Y-m-d H:i:s');
        //查主单
        $order       = $order_model->getOne(['where' => ['order_code' => $order_code, 'parent_order_type' => ['in', [1, 3]]]]);
        $dlr_channel = DbDlr::channel($order['channel']);
        $order       = $order->toArray();
        if ($order['parent_order_type'] <> 3) {
            $order_model->saveData(['parent_order_type' => 3, 'last_updated_date' => $time, 'dlr_code' => $dlr_channel], ['id' => $order['id']]);//原订单为父订单
        }
        if ($mail_type == 2) {
            $order['dlr_code']       = $dlr_channel;
            $order['logistics_mode'] = 2;
            $money                   += $mail_price;
        } else {
            $order['logistics_mode'] = 1;
        }
        unset($order['id']);
        $order['money']             = $money;
        $order['total_money']       = $yj_goods_price;
        $order['order_code']        = $new_order_code;
        $order['integral']          = $integral;
        $order['mail_price']        = $mail_price;
        $order['b_act_goods_price'] = $b_act_goods_price;
        $order['parent_order_type'] = 2;//子单
        $order['last_updated_date'] = $time;
        $res                        = $order_model->insertGetId($order);
        return $order;
    }

    //按照各种订单类型拆单.速赢版本拆单。。
    public function spilt_order($order_code)
    {
        $order_goods_model = new BuOrderCommodity();
        $order_model       = new BuOrder();
        $order_goods_list  = $order_goods_model->getList(['where' => ['parent_order_code' => $order_code]]);
        $order_list        = $order_model->getList(['where' => ['parent_order_code' => $order_code, 'parent_order_type' => ['in', [1, 2]]]]);
//        if($order['parent_order_type']==1){
//            return $order;
//        }
        $order_r_arr          = [];
        $order_goods_name_arr = [];
        $order_goods_id_arr = [];
        if ($order_goods_list) {
            $sp_all_order_list = [];
            $dd_dlr_arr        = [];
            $ss_code           = [];
            $card_goods        = [];
            $gift_types = [];
//            `dd_commodity_type` tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '到店商品类型 9到店备件 1保养套餐-老友惠保养套餐 3保养套餐-心悦保养套餐 4保养套餐-五年双保升级权益套餐 6保养套餐-其他 7到店代金券',
            $spi = 0;

            foreach ($order_list as $k => $o_v) {
                foreach ($order_goods_list as $v_v) {
                    if ($o_v['order_code'] == $v_v['order_code']) {
//                        $order_list[$k]['goods_names'][]=$v_v['commodity_name'];
                        $order_goods_name_arr[$o_v['order_code']][] = $v_v['commodity_name'];
                        $order_goods_id_arr[$o_v['order_code']][] = $v_v['commodity_id'];
                    }
                }
                $o_v['goods_names'] = $order_goods_name_arr[$o_v['order_code']];
                $o_v['commodity_id'] = $order_goods_id_arr[$o_v['order_code']];
                $order_r_arr[]      = $o_v;
                if ($o_v['parent_order_type'] == 2) {
                    $spi = 2;//已经拆过了
                }
            }
            foreach ($order_goods_list as $k => $v) {
                $dd_dlr_arr[] = $v['dd_dlr_code'];
                //9=>15,1=>16,3=>17,4=>18,6=>19,7=>20,41=>18,8=>21,10=>22,11=>23
//                0 => '', 1 => '商品订单', 2 => '拼团订单', 3 => '套装', 4 => '礼包', 5 => '到店有礼', 6 => '积分-日产', 7 => "会员日",
//            8 => '锦尚阳光口罩', 11 => 'CCS订单', 12 => '虚拟商品订单', 13 => '电子卡券商品订单', 14 => '积分-启辰', 15 => '到店备件',
//            16 => '保养套餐-老友惠保养套餐', 17 => '保养套餐-心悦保养套餐', 18 => '保养套餐-双保专属权益套餐', 19 => '保养套餐-其他',
//            20 => '到店代金券', 21 => '到店电子券', 22 => 'pz1a套餐', 23 => '工时商品', 24 => '取送车券', 25 => '启辰充电桩',
//            26 => "众筹商品", 27 => 'PV工会线上订单', 28 => 'PV总工会', //27-37 都给工会
//            40 => '保养套餐-五年双保专享心悦套餐',42=>'日产充电桩'
                if (in_array($v['order_source'], [13,15, 16, 17, 18, 19, 20,21,22,23,24,25,42])) {
                    $one_ss_code = $v['order_source'];
                    $ss_code[]   = $v['order_source'];
                } else {
                    if(in_array($v['supplier'],BuOrderCommodity::$jd_warehouse_supplier)){
                        $one_ss_code = 'pv';
                        $ss_code[]   = 'pv';
                    }elseif($v['supplier']=='JD'){
                        $one_ss_code = 'j';
                        $ss_code[]   = 'j';
                    }elseif($v['supplier']=='省广'){
                        $one_ss_code = 'sg';
                        $ss_code[]   = 'sg';
                    }else{
                        $one_ss_code = 0;
                        $ss_code[]   = 0;
                    }

                }
                $order_goods_list[$k]['t_dd_type'] = $one_ss_code;
                if ($v['order_source'] == 20) {
                    $one_card_goods = $v['commodity_id'];
                    $card_goods[]   = $v['commodity_id'];
                } else {
                    $one_card_goods = 0;
                }

                if (!empty($v['gift_act_id'])) {
                    $one_gift_act_goods = $v['gift_act_id'];
                } else {
                    $one_gift_act_goods = 0;
                }
                $gift_types[] = $v['is_gift'];
                $order_goods_list[$k]['g_act_id'] = $one_gift_act_goods;

                $order_goods_list[$k]['t_dd_card_goods'] = $one_card_goods;
                $order_goods_list[$k]['h_price_count']   = sprintf("%.2f", ((($v['price'] + $v['work_time_money']) * $v['count'] - $v['all_dis']) - $v['card_all_dis']));
            }
            if ($spi == 0) {
                if (count(array_unique($dd_dlr_arr)) > 1 || count(array_unique($card_goods)) > 1 || count(array_unique($ss_code)) > 1 || count(array_unique($gift_types)) > 1) {
                    $spi = 1;
                }
            }
//            print_json($order_goods_list);
            if (in_array($spi, [1, 2])) {
                $order_r_arr = [];
                foreach ($order_goods_list as $k => $v) {
                    if(!$v['gift_act_id']){
                        $v['is_gift']=0;
                    }
                    $sp_all_order_list[$v['dd_dlr_code'] . $v['t_dd_card_goods'] . $v['t_dd_type'] . $v['g_act_id'] . $v['is_gift']]['list'][] = $v;
                }
                if($spi==2){
                    //先删除所有子单
                    $order_model->where(['parent_order_type'=>2,'parent_order_code'=>$order_code])->delete();
                }
//                print_json($sp_all_order_list);
                foreach ($sp_all_order_list as $k => $v) {
                    $mail_type = 0;
//                    $order_code        = '';
                    $total_money       = 0;
                    $money             = 0;
                    $integral          = 0;
                    $mail_price        = 0;
                    $b_act_goods_price = 0;
                    $b_work_time_price = 0;
                    $yj_goods_price    = 0;
                    $new_order_code    = '';
//                    $o_g_data = ['card_ids' => '', 'card_codes' => '', 'card_yh' => '', 'card_all_dis' => 0,'card_sett_rule_id'=>'','card_sett_money'=>'' ,'last_updated_date' => date('Y-m-d H:i:s')];
                    $card_arr     = [];
                    $card_yh      = 0;
                    $act_yh = 0;
                    $order_source = 0;
                    $card_sett_money = 0;
                    $act_sett_money = 0;
                    $is_gift_order = 0;
//                    $logistics_mode=1;
                    $have_dd_type = 0;
                    foreach ($v['list'] as $vv) {
                        $o_g_data                                = [
                            'order_code' => $vv['parent_order_code'] . '-' . $k,//修改订单号
                        ];
                        $new_order_code                          = $o_g_data['order_code'];
                        $order_goods_name_arr[$new_order_code][] = $vv['commodity_name'];
                        $order_goods_id_arr[$new_order_code][] = $vv['commodity_id'];
//                        $order_code     = $vv['order_code'];
                        $dd_dlr_code       = $vv['dd_dlr_code'];
                        $mail_type         = $vv['order_mail_type'];
                        if(!in_array($vv['dd_commodity_type'],[1,2,3,4,5])){
                            $is_by_tc  = 0;
                        }else{
                            $is_by_tc  = $vv['dd_commodity_type'];
                        }
                        if($vv['dd_commodity_type'] > 0 || $vv['commodity_class'] == 6){
                            $have_dd_type=1;
                        }
                        if($vv['mo_id']==0){
                            $b_act_goods_price += $vv['price'] * $vv['count'];//活动前商品总价
                            $b_work_time_price += $vv['work_time_money'] * $vv['count'];//活动前运费总价
                            $act_yh      += $vv['all_dis'];
                            $card_yh      += $vv['card_all_dis'];
                            $act_sett_money      += $vv['act_sett_money'];
                            $card_sett_money      += $vv['card_sett_money'];
                            $yj_goods_price    += ($vv['actual_price'] + $vv['work_time_actual_money']) * $vv['count'];//活动前商品总价
                            $integral     += $vv['actual_point'];//使用厂家积分
                            $money        += $vv['actual_use_money'];//实际支付金钱
                            $total_money  += $vv['h_price_count'];//实际总额=钱+积分
                            if ($mail_type == 2) {
                                $mail_price += $vv['mail_price'];//运费
                            } else {
                                $mail_price = 0;//到店类运费0
                            }
                        }
//                        $logistics_mode = $vv['order_mail_type'];
                        $order_source = $vv['order_source'];
                        if ($vv['card_ids']) {
                            $card_arr = array_merge($card_arr, explode(',', $vv['card_ids']));
                        }
                        $order_goods_model->saveData($o_g_data, ['id' => $vv['id']]);
//                        echo $order_goods_model->getLastSql();
                    }
                    $time = date('Y-m-d H:i:s');
                    //查主单
                    $order       = $order_model->getOne(['where' => ['order_code' => $order_code, 'parent_order_type' => ['in', [1, 3]]]]);
                    $dlr_channel = DbDlr::channel($order['channel']);
                    $order       = $order->toArray();
                    if ($order['parent_order_type'] <> 3) {
                        $order_model->saveData(['parent_order_type' => 3, 'last_updated_date' => $time, 'dlr_code' => $dlr_channel], ['id' => $order['id']]);//原订单为父订单
                    }
                    if ($mail_type == 2) {
                        $order['dlr_code']       = $dlr_channel;

                        $order['logistics_mode'] = 2;
                        $money                   += $mail_price;
                    } else {
                        $order['logistics_mode'] = 1;
                    }
                    if(!$have_dd_type){
                        //快递类商品订单去掉车型数据--按照是否有到店类型+取送车
                        $order['order_vin']       = '';
                        $order['car_info']       = '';
                        $order['is_empower']       = 0;
                    }
                    unset($order['id']);
                    if ($card_arr) {
                        $order['card_id'] = implode(',', array_unique($card_arr));
//                        try {
//                            $order['card_id']             = implode(',',array_unique($card_arr));
//                        }catch (Exception $e){
//                            dd($card_arr);
//                        }
                    }else{
                        $order['card_id'] = '';//忽略
                    }
                    $order['is_by_tc']        = $is_by_tc;
                    $order['act_sett_money']        = $act_sett_money;
                    $order['card_sett_money']        = $card_sett_money;
                    $order['all_act_yh']        = $act_yh;
                    $order['card_money']        = $card_yh;
                    $order['all_card_yh']        = $card_yh;
                    $order['money']             = $money;
                    $order['order_source']      = $order_source;
                    $order['total_money']       = $yj_goods_price;
                    $order['order_code']        = $new_order_code;
                    $order['integral']          = $integral;
                    $order['mail_price']        = $mail_price;
                    $order['b_act_goods_price'] = $b_act_goods_price;
                    $order['b_work_time_price'] = $b_work_time_price;
                    $order['dd_dlr_code']       = $dd_dlr_code;
                    $order['parent_order_type'] = 2;//子单
                    $order['last_updated_date'] = $time;
                    if($integral>0){
                        $order['is_cc_ok'] = 1;//子单
                    }else{
                        $order['is_cc_ok'] = 0;//子单不需要用积分时候改成0
                    }
//                    echo json_encode($order);
                    //在头部删除了所有子单，这里就再重新插入
//                    if ($spi == 2) {
//                        $where = ['order_code' => $order['order_code']];
////                        unset($order['order_code']);
//                        $res = $order_model->saveData($order, $where);
//                    } else {
//                        $res = $order_model->insertGetId($order);
//                    }
                    $res = $order_model->insertGetId($order);

                    $this->orderChange($order['order_code']);

                    $order['goods_names'] = $order_goods_name_arr[$new_order_code];
                    $order['commodity_id'] = $order_goods_id_arr[$new_order_code];
                    $order_r_arr[]        = $order;

                }
            }
        }
//        die();
        return $order_r_arr;
    }


    /**
     * @param $yh_data_json_str
     * @return string
     * 根据类型返回优惠对应KEY
     */
    public function yh_info($yh_data_json_str)
    {
        //优惠解json
        if ($yh_data_json_str) {
            $yh_data_json = [];
            foreach ($yh_data_json_str as $k => $v) {
                $act_js_de = json_decode($v, true);
                $v_key     = array_keys($act_js_de);

                switch ($v_key[0]) {
                    case 1:
                        $yh_data_json[$k]['limit_id'] = $act_js_de[$v_key[0]];
                        break;
                    case 2:
                        $yh_data_json[$k]['group_id'] = $act_js_de[$v_key[0]];
                        break;
                    case  3:
                        $yh_data_json[$k]['full_id'] = $act_js_de[$v_key[0]];
                        break;
                    case  5:
                        $yh_data_json[$k]['suit_id'] = $act_js_de[$v_key[0]];
                        break;
                    case  6:
                        $yh_data_json[$k]['n_dis_id'] = $act_js_de[$v_key[0]];
                        break;
                    case  7:
                        $pre_sale_id                     = $act_js_de[$v_key[0]];
                        $yh_data_json[$k]['pre_sale_id'] = $pre_sale_id;
                        break;
                    case  8:
                        $yh_data_json[$k]['limit_id'] = $act_js_de[$v_key[0]];
                        break;
                    case  9:
                        $yh_data_json[$k]['serv_pack_id'] = $act_js_de[$v_key[0]];
                        break;
                    case  10:
                        $yh_data_json[$k]['seckill_id'] = $act_js_de[$v_key[0]];
                        break;
                    case  11:
                        $yh_data_json[$k]['crowd_id'] = $act_js_de[$v_key[0]];
                        break;
                    case  12:
                        $yh_data_json[$k]['gift_id'] = $act_js_de[$v_key[0]];
                        break;
                }
            }
//            dd($yh_data_json);
            return $yh_data_json;
        }
    }

    /**
     * 根据类型返回优惠对应KEY
     */
    public function yh_wi_info($yh_data_json_str)
    {
        if ($yh_data_json_str) {
            $yh_data_json = [];
            foreach ($yh_data_json_str as $k => $v) {
                $act_js_de = json_decode($v, true);
                $v_key = array_keys($act_js_de);

                switch ($v_key[0]) {
                    case 1:
                        $yh_data_json[$k]['limit_id'] = $act_js_de[$v_key[0]];
                        break;
                    case  3:
                        $yh_data_json[$k]['full_id'] = $act_js_de[$v_key[0]];
                        break;
                }
            }
            return $yh_data_json;
        }
    }

    /**
     * @param $act_id 活动类型ID
     * @param $type 1 返回进行中未开始的活动列表
     * @return array|array[]|bool|\PDOStatement|string|\think\Collection|void
     */
    public function yh_list($act_id='',$type = 1,$param=[])
    {
        $data = [
            1=>['title'=>'限时优惠','id'=>1],
            2=>['title'=>'秒杀活动','id'=>2],
            3=>['title'=>'满优惠','id'=>3],
            4=>['title'=>'N件N折','id'=>4],
            5=>['title'=>'多人拼团','id'=>5],
            6=>['title'=>'优惠套装','id'=>6],
            7=>['title'=>'众筹活动','id'=>7],
            8=>['title'=>'买赠活动','id'=>8],
            9=>['title'=>'抽奖活动','id'=>9],
            ];
        if(!$act_id){
            return $data;
        }
        if($type==1){
            $date_now = date('Y-m-d H:i:s');
            $file = 'id,title,start_time ,end_time';
            $where = ['end_time'=>['>=',$date_now],'is_enable'=>1];
            if($param['title']){
                $where['title'] = $param['title'];
            }
//            if(in_array($act_id,[3,4,5])){
//                $where = ['end_time'=>['>=',$date_now],'is_enable'=>1];
//                if($param['title']){
//                    $where['activity_title'] = $param['title'];
//                }
//                $file="id,activity_title title,start_time,end_time";
//            }

            if($act_id==1){
                $where['set_type'] = 5;
                $ac_model =  new DbLimitDiscount();
            }elseif($act_id==2){
                $where['set_type'] = 5;
                $ac_model =  new DbSeckill();
            }elseif ($act_id==3){
                $where = ['end_time'=>['>=',$date_now],'is_enable'=>1];
                if($param['title']){
                    $where['activity_title'] = $param['title'];
                }
                $where['set_type'] = 5;
                $file="id,activity_title title,start_time,end_time";
                $ac_model =  new DbFullDiscount();
            }elseif ($act_id==4){
                $where['set_type'] = 5;
//                $where = ['end_time'=>['>=',$date_now],'is_enable'=>1];
//                $file="id,title,start_time,end_time";
                $ac_model =  new DbNDiscount();
            }elseif ($act_id==5){
                $where['set_type'] = 5;
//                $where = ['end_time'=>['>=',$date_now],'is_enable'=>1];
//                $file="id,title,start_time  ,end_time ";
                $ac_model =  new DbFightGroup();
            }elseif ($act_id==6){
                $where = ['e_time'=>['>=',time()],'is_enable'=>1];
                if($param['title']){
                    $where['name'] = $param['title'];
                }
                $where['type'] = 5;
                $file="id,name title,FROM_UNIXTIME(s_time) start_time,	FROM_UNIXTIME( e_time) end_time ";
                $ac_model =  new BuCheapSuitIndex();
            }elseif ($act_id==7){
                $where['brand'] = 1;
                $ac_model =  new DbCrowdfund();
            }elseif ($act_id==8){
                $where['set_type'] = 5;
                $ac_model =  new DbGift();
            }elseif ($act_id==9){
                $where['set_type'] = 5;
                $ac_model =  new DbDraw();
            }else{
                return [];
            }
            if($param['act_id']){
                $where['id'] = $param['act_id'];
            }
            $list =  $ac_model->getList(['where'=>$where,'field'=>$file]);
            if(!$list){
                return [];
            }
            foreach ($list as $k=>$v){
                $list[$k]['act_type'] = $data[$act_id]['title'];
            }
            return $list;

        }

    }


    //内部返回
    protected function re_msg($msg, $code = 200, $data = [])
    {
        return ['msg' => $msg, 'code' => $code, 'data' => $data];
    }

    protected function time_diff($timestamp1, $timestamp2)
    {
        if ($timestamp2 <= $timestamp1) {
            return "00:00:00";
        }
        $seconds = $timestamp2 - $timestamp1;
        if ($seconds > 3600) {
            $hours   = intval($seconds / 3600);
            $minutes = $seconds % 3600;
            $time    = $hours . ":" . gmstrftime('%M:%S', $minutes);
        } else {
            $time = gmstrftime('%H:%M:%S', $seconds);
        }
        return $time;


    }

    /**
     * 车系信息
     * @param $car_series_id
     * @return bool|mixed|string
     */
    protected function _carSeries($car_series_id)
    {
        if (!$car_series_id) {
            return '';
        }
        $key         = 'detail_car_series_' . $car_series_id;
        $data        = redis($key);
        $car_s_model = new DbCarSeries();
        if (empty($data)) {
            $data = $car_s_model->getSeriesName($car_series_id);
            redis($key, $data, mt_rand(1800, 3600));
        }

        return $data;
    }

    /**
     * @param $id
     * @param $dlr_code
     * @return bool
     * 收藏
     */
    protected function _checkCollection($id, $user_id, $brand)
    {
//        $dlr_arr         = DbDlr::channel_to_arr($channel_type)['dlr_code'];
        if(!$user_id){
            return  false;
        }
        $_params         = array(
            'where' => array('user_id' => $user_id, 'commodity_id' => $id, 'is_enable' => 1, 'brand' => $brand)
        );
        $coll_model      = new BuUserCollection();
        $user_collection = $coll_model->getOne($_params);
//        echo $coll_model->getLastSql();die();
        if ($user_collection) {
            return $user_collection;
        } else {
            return false;
        }
    }

    /**
     * 通用行为调用
     * @param $behavior
     * @param $params
     */
    public function behavior($behavior, $params)
    {

        #读取配置,查看是否唤起behavior
        $behavior_in = config('behavior_in');

        if (in_array($behavior, $behavior_in)) {
            $detail_param = $params;
            Hook::exec('app\\net_small\\behavior\\' . $behavior, 'run', $detail_param);
        }
    }

    /**
     * 会员卡号、vin码、18位码对应关系初始化
     * */
    public function _getVin18n($member, $user)
    {
        if (empty($member)) return true;
//        $redis = \think\Cache::redisHandler();
        try {
            $user_model      = new DbUser();
            $DbUserCarSeries = new DbUserCarSeries();
            if ($user['brand'] == 1) {
                // 获取用户vin码相关信息缓存
                $vin_18n_key   = config('cache_prefix.user_car_18n_list') . $user['channel_type'] . $user['plat_id'];
//                $vin_18n_key.='1---1';
                $user_vin_list = redis($vin_18n_key);
                if (empty($user_vin_list) && getRedisLock($vin_18n_key, 180)) {
                    // 初始化快捷车
                    $user_car_list = $DbUserCarSeries->where(['user_id' => $user['id'], 'car_brand_code' => $user['brand'], 'channel_type' => ''])->select();
                    foreach ($user_car_list as $v){
                        $v = $v->toarray();
                        $id = $v['id'];
                        unset($v['id']);
                        $v['channel_type'] = 'GWSM';
                        $DbUserCarSeries->insert($v);
                        $v['channel_type'] = 'GWAPP';
                        $DbUserCarSeries->insert($v);
                        $DbUserCarSeries->where('id', $id)->delete();
                    }

                    // 营销平台获取vin车
                    $user_cards = Member::create('member')->crmCards(['member_id' => $user['plat_id'], 'ignore_e3s' => 1]);
                    $car_infos = QuickWin::create('quick_win')->getCarInfos(['appCode' => 'nissan', 'oneId' => $user['one_id'], 'appSkin' => 1]);

                    $user_vin_list = []; // 认证车
                    $user_empower_list = []; // 授权车
                    foreach ($car_infos as $v){
                        if ($v['accountType'] == 1){
                            // 副账号授权接口还没有，暂时先把授权车的逻辑去掉
                            if ($user['channel_type'] == 'GWSM') continue;
                            $author = QuickWin::create('quick_win')->authorCode(['appCode' => 'nissan', 'oneId' => $user['one_id'], 'vin' => $v['vin']]);
                            $accountPowerList = $author['authInfo']['accountPowerList'] ?? [];
                            if (empty($accountPowerList) || !in_array(4, $accountPowerList)){
                                continue;
                            }
                            $user_empower_list[] = $v['vin'];
                        }
                        $user_vin_list[$v['vin']] = $v['vin'];
                    }
                    foreach ($user_cards as $v) {
                        // from=digital 营销平台不取统一认证车 --- 先去掉，万一网联接口挂了就没车了
                        if ($v['brandcode'] == 1 && !in_array($v['vin'], $user_vin_list)) {
                            $user_vin_list[$v['vin']] = $v['vin'];
                        }
                    }

                    $user_car_list = $DbUserCarSeries->where(['user_id' => $user['id'], 'channel_type' => $user['channel_type'], 'is_vin_car' => 1])->select();
                    //已经有的就不会更新了，可能会导致用户数据不能更新，就只能手工删除DbUserCarSeries
                    foreach ($user_car_list as $v) {
                        if (!in_array($v['vin'], $user_vin_list)) { // 删除已经解绑的vin车
                            $DbUserCarSeries->where(['id' => $v['id']])->delete();
                            redis(config('cache_prefix.user_car_18n_list') . $user['id'] . $user['channel_type'], null);
                        } else { // 去掉已经添加过的vin车  ----
                            unset($user_vin_list[$v['vin']]);
                        }
                    }

                    // 保存未添加的vin车
                    foreach ($user_vin_list as $vin) {
                        $car_info = $this->get_car_msg($vin);
                        if (empty($car_info) || empty($car_info['car_config_code']) || empty($car_info['car_type_id'])) continue;

                        $relate_car_18n = $car_info['car_config_code']; // 18位码

                        // 添加用户车型表
                        $user_car = [
                            'user_id'         => $user['id'],
                            'is_vin_car'      => 1,
                            'relate_car_18n'  => $relate_car_18n,
                            'vin'             => $vin,
                            'car_series_id'   => $car_info['car_type_id'] ?? '',
                            'car_series_name' => $car_info['car_series_name'],
                            'car_type_name'   => $car_info['car_type_name'],
                            'car_brand_code'  => $user['brand'],
                            'channel_type'    => $user['channel_type'], // 渠道
                            'is_empower'      => in_array($vin, $user_empower_list) ? 1 : 0, // 是否授权车型
                        ];
                        $check    = $DbUserCarSeries->getOne(['where' => ['user_id' => $user['id'], 'vin' => $vin, 'channel_type' => $user['channel_type']]]);
                        if (!$check && getRedisLock(serialize($user_car))) {
                            $DbUserCarSeries->insertGetId($user_car);
                        } else {
                            $DbUserCarSeries->saveData($user_car, ['id' => $check['id']]);
                        }
                        redis(config('cache_prefix.user_car_18n_list') . $user['id'] . $user['channel_type'], null);
                    }
                    if (empty($user_vin_list)) redis($vin_18n_key, null);
                    redis($vin_18n_key, $user_vin_list, mt_rand(600, 1200)); // 更新redis
                }
            }
            elseif ($user['brand'] == 2) {
                $vin_list_key   = config('cache_prefix.user_car_18n_list') . 'venucia' . $member['member_id'];
                $vin_new_list   = [];
                $vin_list_redis = redis($vin_list_key);
                if ($vin_list_redis) {
                    $vin_list = $vin_list_redis; // 用户认证车型列表
                }else{
                    $vin_list = $DbUserCarSeries->where(['user_id' => $user['id'], 'car_brand_code' => $user['brand'], 'is_vin_car' => 1])->column('*', 'vin');
                }
                if (!empty($member['oauth'][0]['unionid'])) {
                    $Car   = new Carer();
                    $carer = $Car->vin(array('unionid' => $member['oauth'][0]['unionid']));
                    if (!empty($carer['vin']) && $carer['car_brand_code'] == $user['brand']) {
                        $vin_new_list[$carer['vin']] = $carer['vin'];
                    }
                }
                if (!empty($member['oneid'])) {
                    $car_list = QuickWin::create('e3s_dlr')->postQcCarInfoList(['oneid' => $member['oneid'], 'appCode' => 'venucia', 'clientid' => 'venuciaapp']);// 日产：nissan  启辰：venucia
//                    print_json($car_list);
                    if (!empty($car_list['rows'])) {
                        foreach ($car_list['rows'] as $v) {
                            if ($v['accountType'] == 1) continue;
                            $vin_new_list[$v['vin']] = $v['vin'];
                        }
                    }
                }

                foreach ($vin_list as $k => $v) {
                    if (!in_array($k, $vin_new_list)) {
                        unset($vin_list[$k]);
                        $DbUserCarSeries->where(['user_id' => $user['id'], 'vin' => $k, 'car_brand_code' => $user['brand']])->delete();// 删除用户车型表数据
                        redis(config('cache_prefix.user_car_18n_list') . $user['id'] . 'brand' . $user['brand'], null);
                    } else {
                        unset($vin_new_list[$k]);
                    }
                }
                foreach ($vin_new_list as $vin) {
                    $car_info = $this->get_car_msg($vin);
                    if (empty($car_info) || empty($car_info['car_config_code']) || empty($car_info['car_type_id'])) continue;

                    $relate_car_18n = $car_info['car_config_code']; // 18位码

                    $vin_list[$vin] = $vin;

                    // 添加用户车型表
                    $user_car = [
                        'user_id'         => $user['id'],
                        'is_vin_car'      => 1,
                        'relate_car_18n'  => $relate_car_18n,
                        'vin'             => $vin,
                        'car_series_id'   => $car_info['car_type_id'] ?? '',
                        'car_series_name' => $car_info['car_series_name'],
                        'car_type_name'   => $car_info['car_type_name'],
                        'car_brand_code'  => $user['brand'],
                    ];

                    $check = $DbUserCarSeries->getOne(['where' => ['user_id' => $user['id'], 'vin' => $vin, 'car_brand_code' => $user['brand']]]);
                    if (!$check && getRedisLock(serialize($user_car))) {
                        $DbUserCarSeries->insertGetId($user_car);
                    } else {
                        $DbUserCarSeries->saveData($user_car, ['id' => $check['id']]);
                    }
                    redis(config('cache_prefix.user_car_18n_list') . $user['id'] . 'brand' . $user['brand'], null);
                }
                if (empty($vin_new_list)) redis($vin_list_key, null);
                redis($vin_list_key, $vin_new_list, mt_rand(1800, 3600));
//                $redis->sadd(config('cache_prefix.user_car_18n_set'), $vin_list_key);
            }
            elseif ($user['brand'] == 3) {
                $vin_list_key   = config('cache_prefix.user_car_18n_list') . 'ariya' . $member['member_id'];
                $vin_new_list   = [];
                $vin_list_redis = redis($vin_list_key);
                if ($vin_list_redis) {
                    $vin_list = $vin_list_redis; // 用户认证车型列表
                }else{
                    $vin_list = $DbUserCarSeries->where(['user_id' => $user['id'], 'car_brand_code' => $user['brand'], 'is_vin_car' => 1])->column('*', 'vin');
                }
                if (!empty($member['oneid'])) {
                    $car_list = QuickWin::create('e3s_dlr')->postQcCarInfoList(['oneid' => $member['oneid'], 'appCode' => 'nissan', 'clientid' => 'nissanapp']);// 日产：nissan  启辰：venucia
                    if (!empty($car_list['rows'])) {
                        foreach ($car_list['rows'] as $v) {
                            if ($v['accountType'] == 1) continue;
                            $vin_new_list[$v['vin']] = $v['vin'];
                        }
                    }
                }

                foreach ($vin_list as $k => $v) {
                    if (!in_array($k, $vin_new_list)) {
                        unset($vin_list[$k]);
                        $DbUserCarSeries->where(['user_id' => $user['id'], 'vin' => $k, 'car_brand_code' => $user['brand']])->delete();// 删除用户车型表数据
                        redis(config('cache_prefix.user_car_18n_list') . $user['id'] . 'brand' . $user['brand'], null);
                    } else {
                        unset($vin_new_list[$k]);
                    }
                }
                foreach ($vin_new_list as $vin) {
                    $car_info = $this->get_car_msg($vin);
                    if (empty($car_info) || empty($car_info['car_config_code']) || empty($car_info['car_type_id'])) continue;

                    $relate_car_18n = $car_info['car_config_code']; // 18位码

                    $vin_list[$vin] = $vin;

                    // 添加用户车型表
                    $user_car = [
                        'user_id'         => $user['id'],
                        'is_vin_car'      => 1,
                        'relate_car_18n'  => $relate_car_18n,
                        'vin'             => $vin,
                        'car_series_id'   => $car_info['car_type_id'] ?? '',
                        'car_series_name' => $car_info['car_series_name'],
                        'car_type_name'   => $car_info['car_type_name'],
                        'car_brand_code'  => $user['brand'],
                    ];
                    $check = $DbUserCarSeries->getOne(['where' => ['user_id' => $user['id'], 'vin' => $vin, 'car_brand_code' => $user['brand']]]);
                    if (!$check && getRedisLock(serialize($user_car))) {
                        $DbUserCarSeries->insertGetId($user_car);
                    } else {
                        $DbUserCarSeries->saveData($user_car, ['id' => $check['id']]);
                    }
                    redis(config('cache_prefix.user_car_18n_list') . $user['id'] . 'brand' . $user['brand'], null);
                }

                if (empty($vin_new_list)) redis($vin_list_key, null);
                redis($vin_list_key, $vin_new_list, mt_rand(1800, 3600));
//                $redis->sadd(config('cache_prefix.user_car_18n_set'), $vin_list_key);
            }
        } catch (\Exception $e) {
            Logger::error('error_18_n:' . $e->getMessage());
        }
    }


    /**
     * 获取多个商品sku的工时价格和总价
     * @param array $sku_ids sku_id列表
     * @param array $sku_num 购买数量列表
     * @param string $car_18n 18位码
     * @param string $dlr_code 经销商编码
     */
    public function getWiPriceAll(array $sku_ids, array $sku_num, string $car_18n, string $dlr_code)
    {
        $wi_price_all  = 0;
        $wi_price_list = [];
        if (!empty($sku_ids)) {
            $sku_model = new DbCommoditySku();
            $work_ids  = [];
            $sku_list  = $sku_model->where(['id' => ['in', $sku_ids]])->column('id, commodity_id, relate_car_work_hour');
            $com_ids   = array_unique(array_column($sku_list, 'commodity_id'));
            foreach ($sku_list as $k => $v) {
                $relate_car_work_hour = json_decode($v['relate_car_work_hour'], true);
                if (empty($v['relate_car_work_hour'])) {
                    unset($sku_list[$k]);
                } else {
                    foreach ($relate_car_work_hour as $key => $va) {
                        if (in_array($car_18n, $va)) {
                            $sku_list[$k]['work_id'] = $key;
                            $work_ids[]              = $key;
                            break;
                        }
                    }
                }
            }

            if (!empty($work_ids)) {
                $com_model = new DbCommodity();
                // 工时单价
                $com_list                  = $com_model->where(['id' => ['in', $com_ids]])->column('work_hour_type', 'id');
                $widata['dlr_code']        = $dlr_code;
                $widata['car_config_code'] = $car_18n;
                $hours                     = [];
                $set_sku_model             = new DbCommoditySetSku();
                foreach ($com_list as $k => $v) {
                    if (empty($v)) continue;
                    $widata['repair_type'] = $v;
                    $price_ret             = $set_sku_model->getE3sPrice($widata);
                    $hours[$k]             = $price_ret['data'][0]['wi_price'];
                }

                $work_model = new E3sPartCarSeries();
                $work_list  = $work_model->where(['id' => ['in', $work_ids], 'is_enable' => 1])->column('wi_code,wi_qty', 'id');
                foreach ($sku_list as $k => $v) {
                    $work_info = $work_list[$v['work_id']];
                    if ($work_info['wi_qty'] > 0) {
                        $wi_price        = $hours[$v['commodity_id']] * $work_info['wi_qty'];
                        $_wi_price_all   = $wi_price * $sku_num[$k];
                        $wi_price_list[] = [
                            'sku_id'           => $v['id'],
                            'sku_num'          => $sku_num[$k],
                            'commodity_id'     => $v['commodity_id'],
                            'work_time_number' => $work_info['wi_qty'],
                            'work_time_price'  => $wi_price,
                            'work_time_code'   => $work_info['wi_code'],
                            'wi_price_all'     => $_wi_price_all,
                        ];
                        $wi_price_all    += $_wi_price_all;
                    }
                }
            }
        }
        return ['wi_price_list' => $wi_price_list, 'wi_price_all' => $wi_price_all];
    }

    /**
     * 获取工时单价
     * @param $dlr_code
     * @param $car_18
     * @param $work_hour_type
     * @return int|mixed
     */
    public function getWorkPrice($dlr_code = '', $car_18 = '', $work_hour_type = '')
    {
        if (!$dlr_code || !$car_18 || !$work_hour_type) {
            return 0;
        }
        $set_sku_model = new DbCommoditySetSku();
        $ww_price_data = [
            'dlr_code'        => $dlr_code,
            'car_config_code' => $car_18,
            'repair_type'     => $work_hour_type,
        ];
        $price_ret     = $set_sku_model->getE3sPrice($ww_price_data);
        return $price_ret;
    }


    /**
     * 初始化用户车型列表redis
     * */
    public function user_car_type_redis($user)
    {
        $redis_key  = config('cache_prefix.user_car_18n_list') . $user['id'] . $user['channel_type'];
        $redis_list = redis($redis_key.'1--');
//        $redis_list = [];
        if(!$user['id']){
            $return_list = ['list' => [], 'user_status' => 0, 'have_brand_car' => 0, 'user_vin' => ['vin'=>''],'car_number'=>0];
            return  $return_list;
        }
//        $redis_list = 0;
        if ($redis_list && !empty($redis_list['list'])) {
            $return_list = $redis_list;
        } else {
            $u_car_s_model = new DbUserCarSeries();
            $params        = [
                'field' => 'a.id, a.is_vin_car, a.vin, a.is_bind, a.relate_car_18n car_config_code, a.car_series_name car_series_cn, a.car_type_name large_car_type_cn, a.car_series_id, b.car_brand_code brand,a.is_empower,b.is_nev',
                'where' => ['a.user_id' => $user['id'], 'a.car_brand_code' => $user['brand'], 'a.is_enable' => 1, 'a.relate_car_18n' => ['neq', ''], 'a.channel_type' => ''],
                'order' => 'a.is_vin_car desc,a.last_updated_date desc',
                'group' => 'a.vin',
            ];
            if ($user['brand'] == 1) $params['where']['a.channel_type'] = $user['channel_type'];

            $list          = $u_car_s_model
                ->alias('a')
                ->field($params['field'])
                ->join('t_e3s_car_series b', 'a.relate_car_18n = b.car_config_code')
                ->where($params['where'])
                ->order($params['order'])
                ->group($params['group'])
                ->select();
            $is_bind       = 0; // 是否有默认车型
            //记录当时发生的vin跟是否绑定
            $log_vin = '';
            $log_is_vin = '';
            $log_is_bind = '';
            foreach ($list as $v) {
                $log_vin.=$v['vin'].',';
                $log_is_vin.=$v['is_vin_car'].',';
                $log_is_bind.=$v['is_bind'].',';
                if ($v['is_bind'] == 1) {
                    $is_bind = 1;
                    break;
                }
            }
            Logger::error('userinforedis',['sql'=>$u_car_s_model->getLastSql(),'log_vin'=>$log_vin,'log_is_vin'=>$log_is_vin,'log_is_bind'=>$log_is_bind]);
            if ($is_bind == 0 && isset($list[0])) {
                $list[0]['is_bind'] = 1;
                $u_car_s_model->saveData(['is_bind' => 1], ['id' => $list[0]['id']]);
                DbUser::where('id', $user['id'])->update(['car_series_id' => $list[0]['car_series_id'], 'car_18n' => $list[0]['car_config_code']]);
            }
            $user_status    = 0; // 无vin车用户
            $user_vin       = []; // 用户默认车型信息
            $have_brand_car = 0; // 无当前品牌vin车
            $have_more_car = 0;//有多少台vin车
            foreach ($list as $v) {
                if($v['is_vin_car']==1){
                    $have_more_car++;
                }
                if ($v['is_vin_car'] == 1 && $user_status == 0) {
                    $user_status = 1; // 有vin车但非默认车型用户
                }
                if ($v['is_bind'] == 1) {
                    if ($v['is_vin_car'] == 1) $user_status = 2; // 默认车型为vin车用户
                    $user_vin = $v;
                    DbUser::where('id', $user['id'])->update(['car_series_id' => $v['car_series_id'], 'car_18n' => $v['car_config_code']]);
                }
                if ($v['is_vin_car'] == 1 && $v['brand'] == $user['brand']) {
                    if ($v['is_empower'] == 0){
                        $have_brand_car = 1;
                    } elseif ($have_brand_car == 0 && $user['channel_type'] == 'GWAPP'){ // 只有GWAPP才有授权车
                        $have_brand_car = 2;
                    }
                }
            }
            if (!empty($user_vin)) {
                $dbCommoditySetSkuObj       = new DbCommoditySetSku();
                $n18                        = $dbCommoditySetSkuObj->get18Code(['vin' => $user_vin['vin']]);
                $user_vin['18_oil_type']    = $n18['config_attr'][3] ?? 4;
                $user_vin['have_brand_car'] = $have_brand_car;
                $user_vin['car_offline_date'] = $n18['offline_date'];
                $user_vin['user_status'] = $user_status;
            }
            $return_list = ['list' => $list, 'user_status' => $user_status, 'have_brand_car' => $have_brand_car, 'user_vin' => $user_vin,'car_number'=>$have_more_car];
            redis($redis_key, $return_list, mt_rand(1800, 3600));
//            $redis = \think\Cache::redisHandler();
//            $redis->sadd(config('cache_prefix.user_car_18n_set'), $redis_key);
        }

        return $return_list;
    }

    /**
     * 获取用户基本信息
     * @return mixed
     */
    public function getFriendBaseInfo($user, $longitude = 0, $latitude = 0)
    {
        $buOrderObj = new BuOrder();
        $dlrObj     = new DbDlr();
        if (!empty($user['id'])) {
            $order_info    = $buOrderObj->where(['user_id' => $user['id'], 'brand' => $user['brand']])->order("id desc")->find();
            $dlr_info      = $dlrObj->maintenance_dlr($user, $longitude, $latitude);
            $user_car_info = $this->user_car_type_redis($user);
        }
        $user_info['user_name'] = $order_info['name'] ?? '';
        $user_info['phone']     = $order_info['phone'] ?? '';

        $user_info['dlr_code'] = $dlr_info['dlr_code'] ?? "";
        $user_info['dlr_name'] = $dlr_info['dlr_name'] ?? "";

        //车型
        $user_info['user_status']     = $user_car_info['user_status'] ?? "";
        $user_info['vin']             = $user_car_info['user_vin']['vin'] ?? ""; // 车型id
        $user_info['car_type_id']     = $user_car_info['user_vin']['car_series_id'] ?? ""; // 车型id
        $user_info['car_series_name'] = $user_car_info['user_vin']['car_series_cn'] ?? ""; // 车系中文名
        $user_info['car_type_name']   = $user_info['car_series_name']? $user_info['car_series_name'] . '  ' . ($user_car_info['user_vin']['large_car_type_cn'] ?? "") :''; // 车型中文名
        $user_info['car_type_code']   = $user_car_info['user_vin']['large_car_type_code'] ?? ""; // 车型编码
        $user_info['car_config_code'] = $user_car_info['user_vin']['car_config_code'] ?? ""; // 车型18位码
        $user_info['18_oil_type']     = $user_car_info['user_vin']['18_oil_type'] ?? 4; // 车型油量
        $user_info['brand']           = $user_car_info['user_vin']['brand'] ?? ''; // 车型品牌
        $user_info['have_brand_car']  = $user_car_info['user_vin']['have_brand_car'] ?? 0; // 是否拥有当前品牌的vin车
        $user_info['car_offline_date']  = $user_car_info['user_vin']['car_offline_date'] ?? ''; // 车型下架时间
//        $user_info['car_offline_date']= '';//bom前端暂时不上。等要上了用上面的。
        $user_info['car_number']  = $user_car_info['car_number'] ?? ''; // 车数
        return $user_info;
    }

    /*
     * 根据车牌号或手机号查询用户车辆信息
     * */
    public function getCarByPhonePlate($params)
    {
        $phone_cars_redis_key = config('cache_prefix.user_car_18n_list') . 'get_car_by_phone_plate' . md5($params['key']);
        $phone_cars_redis     = redis($phone_cars_redis_key);
        if ($phone_cars_redis) {
            $return_data = $phone_cars_redis;
        } else {
            $params['data']['E3S_CODE'] = 'ZZYY';
            $list                       = Third::create('third')->rpc('POST', 'proxy/e3s/', $params['data']);
            $return_data                = ['data' => []];
            if (!empty($list['DATA'])) {
                foreach ($list['DATA'] as $v) {
                    $info = $this->get_car_msg($v['VIN']);
                    if (!empty($info)) $return_data['data'][] = $info;
                }
            }
            redis($phone_cars_redis_key, $return_data, mt_rand(1800, 3600));
//            $redis = \think\Cache::redisHandler();
//            $redis->sadd(config('cache_prefix.user_car_18n_set'), $phone_cars_redis_key);
        }
        return $return_data['data'];
    }

    private function query_car($vin)
    {
        $return_data         = [];
        $query_car_redis_key = config('cache_prefix.user_car_18n_list') . 'query_car' . $vin;
        $query_car_redis     = redis($query_car_redis_key);
//        $query_car_redis = [];
        if (!empty($query_car_redis)) {
            $return_data = $query_car_redis;
        } else {
            $car_info = Crm::create('crm')->queryCar(['vin' => $vin]);

            if ($car_info['code'] == 0) {
                $return_data          = [
                    'car_series_name' => $car_info['data']['CAR_SERIES_NAME'],
                    'car_brand_code'  => $car_info['data']['CAR_BRAND_CODE'],
                    'vin'             => $vin,
                ];
                $dbCommoditySetSkuObj = new DbCommoditySetSku();
                // 判断vin是否能查到18位码
                $n18 = $dbCommoditySetSkuObj->get18Code(['vin' => $vin]);

//                if (!empty($n18) && !empty($n18['car_config_code'])) {
                    $return_data = array_merge($return_data, $n18);
//                }
                redis($query_car_redis_key, $return_data, mt_rand(1800, 3600));
//                $redis = \think\Cache::redisHandler();
//                $redis->sadd(config('cache_prefix.user_car_18n_set'), $query_car_redis_key);
            }
        }
        return $return_data;
    }

    /**
     * @param $order_cards 订单/购物车对应卡券
     * @param $card_list 用户卡券
     * @param $order_code 订单的时候需要是用
     * @return void
     * 订单或者购物车卡券价值计算
     */
    public function card_js($order_cards=[], $card_list=[], $order_code = '', $is_cache = true)
    {
        $no_card_arr   = [];
        $redis_name    = "order-all-card-list" . $order_code;//并没有使用到性能
        $user = session("net-api-user-info");
        //为了下面的  本券适用您车辆：车型+车架号，当前车辆为：车型+车架号
        $carSerModel =  new DbUserCarSeries();
        $userCarSeries = $carSerModel->getList(['where'=>['user_id' => $user['id'],'car_brand_code'=>$user['brand']]]);
        $vin_car_arr =[];
        if($userCarSeries){
            foreach ($userCarSeries as $u_v){
                if($u_v['vin']){
                    $vin_car_arr[$u_v['vin']]  = $u_v['car_series_name'].' '.$u_v['car_type_name'];
                }
            }
        }
        $all_card_list = [];
        $one_goods_sp_arr=[];//特殊商品+数量
        if ($order_cards) {
            $mj_card_ids = [];
            $mj_card_arr = [];
            //增加一个卡券在领取记录表的判断
            $have_get_card_arr =  array_column($card_list,'id');
            //整理出总价格以及优惠价格...

            foreach ($order_cards as $m_k=> $m_v) {
                $can_use_card = 1;
                $g_s_sid_arr = explode(',',$m_v['g_s_sid']);
                //按照本版本sku卡券要在这里进行过滤，不然会导致下面的计算异常，直接从这里去掉
                //比如不匹配的的规格会进入到goods_res里面计算
                //同样的商品，不同的规格，可能就会有一个可用一个不可用，就要去掉
                //如果要显示在不可用里，那就要把这里的不可用ID加入数组，在下面与可用的进行差集，然后在从card_list里面循环获得
                if($m_v['sku_c_json']){
                    $sku_c_json =  json_decode($m_v['sku_c_json'],true);
                    $sku_id_key = array_column($sku_c_json,"sku_id");
                    //如果是ID的就拿ID，如果不是就拿KEY，从购物车过来是key
                    if(!$sku_id_key){
                        $sku_id_key =  array_keys($sku_c_json);
                    }
                    $can_use_card = $this->_cl_can_card($m_v,$sku_id_key);
//                    if($m_v['id']=='20529434621674496'){
////                        dd($can_use_card);
//                    }
                }else{
                    if(!in_array($m_v['sku_id'],$g_s_sid_arr)&& $m_v['g_s_sid']){
                        $can_use_card=0;
                    }
                }
//                    if($m_v['id']=='28799398767002624'){
//                        print_json($can_use_card,$m_v['sku_id'],$g_s_sid_arr);
//
//                    }

                if(in_array($m_v['id'],$have_get_card_arr) && $can_use_card==1){
                    $mj_data = [
                        'sku_id'        => $m_v['sku_id'],
                        'id'            => $m_v['id'],
                        'cid'           => $m_v['cid'],
                        'card_type'     => $m_v['card_type'],
                        'commodity_id'  => $m_v['commodity_id'],
//                    'commodity_set_id' => $m_v['commodity_set_id'],
                        'price'         => $m_v['price'],
                        'count'         => $m_v['count'],
                        'card_discount' => $m_v['card_discount'],
                        'card_quota'    => $m_v['card_quota'],
                        'least_cost'    => $m_v['least_cost'],
                        'max_discount'    => $m_v['max_discount'],
                        'receive_scene'    => $m_v['receive_scene'],
                        'dd_dlr_code'    => $m_v['dd_dlr_code']??'',
                        'is_gift_card'    => $m_v['is_gift_card']??0,
                        'dlr_price'    => $m_v['dlr_price']??0,
                        'is_gift'    => $m_v['is_gift']??0,
                        'card_code'    => $m_v['card_code']??0,
                    ];
                    if(in_array($m_v['receive_scene'],$this->spe_receive_scene) || $m_v['is_gift_card']==1){
                        $one_goods_sp_arr[$m_v['cid'].implode(',',$this->spe_receive_scene)] =$m_v['count'];
                        $mj_count = $mj_data['count'];
                        for ($i = 0; $i < $mj_count; $i++) {
                            $mj_data['count']=1;
                            $mj_card_arr[$m_v['id']]['list'][] = $mj_data;

                        }
                    }else{
                        $one_goods_sp_arr[$m_v['cid']] =$m_v['count'];
                        $mj_card_arr[$m_v['id']]['list'][] = $mj_data;
                    }

                }
                if(!$can_use_card){
                    unset($order_cards[$m_k]);
                }
            }
//            if($is_cache){
//                print_json($mj_card_arr,$order_cards,$card_list);
//            }

            if ($mj_card_arr) {
//                $mj_card_ids =  array_unique($mj_card_ids);
                foreach ($mj_card_arr as $kk => $mj_val) {
                    $mj_card_value = 0;
                    foreach ($mj_val['list'] as $m_list) {
                        if(in_array($m_list['receive_scene'],$this->spe_receive_scene) || $m_list['is_gift_card']==1){
                            $mj_card_value += $m_list['price'] ;
                        }else{
                            $mj_card_value += $m_list['price'] * $m_list['count'];
                        }
                    }
                    $mj_card_arr[$kk]['card_all_price'] = round($mj_card_value, 1);//计算总价-只计算到1位
                }
                foreach ($mj_card_arr as $k => $val) {
                    $mj_card_value = 0;
                    $toEnd         = count($val['list']);
                    $i             = 1;//
                    $y_yh_m        = 0;//已优惠
                    foreach ($val['list'] as $kk => $vv) {
                        $vv_price_count = bcmul($vv['price'], $vv['count'], 2);
                        if ($vv['card_type'] == 2) {
                            $yh_money = round(($vv['price'] - ($vv['price'] * $vv['card_discount'] / 10)) * $vv['count'], 1);

                        } else {
                            //特殊的券一张券只会用于一个商品
                            if(!in_array($vv['receive_scene'],$this->spe_receive_scene) || $vv['is_gift_card']==1){
                                if (0 === --$toEnd) {
                                    $yh_money = min(bcsub($vv['card_quota'], $y_yh_m, 2), $vv_price_count);//优惠价格就是已经有数量的了
//                                $yh_money = $vv['card_quota'] - $y_yh_m;//优惠价格就是已经有数量的了
                                } else {
                                    $yh_money = round(min($vv['price'] * $vv['card_quota'] / $val['card_all_price'] * $vv['count'],$vv_price_count), 1);//如果优惠后价格高，就取优惠价*count
                                    $y_yh_m   += $yh_money;
                                }
                                $i++;
                            }else{
                                $yh_money = min($vv['card_quota'],$vv['price']);
                            }

                        }
                        $mj_card_arr[$k]['list'][$kk]['yh_money']       = sprintf("%.2f", round($yh_money, 2));
                        $mj_card_arr[$k]['list'][$kk]['card_all_price'] =$yh_money;//修改为实际优惠金额
//                        $mj_card_arr[$k]['list'][$kk]['card_all_price'] = $val['card_all_price'];
                    }
                }
            }
            $use_card_list  = [];
            $sort           = [];
            $no_card_id_arr = [];
            $extra_card_arr = [];
            if(!empty($order_cards)){
//                $all_card_item_tmp = [];
//                foreach($order_cards as $all_card_item){
//                    $all_card_item_tmp[] = $all_card_item['id'];
//                }
                //用券不需要判断了
//                $netUserObj = new NetUser();
//                //这个位置没有从前端传，只能拿缓存了
//                $this->user=session('net-api-user-info');
//                $this->channel_type=session('net_api_channel_type');
//                $extra_card_arr = $netUserObj->canGetCards($this->user,$all_card_item_tmp,$this->channel_type);
//                if(empty($extra_card_arr))  $extra_card_arr = [];

            }
            $use_card_code=[];
//            print_json($order_cards,$card_list,$mj_card_arr);
//            print_json($order_cards,$card_list);
            foreach ($order_cards as $key => $v) {
                $value = 0;
                $cid_arr = [];
                $q_time1 = [];
                if(!$v['count']){
                    continue;
                }
                foreach ($card_list as $k => $card_val) {
//                    echo $v['id'].'---'.$card_val['id']."<br/>";
//                    var_dump($card_val);die();
                    // 一张特殊卡券用在一个商品上
                    $in_card_arr = 0;
                    $in_card___r = '';
//                    if($card_val['card_code']){
//                        if(isset($cid_arr[$card_val['card_code']])){
//                            $in_card_arr=1;
//                        }
//                    }
                    $pre_card_code=[];



                    if($v['least_cost']){
                        $v['least_cost'] = number_format($v['least_cost']??0,2,'.','');
                    }
//                    $card_val['coupon_least_cost'] = $v['coupon_least_cost'];
                    //前端取
                    if($v['max_discount']){
                        $v['max_discount'] = number_format($v['max_discount']??0,2,'.','');
//                        $v['max_discount'] = number_format($v['max_discount']??0,2);
                        $card_val['coupon_max_discount'] =$card_val['max_discount'] = $v['max_discount'];
                    }


                    if (!empty($card_val['least_cost'])) {
                        $card_val['word'] = sprintf("满￥%s使用", $card_val['least_cost']);
                    }
//
//                        if($card_val['id']==30309588858340352 && $v['id'] ==$card_val['card_id']){
////                            print_json($mj_card_wi_id['card_all_price'],$card_val['least_cost'],$can_use_card);
//                            print_json($v['cid'],$pre_card_code,$in_card_arr);
//
//                        }

                    //&& isset($mj_card_arr[$card_val['id']])
                    $one_goods_sp_key = $v['cid'].implode(',',$this->spe_receive_scene);
                    if(in_array($v['receive_scene'],$this->spe_receive_scene) || $v['is_gift_card']==1){
                        //(!isset($one_goods_sp_arr[$one_goods_sp_key]) || (isset($one_goods_sp_arr[$one_goods_sp_key]) && $one_goods_sp_arr[$one_goods_sp_key]>0) )
                        if(in_array($card_val['card_code'],$use_card_code) || empty($one_goods_sp_arr[$one_goods_sp_key])){
                            $in_card_arr=1;
                            $in_card___r='use_card_code';
                        }
                        //订单才有，购物车没有
                        if(isset($v['pre_card_code']) && $v['is_gift_card']==1){
                            if($v['pre_card_code']){
                                $pre_card_code =  explode(',',$v['pre_card_code']);
                            }
                            if($v['g_c_cc']){
                                $card_code_arr =  explode(',',$v['g_c_cc']);
                            }
                            if(in_array($v['status'],[5,7]) && !in_array($card_val['card_code'],$pre_card_code) ){
                                $in_card_arr = 1;
                                $in_card___r='no_status';
                            }
                        }

                    }

                    //$card_val['card_id'] 改成card_code
                    if(isset($v['card_code'])){
                        if($pre_card_code){
                            $bool = in_array($card_val['card_code'],$pre_card_code);
                        }elseif(isset($v['g_c_cc'])){
                            $pp_card_code_arr = explode(',',$v['g_c_cc']);
                            $bool = in_array($card_val['card_code'],$pp_card_code_arr);
                        }else{
                            $bool = $v['card_code'] ==$card_val['card_code'];

                        }
                    }else{
                        $bool = $v['card_id'] ==$card_val['card_id'];
                    }
                    if($bool){
                        //特殊卡券被使用了就不能再用
//                    ($v['status']==1 || (in_array($v['status'],[5,7]) && in_array($v['card_code'],$pre_card_code))) &&

                    }

//
//                    if('kafkac20241213135003kmy'==$card_val['card_code']){
//                        print_r($pre_card_code);
//                    }
                    if($bool &&  empty($in_card_arr) ){

                        if (empty($card_val['least_cost'])) {
                            $card_val['word'] = sprintf("部分商品可用");
                        }
                        $card_val['car_s_name'] = $v['car_s_name']??'';
                        $card_val['apply_dlr_name'] = $v['apply_dlr_name']??'';
                        $card_val['use_vin'] = $v['use_vin']??'';

                        $card_val['car_config_cn'] = $v['car_config_cn']??'';


                        $card_s_date = $card_val['validity_date_start'];
                        $card_e_date = $card_val['validity_date_end'];
                        $card_val['validity_date_start'] = date('Y-m-d',strtotime($card_val['validity_date_start']));
                        $card_val['validity_date_end'] = date('Y-m-d',strtotime($card_val['validity_date_end']));
                        $card_val['card_date'] = date('Y.m.d',strtotime($card_val['validity_date_start'])) . '-' .date('Y.m.d',strtotime($card_val['validity_date_end']));
//                        if($card_val['id'] == 1518){
//                            Logger::error('testcardnotuse',['csd'=>$card_s_date,'ced'=>$card_e_date,'dd'=>date('Y-m-d').' 00:00:00']);
//                        }
                        $can_use_card = 1;
                        $g_s_sid_arr = explode(',',$v['g_s_sid']);
                        if(in_array($v['receive_scene'],$this->spe_receive_scene) || $v['is_gift_card']==1){
                            $cid_arr[$card_val['card_code']]=$v['cid'];
                        }
                        $card_val['cid']=$v['cid'];

//                        if($v['sku_c_json']){
//                            $sku_c_json =  json_decode($v['sku_c_json'],true);
//                            $sku_id_key = array_column($sku_c_json,"sku_id");
//                            //如果是ID的就拿ID，如果不是就拿KEY，从购物车过来是key
//                            if(!$sku_id_key){
//                                $sku_id_key =  array_keys($sku_c_json);
//                            }
//                            $can_use_card = $this->_cl_can_card($v,$sku_id_key);
//                        }else{
////                            var_dump($g_s_sid_arr);
////                            var_dump($v['sku_id']);
//                            // && !in_array($v['cid'],$g_s_sid_arr)  skuid=set_skuid 这就可以了
////                            if($v['id']==16818459483603968){
////                                var_dump($g_s_sid_arr);
////                            var_dump($v['sku_id']);
////                            die();
////                            }
//
//                            if(!in_array($v['sku_id'],$g_s_sid_arr)&& $v['g_s_sid']){
//                                $can_use_card=0;
//                            }
//                        }
//                        print_json($can_use_card,$v['sku_c_json']);
//                        die();
                        $mj_card_wi_id = $mj_card_arr[$card_val['id']];//有满减的话，这里就应该有 判断总额

                        if(in_array($v['receive_scene'],$this->spe_receive_scene) || $v['is_gift_card']==1){
                            $qt_time_value =[];
                            //获取那个商品对应的卡券列表，同卡券可能异常
                            foreach ($mj_card_wi_id['list'] as $qv_v){
                                if($qv_v['cid']==$v['cid']){
                                    $qt_time_value[]=$qv_v;
                                }
                            }
                            if(isset($q_time[$v['id']])){
                                $q_time1[$v['id']]++;
                            }else{
                                $q_time1[$v['id']]=0;
                            }
                            //倒序商品价格
                            usort($qt_time_value, function ($a, $b) {
                                return $b['price'] <=> $a['price'];
                            });

                            $card_value_list = [$qt_time_value[$q_time1[$v['id']]]];//卡券价值在上面计算好了，这里做统计
                        }else{
                            $card_value_list = $mj_card_wi_id['list'];//卡券价值在上面计算好了，这里做统计

                        }
                        $value           = 0;
                        foreach ($card_value_list as $vvv) {
                            $value += $vvv['yh_money'];
                        }
//                        if($is_cache && $v['cid']==4582241 && $v['id']==30772097894614016){
//
//                            print_json($value);
//                        }
                        //
                        $card_val['value'] = $card_val['old_value'] = round($value, 2);
                        if($v['max_discount']>0 && $v['max_discount']<$card_val['value'] && $v['max_discount']!='0.00'){
                            $card_val['value'] = (float)$v['max_discount'];

                        }
                        $card_val['card_goods_all_price']  = $mj_card_wi_id['card_all_price'];
                        if (!empty($card_val['least_cost'])) {
                            $card_db_list[$card_val['id'] . 'price'] = 0;
                            //商品总价《满减额度
                            if ($mj_card_wi_id['card_all_price'] < (float)$card_val['least_cost']) {
                                $can_use_card = 0;
                            }
                        }
                        $check_vin =1;
                        if($v['select_obj']){
                            if(in_array($v['select_obj'],[1])){
                                $check_vin=0;
                            }
                        }
                        if($card_val['receive_vin'] && $check_vin){
                            if($card_val['receive_vin']!=$v['order_vin']){
                                $one_vin_car_arr = $vin_car_arr[$card_val['receive_vin']]??'';
                                $can_use_card = 0;
                                if(!$v['order_vin']){
                                    $card_val['not_use_word'] = sprintf("本券适用您车辆：%s %s",$one_vin_car_arr,$card_val['receive_vin']);

                                }else{
                                    $card_val['not_use_word'] = sprintf("本券适用您车辆：%s %s，当前车辆为：%s %s",$one_vin_car_arr,$card_val['receive_vin'],$one_vin_car_arr,$v['order_vin']);

                                }
                            }
                        }
                        //当前车型不匹配卡券车型则不能用
                        if($v['car_series_id_str']){
                            if(!in_array($v['car_series_id'],explode(',',$v['car_series_id_str']))){
                                $can_use_card = 0;
                                $card_val['not_use_word'] = "不适用于当前订单所选的车型";
                            }
                        }

                        //不适用于当前订单所选专营店
                        if($v['apply_dlr_code'] && $v['dd_dlr_code']){
                            if(!in_array($v['dd_dlr_code'],explode(',',$v['apply_dlr_code']))){
                                $can_use_card = 0;
                                $card_val['not_use_word'] = "不适用于当前订单所选专营店";
                            }
                        }
//                        if(!$can_use_card){
//                            foreach ($mj_card_arr as $mj_k => $mj_v){
//
//                            }
//                        }
//                        if($card_val['id']==30768556719244288){
//                            print_json($card_val,$extra_card_arr,$can_use_card);
//
//                        }
                        if(!(date('Y-m-d') >= $card_val['validity_date_start'] && date('Y-m-d') <= $card_val['validity_date_end'])){
                            $card_val['disabled_remark'] = '不在适配时间内';
                            $can_use_card = 0;
                        }

//                        echo $v['card_code'].'|||';
                        if ($can_use_card==1) {
                            $use_card_list[$k] = $card_val;
                            $sort[$k]          = $value;//按价值从高到低排序
                            if(in_array($v['receive_scene'],$this->spe_receive_scene) || $v['is_gift_card']==1){
//                                echo $v['id'] .'---'.$card_val['card_id'].'+++'.$v['receive_scene'].'==='.$v['is_gift_card'].';;;';

                                $one_goods_sp_arr[$one_goods_sp_key]--;
                                $use_card_code[]=$card_val['card_code'];
                            }
                        } else {
                            $card_val['can_use_ca'] = $can_use_card;
                            $card_val['can_list_arr'] = $extra_card_arr;
                            $no_card_arr[] = $card_val;
//                        $no_card_id_arr[]=$v['id'];
                        }
                    }

                }
            }
//            dd(33324455);

            array_multisort($sort, SORT_DESC, $use_card_list);//重新排序
//            foreach ($use_card_list as $u_k=>$u_v){
//                if(isset($u_v['id'])){
//                    unset($use_card_list[$u_k]);
//                }
//            }
//            print_json($use_card_list);
            $all_card_list = $use_card_list;//合并数组，拼合多个商品的券
//            print_json($use_card_list,$no_card_arr);
//            $all_card_list = array_merge($all_card_list, $card_list);//合并数组，拼合多个商品的券
            if ($no_card_arr) {
                $no_card_arr    = array_diff($no_card_arr, $use_card_list);

                $no_card_db_arr = [];
                foreach ($no_card_arr as $k=> $v) {
                    if (in_array($v['id'], $no_card_db_arr)) {
                        unset($no_card_arr[$k]);
                    }
                    $no_card_db_arr[] = $v['id'];
                }
            }
            $no_card_arr = array_values($no_card_arr);
            if ($is_cache) {
                redis($redis_name . '-no', $no_card_arr, 3600 * 24);//保存24个小时缓存
            }
            if ($all_card_list) {
                $card_db_arr = array();
                foreach ($all_card_list as $k => $v) {
                    if (in_array($v['id'], $card_db_arr) && !in_array($v['receive_scene'],$this->spe_receive_scene) &&  $v['is_gift_card']!=1) {
                        unset($all_card_list[$k]);
                    }
                    $card_db_arr[] = $v['id'];//验证是否已经插入过了过滤重复的卡券 代金券留存最高价值的券 $sort[$key] = $value
                }
//                print_json($all_card_list);
                $q_time=[];
                foreach ($all_card_list as $k => $v) {
                    if(in_array($v['receive_scene'],$this->spe_receive_scene) ||  $v['is_gift_card']==1){
                        $mj_card_wi_id = $mj_card_arr[$v['id']];
                        $qt_time_value=[];
                        foreach ($mj_card_wi_id['list'] as $qv_v){
                            if($qv_v['cid']==$v['cid']){
                                $qt_time_value[]=$qv_v;
                            }
                        }
                        if(isset($q_time[$v['id']])){
                            $q_time1[$v['id']]++;
                        }else{
                            $q_time1[$v['id']]=0;
                        }

                        if(isset($qt_time_value[$q_time1[$v['id']]])){
                            $card_with_id_arr               = [$qt_time_value[$q_time1[$v['id']]]];
                        }else{
                            unset($all_card_list[$k]);
                            continue;
                        }
                    }else{
                        $card_with_id_arr               = $mj_card_arr[$v['id']]['list'];
                    }

                    //这里需要重新计算带有最高抵扣的折扣券啊
                    if($v['old_value']>$v['value'] && $v['card_type']==2){
                        $y_yh_m = 0;

                        foreach ($card_with_id_arr as $cc_k=> $cc_v){
                            $toEnd = count($card_with_id_arr);
                            $vv_price_count = bcmul($cc_v['price'],$cc_v['count'],2);//价格乘数量
                            if (0 === --$toEnd) {
                                $yh_money = min(bcsub($v['value'], $y_yh_m, 2), $vv_price_count);//优惠价格就是已经有数量的了
//                                $yh_money = $vv['card_quota'] - $y_yh_m;//优惠价格就是已经有数量的了
                            } else {
                                $yh_money = round(min(($vv_price_count*$v['value']/$v['card_goods_all_price']),$vv_price_count),2);
//                                $yh_money = round(min($vv['price'] * $vv['card_quota'] / $val['card_all_price'] * $vv['count'],$vv_price_count), 1);//如果优惠后价格高，就取优惠价*count
                                $y_yh_m   += $yh_money;
                            }
//                            print_json($yh_money);

                            $card_with_id_arr[$cc_k]['yh_money'] = sprintf("%.2f",$yh_money);
                        }
                    }
                    //yh_money 单独商品的优惠金额+++是每个卡券的总优惠金额
                    //

                    $all_card_list[$k]['goods_res'] = $card_with_id_arr;
                }
            }
        } else {
            // 当无适用订单卡券时，要显示不可用列表
            $no_card_arr = $card_list;
            if ($no_card_arr) {
                $no_card_db_arr = [];
                foreach ($no_card_arr as $k => $v) {
                    if (in_array($v['id'], $no_card_db_arr)) {
                        unset($no_card_arr[$k]);
                    }
                    $no_card_arr[$k]['no_card_re'] = 'no_order';
                    $no_card_arr[$k]['validity_date_start'] = date('Y-m-d', strtotime($v['validity_date_start']));
                    $no_card_arr[$k]['validity_date_end'] = date('Y-m-d', strtotime($v['validity_date_end']));
                    $no_card_arr[$k]['card_date'] = date('Y.m.d', strtotime($v['validity_date_start'])) . '-' . date('Y.m.d', strtotime($v['validity_date_end']));
                    $no_card_db_arr[] = $v['id'];

                    if (in_array($v['id'], $no_card_db_arr)) {
                        unset($no_card_arr[$k]);
                        continue;//没有加这个就会导致变成不是对象，就会报错toarray()
                    }

                }
            }
            $no_card_arr = array_values($no_card_arr);
        }
//        print_json($all_card_list);

        if (!$all_card_list) {
            $all_card_list = [];
        }
        $all_card_list = array_values($all_card_list);

        if ($is_cache) {
            redis($redis_name, $all_card_list, 3600 * 24);//保存24个小时缓存
        }
//        print_json($all_card_list);

//        if($is_cache){
//            print_json($all_card_list);
//        }
        $data = ['all_card_list' => $all_card_list, 'no_card_list' => $no_card_arr];
//        print_json($data);
        return $data;
    }

    //获取最优卡券
    public function best_card($all_card_list)
    {

        $rt_data = ['card_yh_arr' => [], 'card_yh_all_money' => 0,'list'=>[],'card_yh_not_rel_money'=>0];
        if(!$all_card_list){
            return $rt_data;
        }
        $more_card_arr = [];
        $one_card_arr  = [];
        $card_id_arr= [];
        $card_act_id_arr = [];
//        print_json($all_card_list);
        foreach ($all_card_list as $vv) {
//            if ($vv['can_with'] == 1) {
//                $more_card_arr[] = $vv;
//            } else {
//                $one_card_arr[] = $vv;
//            }
            $card_id_arr[]=$vv['id'];
            if($vv['rec_act_id']){
                $card_act_id_arr[]=$vv['rec_act_id'];
            }
        }
//        $card_model =  new DbCard();

//        $card_act_where = ['a.id'=>['in',$card_id_arr]];
//       activity_mutex_flag` '是否互斥活动 0-否 1-是',
//  `mutex_activity_list`'互斥活动id',
//  `coupon_mutex_superpose_type`'卡券策略：1-与指定券叠加 2-与指定券互斥 3-与全部券叠加 4-与全部券互斥',
//  `coupon_id`  '互斥/可叠加优惠券id',
//        $card_act_list =  $card_model->cardActive(['where'=>$card_act_where,'field'=>"a.id,a.activity_id,act.coupon_mutex_superpose_type,act.coupon_id,act.activity_mutex_flag,act.mutex_activity_list,a.quick_win_card_id"]);
        $card_act_data=[];
        $redis_name =  'best_card.'.implode(',',$card_id_arr);
        $act_card_hc_arr=  \redis($redis_name);
        $act_card_hc_arr=[];
        if(!$act_card_hc_arr){
            $act_moddel =  new DbActivity();
            $act_where = ['a.activity_id'=>['in',$card_act_id_arr],'b.card_id'=>['in',$card_id_arr]];

            $act_list =  $act_moddel->alias('a')->join('t_db_activity_card b','a.activity_id=b.activity_id')
                ->where($act_where)->field('b.*,a.coupon_mutex_superpose_type,a.activity_mutex_flag,a.mutex_activity_list')->select();
            $act_arr = [];
            foreach($act_list as $act_v){
                $act_arr[$act_v['activity_id'].$act_v['card_id']] = $act_v;
            }
            $all_card_list = collection($all_card_list)->toArray();
            foreach ($all_card_list as $k=> $vv) {
                $card_act = $act_arr[$vv['rec_act_id'].$vv['id']]??[];
                $act_not_with='';
                $can_use = '';
                $can_no_use = '';
                if($vv['id']==20348052837532672){
//                print_json($card_act);
                }
                if($card_act){

                    if($card_act['activity_mutex_flag']==1){
                        $act_not_with =$card_act['mutex_activity_list'];
                    }
                    if($card_act['coupon_mutex_superpose_type']==1){
                        $can_use = $card_act['coupon_id'];
                    }
                    if($card_act['coupon_mutex_superpose_type']==2){
                        $can_no_use = $card_act['coupon_id'];
                        $can_use='';
                    }
                    if($card_act['coupon_mutex_superpose_type']==3){
                        $can_use = 'all';
                        $can_no_use = '';
                    }
                    if($card_act['coupon_mutex_superpose_type']==4){
                        $can_no_use = 'all';
                        $can_use='';

                    }
                }else{
//                $act_not_with='all';//活动全部不能用

                    $act_not_with = '';

                    if($vv['can_with']){
                        $can_use = 'all';
                        $can_no_use = '';
                    }else{
                        $can_no_use = 'all';
                        $can_use='';
                    }
                }
                $vv['act_card_id'] = $vv['quick_win_card_id'];
                $vv['can_use'] = $can_use;
                $vv['can_no_use'] = $can_no_use;
                $vv['act_id'] = $vv['rec_act_id'];
                $vv['can_no_with'] = $act_not_with;
//                $ca_data= ['act_card_id' =>  $vv['quick_win_card_id'], 'can_use' => $can_use, 'can_no_use' => $can_no_use, 'act_id' => $vv['rec_act_id'], 'can_no_with' => $act_not_with];
//            print_json($vv,$ca_data);
                $all_card_list[$k] = $vv;
//            try{
//                $card_act = $card_act_data[$vv['id']];
//                $all_card_list[$k] = array_merge($vv,$card_act);
//            }catch (Exception $e){
//                print_json($vv);
//            }

            }
//        foreach ($card_act_list as $v){
//            $act_not_with='';
//            $can_use = '';
//            $can_no_use = '';
//            if($v['activity_mutex_flag']==1){
//                $act_not_with =$v['mutex_activity_list'];
//            }
//            if($v['coupon_mutex_superpose_type']==1){
//                $can_use = $v['coupon_id'];
//            }
//            if($v['coupon_mutex_superpose_type']==2){
//                $can_no_use = $v['coupon_id'];
//            }
//            if($v['coupon_mutex_superpose_type']==3){
//                $can_use = 'all';
//            }
//            if($v['coupon_mutex_superpose_type']==4){
//                $can_no_use = 'all';
//            }
//            if(!$v['activity_id']){
//                $act_not_with='all';
//                $can_no_use = 'all';
//            }
//
//        }
//        $all_card_list = collection($all_card_list)->toArray();
//        foreach ($all_card_list as $k=> $vv) {
//            $card_act = $card_act_data[$vv['id']];
//            $all_card_list[$k] = array_merge($vv,$card_act);
////            try{
////                $card_act = $card_act_data[$vv['id']];
////                $all_card_list[$k] = array_merge($vv,$card_act);
////            }catch (Exception $e){
////                print_json($vv);
////            }
//
//        }
            if(!$all_card_list){
                return  $rt_data;
            }
            $act_card_hc_arr = $this->act_card_hc($all_card_list);
            \redis($redis_name,$act_card_hc_arr,10);
        }

        $act_combo = $act_card_hc_arr['best'];
        //card_yh_not_rel_money 虚拟价格，比如买赠券是999999
        return ['card_yh_arr' => $act_combo['combo'], 'card_yh_all_money' => round($act_combo['total_value'], 2), 'card_yh_not_rel_money' => round($act_combo['not_rel_value'], 2),'list'=>$act_card_hc_arr['list']];


//        $more_card_money = 0;
//        $one_card_money  = 0;
//        if ($more_card_arr) {
//            $more_card_money = array_sum(array_column($more_card_arr, 'value'));
//        }
//        if ($one_card_arr) {
//            $one_card_money = max(array_column($one_card_arr, 'value'));
//        }
//
//        if ($more_card_money >= $one_card_money || ($more_card_money==$one_card_money && $one_card_money==0)) {
//            $card_yh_all_money = $more_card_money;
//            $card_yh_arr       = array_values($more_card_arr);
//        } else {
//            $key = array_column($one_card_arr, 'value');//取出数组中serverTime的一列，返回一维数组
//            array_multisort($key, SORT_DESC, $one_card_arr);//排序，根据$serverTime 排序
//            $card_yh_all_money = isset($one_card_arr[0]['value'])?$one_card_arr[0]['value']:0;
//            $card_yh_arr       = [$one_card_arr[0]];
//        }
//        return ['card_yh_arr' => $card_yh_arr, 'card_yh_all_money' => round($card_yh_all_money, 2)];
    }

    public function get_car_msg($vin)
    {
        $redis_key  = config('cache_prefix.user_car_18n_list') . 'get_car_msg' . $vin;
        $redis_data = redis($redis_key);
        if (!empty($redis_data)) {
            $query_car_info = $redis_data;
        } else {
            $query_car_info = $this->query_car($vin);
            if (empty($query_car_info) || empty($query_car_info['car_config_code'])) return []; // 没有查到车辆信息直接返回

            $e3s_car_model                         = new E3sCarSeries();
            $car_type                              = $e3s_car_model->getOne(['where' => ['car_config_code' => $query_car_info['car_config_code']]]);
            $query_car_info['car_type_id']         = $car_type['id'] ?? '';
            $query_car_info['car_type_name']       = $car_type['car_config_cn'] ?? '';
            $query_car_info['car_series_code']     = $car_type['car_series_code'] ?? '';
            $query_car_info['large_car_type_code'] = $car_type['large_car_type_code'] ?? '';
            $query_car_info['brand']               = $car_type['car_brand_code'] ?? '';
            $query_car_info['car_config_code']     = $car_type['car_config_code'] ?? '';
            $query_car_info['car_config_cn']       = $car_type['car_config_cn'] ?? '';

            redis($redis_key, $query_car_info, mt_rand(1800, 3600));
//            $redis = \think\Cache::redisHandler();
//            $redis->sadd(config('cache_prefix.user_car_18n_set'), $redis_key);
        }
        return $query_car_info;

    }

    public function getReceiveChannel($channel)
    {
        switch ($channel) {
            case 'GWSM':
                $receive_channel = 1;
                $receive_source  = 'nissanminiapp'; // 日产小程序
                break;
            case 'GWAPP':
                $receive_channel = 2;
                $receive_source  = 'nissanapp'; // 日产APP
                break;
            case 'GWNET':
                $receive_channel = 3;
                $receive_source  = 'nissanwebsite'; // 日产官网
                break;
            case 'PZ1ASM':
                $receive_channel = 1;
                $receive_source  = 'dndc385plddfk8593N43'; // ariya小程序
                break;
            case 'PZ1AAPP':
                $receive_channel = 2;
                $receive_source  = 'ariyaapp'; // ariyaAPP
                break;
            case 'QCSM':
                $receive_channel = 1;
                $receive_source  = 'venuciaminiapp'; // 启辰小程序
                break;
            case 'QCAPP':
                $receive_channel = 2;
                $receive_source  = 'venuciaapp'; // 启辰APP
                break;
            default:
                $receive_channel = 1;
                $receive_source  = 'nissanminiapp'; // 日产小程序
                break;
        }
        return ['receive_channel' => $receive_channel, 'receive_source' => $receive_source];
    }

    /*public function quickWinCard($re_data, $card, $event_type = 1)
    {
        $receive_data    = $this->getReceiveChannel($re_data['dlr_code']);
        $receive_channel = $receive_data['receive_channel'];
        $receive_source  = $receive_data['receive_source'];
        if (empty($card['quick_win_card_id'])) return true;

        if ($event_type == 1) {
            $data = [
                "coupon_id"          => $card['quick_win_card_id'],
                "intention_brand_id" => DbDlr::channel_to_brand($re_data['dlr_code']),
                "one_id"             => $re_data['one_id'],
                "receive_channel"    => $receive_channel,
                "receive_source"     => $receive_source,
                "reserve_status"     => 0,
                "user_name"          => $re_data['name'],
                "user_phone"         => $re_data['phone'],
                "vin"                => $re_data['vin'],
            ];
            if ($card['consume_condition'] == 1) {
                $data['intention_store']       = $re_data['get_dlr_code'];
                $data['intention_car_type_id'] = $re_data['car_18n'];
                $redis_dlr_key                 = 'e3s_dlr_id_' . $re_data['get_dlr_code'];
                $redis_dlr_id                  = redis($redis_dlr_key);
                if (!empty($redis_dlr_id)) {
                    $data['intention_store_id'] = $redis_dlr_id;
                } else {
//                    $dlr_list                   = QuickWin::create('e3s_dlr')->postDlr(['dlrCode' => $re_data['get_dlr_code'], 'sortType' => '2'], $data['intention_brand_id']);
//                    $data['intention_store_id'] = $dlr_list['rows'][0]['dlrId'] ?? 0;
//                    redis($redis_dlr_key, $data['intention_store_id'], 3600);
                    $dlr_info                   = DbDlr::where(['dlr_code' => $re_data['get_dlr_code']])->find();
                    $data['intention_store_id'] = $dlr_info['base_dlr_id'];
                    redis($redis_dlr_key, $dlr_info['base_dlr_id'], 3600);
                }
                $redis_car_key  = 'e3s_car_id_' . $re_data['car_18n'];
                $redis_car_info = redis($redis_car_key);
                if (!empty($redis_car_info)) {
                    $car_info = $redis_car_info;
                } else {
                    $car_info = E3sCarSeries::where('car_config_code', $re_data['car_18n'])->find();
                    redis($redis_car_key, $car_info, 3600);
                }
                $data['intention_car_series_id'] = $car_info['base_series_code'];
                BuCardReceiveRecord::where('id', $data['id'])->update(['intention_dlr_id' => $data['intention_store_id']]); // 意向门店id填充进卡券领取表
            }
            $ret = QuickWin::create('quick_win')->postCouponReceive($data);
            if($ret['result'] == '1') return $this->re_msg($ret['rows']);
        } else {
            $coupon_receive = QuickWin::create('quick_win')->getCouponReceiveRecord(['coupon_code' => $re_data['coupon_code'], 'request_channel' => 1]);
            $data           = [
                [
                    "business_order_name"     => "",
                    "business_order_no"       => $re_data['consume_order_code'],
                    "coupon_code"             => $re_data['coupon_code'],
                    "coupon_discounted_price" => (int)($re_data['card_all_yh'] * 100),
                    "coupon_receive_id"       => $coupon_receive['rows'][0]['id'],
                    "creator"                 => "",
                    "order_source"            => "",
                    "used_store"              => $re_data['get_dlr_code'],
                    "used_store_id"           => $re_data['intention_dlr_id'],
                ]
            ];
            $ret            = QuickWin::create('quick_win')->postCouponConsume($data);
            if($ret['result'] == '1') return $this->re_msg('');
        }
        DbCardLog::create(
            [
                'user_id'       => $re_data['user_id'],
                'event_type'    => $event_type,
                'is_success'    => $ret['result'] == '1' ? 1 : 0,
                'card_id'       => $card['id'],
                'request_info'  => json_encode($data),
                'response_info' => json_encode($ret),
            ]
        );

        return $this->re_msg('', 400);
    }*/


    //订单商品拆成组合，在订单确认，订单详情使用
    public function order_goods_spilt($goods){
        $dlr_model =  new DbDlr();
        $new_order_goods = [];
        foreach ($goods as $k=>$v) {
            //0423版本
//            $dd_dlr_code = $v['dd_dlr_code'] ?? '';
//            if(empty($dd_dlr_code)) continue;
//            $v['order_mail_type'] = $v['order_mail_type'] ?? 0;
//            $v['price'] = formatNumber($v['price']);
//            $v['mail_price'] = formatNumber($v['mail_price']);
//            $v['mail_price'] = formatNumber($v['mail_price']);
            if($v['order_mail_type']==1){
                if($v['dd_dlr_code']){
                    $dd_dlr_info =  $dlr_model->getOne(['where'=>['dlr_code'=>$v['dd_dlr_code']],'field'=>'dlr_name,is_enable']);
                    $new_order_goods[$v['tip_dd_dlr_code']]['dlr_name'] =$dd_dlr_info['dlr_name'];
                }else{
                    $new_order_goods[$v['tip_dd_dlr_code']]['dlr_name'] ='';
                    $new_order_goods[$v['tip_dd_dlr_code']]['un_dlr_code'] = 1;

                }
//                if($dd_dlr_info['is_enable']!=1){
//                    $dd_dlr_info['dlr_name'] = '';
//                    $v['tip_dd_dlr_code'] = '';
//                }

                $new_order_goods[$v['tip_dd_dlr_code']]['order_mail_type'] =1;

            }else{
                $new_order_goods[$v['dd_dlr_code']]['dlr_name'] = DbDlr::brand_dlr_name($v['brand']);
                $new_order_goods[$v['dd_dlr_code']]['order_mail_type'] =2;
            }
            //保养套餐类型有这个值，不能选店
            if(isset($v['cannt_c_dlr']) && $v['cannt_c_dlr']==1){
                $new_order_goods[$v['tip_dd_dlr_code']]['cannt_c_dlr']=1;
            }
            if(isset($new_order_goods[$v['tip_dd_dlr_code']]['un_dlr_code'])){
                $v['dd_dlr_code'] = '';
                $new_order_goods[$v['tip_dd_dlr_code']]['dlr_name'] = '';
            }
            $new_order_goods[$v['tip_dd_dlr_code']]['dlr_code'] =  $v['dd_dlr_code'];
            $new_order_goods[$v['tip_dd_dlr_code']]['tip_dd_dlr_code'] =  $v['tip_dd_dlr_code'];
            $new_order_goods[$v['tip_dd_dlr_code']]['list'][]         = $v;


        }
        krsort($new_order_goods);
        return array_values($new_order_goods);
    }

    //pz多订单拆分支付积分
    public function pz_more_point($more_point,$sp_order,$order_code){
        //有拆单时候需要将原来的订单给is_enable=0然后再做新的拆单
        $more_card_point_model = new BuOrderMoreCardPoint();
        $one_more_data = [];
        $more_card_point_model->saveData(['is_enable'=>0] , ['order_code'=>$order_code]);
        if($more_point){
            $i=0;
            $sy=0;
            foreach ($sp_order as $v){
                $more_card_point_model->saveData(['is_enable'=>0] , ['order_code'=>$v['order_code']]);
//                while ($sy<=0){
//                    $sy += $more_point[$i]['point'];
//                    if($v['integral']<=$sy){
//                        $sub_new_point = $v['integral'];
//                        $sy -=$sub_new_point;
//                    }
//                }
                if($v['integral']>0){
                    if($sy==0){
                        $sy += $more_point[$i]['point'];
                    }
//                    echo $sy.'.='.$i.'=.'.$v['integral'].';';
                    if($v['integral']<=$sy){
//                        echo "11-".$sy.'.='.$i.'=.'.$v['integral'].';';
                        $sub_new_point = $v['integral'];
                        $sy -=$sub_new_point;
                        $one_point_data=[
                            'order_code'=>$v['order_code'],
                            'point_order_code'=>$v['order_code'].$v['logistics_mode'],
                            'vin'=>$more_point[$i]['vin'],
                            'ic_card_no'=>$more_point[$i]['ic_card_no'],
                            'all_point'=>$v['integral'],
                            'point'=>$sub_new_point,
                            'creator'=>'nowhilepoint',
                            'point_type'=>$more_point[$i]['point_type'],
                        ];
                        $one_more_data[]=$one_point_data;
                    }else{
                        while ($v['integral']>=$sy){
                            $sub_new_point = $v['integral'];
                            $b_it=0;
//                                echo "2-".$sy.'.='.$i.'=.'.$v['integral'].$v['order_code'].';';
                            if($sy>=$sub_new_point){
                                $one_point =$more_point[$i]['point'] -($sy-$sub_new_point) ;
                                $b_it=1;
                                if(isset($more_point[$i])){
                                    $sy -=$sub_new_point;
                                }
                            }else{
                                try {
                                    $one_point = $sy;
                                }catch (Exception $e){
                                    Logger::error('kjferror'.$v['integral'].'=='.$sy.$v['order_code'].';');
//                                    echo $v['integral'].'=='.$sy.$v['order_code'].';';
//                                    die();
                                }
                            }
//                            echo "3-".$sy.'.='.$i.'=.'.$v['integral'].'[one]'.$one_point.$v['order_code'].';<br/>';
                            $one_point_data=[
                                'order_code'=>$v['order_code'],
                                'point_order_code'=>$v['order_code'].$v['logistics_mode'],
                                'vin'=>$more_point[$i]['vin'],
                                'ic_card_no'=>$more_point[$i]['ic_card_no'],
                                'all_point'=>$v['integral'],
                                'point'=>$one_point,
                                'creator'=>'whilepoint',
                                'point_type'=>$more_point[$i]['point_type'],
                            ];
                            $one_more_data[]=$one_point_data;
                            $i++;
                            if(isset($more_point[$i])){
                                $sy += $more_point[$i]['point'];
                            }
                            if($b_it==1){
                                break;
                            }
                        }
//                        try {
//
//                        }catch (Exception $e){
//                            echo $i;
//                        }
                    }
                }
            }
//            echo json_encode_cn($one_more_data);
//            die();
            $more_card_point_model->insertAll($one_more_data);

        }
    }

    //获取随心配价格--通过set_sku_id;
    public function get_suit_self_info($sku_id)
    {
        if (!$sku_id) {
            return false;
        }
        $ac_suit_model = new AcSuitYourself();
        $suit_self = $ac_suit_model->getOne(['where' => ['set_sku_id' => $sku_id, 'is_enable' => 1]]);
        if ($suit_self) {
            $actual_price = $suit_self['discount_price'];
            return $actual_price;
        } else {
            return false;
        }
    }
    //异常vin日志
    public function error_vin_goods($vin,$sku,$action=''){
        $log_model  =  new DbLog();
        $log_data = array(
            'type'         => 'err_vin',
            'is_success'   => 'error'.$action,
            'send_note'    => json_encode_cn(['vin' => $vin, 'sku' => $sku]),
            'receive_note' => '',
        );
        $log_model->insertData($log_data);

    }
    //清除vin日志，，一个月
    public function cl_err_vin_goods(){
        $log_model  =  new DbLog();
        $where = ['type'=> 'err_vin','last_updated_date'=>['<',date('Y-m-d H:i:s',strtotime('-1 month'))],'is_enable'=>1];
        $res = $log_model->where($where)->delete();
//        dd($log_model->getLastSql());
        Logger::error('clerrvingoods',['sql'=>$log_model->getLastSql(),'r'=>$res]);
        return $res;

    }
    //判断秒杀商品库存
    public function kill_count($kill_id,$goods_id,$screening=''){
        $redis_name = 'acKillCountById'.$kill_id.$goods_id; //
        if ($screening != '') {
            $redis_name = $redis_name.':'.$screening; // 场次
        }
        $redis =  new Redis(config('cache'));
        $count =  $redis->get($redis_name);
        $arr_count = explode(':',$count);
//        $count =  redis($redis_name);
        if(($count===0  || $count)&& count($arr_count)==1){
            if($count=='-1'){
                $count=0;
            }
            return $count;
        }else{
            $kill_goods_model =  new DbSeckillCommodity();
            $kill_info =  $kill_goods_model->getOne(['where' => ['seckill_id'=>$kill_id,'commodity_id'=>$goods_id],'field' =>'sku_stock']);
            $seckill_model = new DbSeckill();
            $field = 'id,start_time,end_time,seckill_type';
            $seckill_info = $seckill_model->where('id', $kill_id)->field($field)->find();
            if($kill_info){
                if ($seckill_info['seckill_type'] == 2) {
                    $day=floor((strtotime($seckill_info['end_time'])-strtotime($seckill_info['start_time']))/86400);
                    for ($i=0; $i<=$day; $i++) {
                        $date = date('Y-m-d',strtotime("+".$i." day", strtotime($seckill_info['start_time'])));
                        $redis_name = 'acKillCountById'.$kill_id.$goods_id.':'.$date;
                        $count = $kill_info['sku_stock'];
                        $redis->set($redis_name,$count,3600*24*100);//缓存100天
                    }
                    return $count;
                } else {
                    $count = $kill_info['sku_stock'];
                    $redis->set($redis_name,$count,3600*24*100);//缓存100天
//                  redis($redis_name,$count,3600*24*100);//缓存100天
                    return $count;
                }
            }else{
                return 0;
            }

        }
    }


    /**
     * 删除秒杀库存
     * @param $kill_id
     * @param $goods_id
     * @param $seckill_type 秒杀类型 1:单场秒杀 2:重复秒杀
     * @param string $start_time 开始时间
     * @param string $end_time 结束时间
     */
    public function del_kill_count($kill_id, $goods_id, $seckill_type, $start_time='', $end_time='')
    {
        $redis =  new Redis(config('cache'));
        $redis_name = 'acKillCountById'.$kill_id.$goods_id; //
        // 单场秒杀
        if ($seckill_type == 1) {
            $redis->rm($redis_name);
        } else {
            // 重复秒杀
            $day=floor((strtotime($end_time)-strtotime($start_time))/86400);
            for($i=0; $i<=$day; $i++) {
                $date = date('Y-m-d',strtotime("+".$i." day", strtotime($start_time)));
                $redis_name = 'acKillCountById'.$kill_id.$goods_id.':'.$date;
                $redis->rm($redis_name);
            }
        }

    }

    //扣秒杀商品库存;
    public function dec_kill_count($kill_id,$goods_id,$step=1,$order_code='',$screening=''){
        $redis_name = 'acKillCountById'.$kill_id.$goods_id; //
        if ($screening != '') {
            $redis_name = $redis_name.':'.$screening; // 场次
        }
        $redis_name_order = $redis_name.$order_code; //
        $redis =  new Redis(config('cache'));
        $count =  $redis->get($redis_name);
        if(empty($count)){
            return false;
        }
        if($count<=0){
            return false;
        }

        $res =  $redis->dec($redis_name,$step);
        if($res<0){
            return false;
        }
        $redis->set($redis_name_order,1,3600*24*100);
        return 1;//成功扣除就1
    }
    //返回秒杀商品库存;
    public function inc_kill_count($kill_id,$goods_id,$step=1,$order_code='',$screening=''){
        $redis_name = 'acKillCountById'.$kill_id.$goods_id; //
        if ($screening != '') {
            $redis_name = $redis_name.':'.$screening; // 场次
        }
        $redis_name_order = $redis_name.$order_code; //
        $redis =  new Redis(config('cache'));
        $redis_order =  $redis->get($redis_name_order);
//        dd($redis_name_order);
        if(!$redis_order){
            return false;
        }
        $res =$redis->inc($redis_name,$step);
//        $count =  redis($redis_name);
//        if($count=='-1'){
//            $count=0;
//        }
//        $re_count = bcadd($count,$step);
//        redis($redis_name,$re_count,3600*24*100);
        $redis->set($redis_name_order,0);
        return $res;
    }

    /**
     * 根据vin码获取车龄和保养信息--移到这里来
     * @param string $vin
     * @return mixed
     */
    public function _get_maintain($url, $vin = '')
    {
        if(config('app_status')=='develop' && date('Y-m-d')<='2024-09-15'){
//            return '';
        }
//        $params = ["body" => [["VIN" => $vin]]];//@樊苏喜 改了,2024-09-25 16:07:12
        $params = ["vin" => $vin];//@樊苏喜 改了,2024-09-25 16:07:12
        return E3spRefactor::create('e3sp_refactor')->getMaintainInfo($params);



        $header = array(
            'Content-Type: application/json',
        );
        $ch     = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 3);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($params));
        $data = curl_exec($ch);
        $time = curl_getinfo($ch, CURLINFO_TOTAL_TIME);
        curl_close($ch);
        trace(['url' => $url, 'param' => $params, 'time' => $time], 'debug');
        return $data;
    }

    //获取车龄--商品版整合车龄+公里数，返回折扣
    public function vehicleAge2($vin,$kilometer)
    {
        $type = '8.0';
        $can_buy=1;
        //20230818  || !$kilometer 去掉这个公里数，即使0也正常往下走 但是前端加购跟下单要提示
        if(!$vin){
            $can_buy = 0;
            $data =['can_buy'=>$can_buy,'type'=>$type,'can_buy_word'=>'车辆数据获取失败无法购买，请联系客服处理'];
            return  $data;
        }

        // 添加基于VIN的随机失败逻辑：约1%的VIN会返回失败
        // 取VIN的最后两个字符转为数字，如果是1则返回失败
//        $last_two_chars = substr($vin, -2);
//        $hash_value = crc32($last_two_chars) % 100;
//        if($hash_value === 1) {
//            $can_buy = 0;
//            $data =['can_buy'=>$can_buy,'type'=>$type,'can_buy_word'=>'请求失败，请稍后重试'];
//            return $data;
//        }
        $redis_name =  'getVinvehicleAge2'.$vin.$kilometer;
        $car_age =  redis($redis_name);
        $car_age = [];
        if(!$car_age){

            $url = config('friend.vehicle_age').'/postdata/DNDC_ONLINESHOP/DNDC_GET_CAR_AGE';
//            $car_age = json_decode($this->_get_maintain($url, $vin), true);
//            $params = ["body" => [["VIN" => $vin]]];//@樊苏喜 改了,2024-09-25 16:07:12
            $params = ["vin" => $vin];//@樊苏喜 改了,2024-09-25 16:07:12
            $car_age = E3spRefactor::create('e3sp_refactor')->getCarAge($params);
//            $car_age = $this->_get_maintain($url, $vin);
            $is_success = 'fail';
            if(!empty($car_age)){
                $is_success = 'success';
            }
            $redis_log_name = $redis_name.'_log';
            $redis_log =  redis($redis_log_name);
            if(!$redis_log){
                $log = new DbLog();
                $log->insertData(
                    [
                        'type' => 'package',
                        'is_success'  => $is_success,
                        'send_note' =>  json_encode(["vin" => $vin,'k'=>$kilometer]),
                        'receive_note' => $car_age
                    ]
                );

                redis($redis_log_name,1,360);
            }

            if(!$car_age){
                $can_buy = 0;
                $data =['can_buy'=>$can_buy,'type'=>$type,'can_buy_word'=>'车辆数据获取失败无法购买，请联系客服处理!'];
                return  $data;
            }
            redis($redis_name,$car_age,300);
        }
        $car_age = json_decode($car_age,true);
        $me_car_age = 0;
        if (isset($car_age['result']) && $car_age['result'] == 1) {
            $me_car_age = $car_age['rows']['carAge'] ?? 0;
        }
        $cant_buy_word='';
        if($me_car_age==0){
            $can_buy = 0;
            $cant_buy_word = '车辆数据获取失败无法购买，请联系客服处理';
        }
        $age_info = $me_car_age;
        //8折 车龄小于等于三年且里程小于等于10万公里
        if($age_info <=3 && $kilometer <= 100000){
            $type = '8.0';
        }

        //7.5折（车龄大于三年或里程大于10万公里）且（车龄小于等于五年且里程小于等于15万公里）
        if(($age_info > 3 || $kilometer > 100000 ) && ($age_info <= 5 && $kilometer <= 150000)){
            $type = '7.5';
        }
        //7折车龄大于五年或里程大于15万公里
        if($age_info > 5 || $kilometer > 150000){
            $type = '7.0';
        }
        $data =['can_buy'=>$can_buy,'type'=>$type,'can_buy_word'=>$cant_buy_word];
        return  $data;
    }

    public function yb_sku_code($vin,$kilometer)
    {
        $params = ['vin'=>$vin,'runningMile'=>$kilometer];
        $res =  E3spRefactor::create('e3sp_refactor')->getExtendPro($params);
        
        // 新增：develop环境写日志
        if (config('app_status') == 'develop') {
            $log = new DbLog();
            $log->insertData([
                'type' => 'yb_sku_code',
                'is_success' => $vin,
                'send_note' => json_encode($params, JSON_UNESCAPED_UNICODE),
                'receive_note' => json_encode($res, JSON_UNESCAPED_UNICODE),
            ]);
        }
        if(isset($res['rows'])){
            return array_column($res['rows'],'guaranteeProductId');
//            return $res['rows'];
        }else{
            return [];
        }
    }

    //获取五年双保信息--商品版
    public function maintion_info($vin,$pagesb='packagesb'){
        $upgrade_type = 0;
        $times =3;
        $num=0;
        $can_buy=1;
        $is_sb = 0;
        $can_buy_word = '无升级次数!';
        if(!$vin){
            $data =['can_buy'=>0,'upgrade_type'=>$upgrade_type,'times'=>$times,'can_buy_word'=>$can_buy_word,'num'=>$num,'is_sb'=>$is_sb,'left_time'=>0];
            return  $data;
        }
        $redis_name =  'getVinMaintionInfo'.$vin;
//        $car_package =  redis($redis_name);
        $car_package = false;
        if(!$car_package){
            $url = config('friend.vehicle_age').'/postdata/DNDC_ONLINESHOP/DNDC_GET_MAINTAIN_INFO';
//            $car_package = json_decode($this->_get_maintain($url, $vin), true);
            $car_package = $this->_get_maintain($url, $vin);
            $is_success = 'fail';
            if(!empty($car_package)){
                $is_success = 'success';
            }
            $redis_log_name = $redis_name.'_log';
//            $redis_log =  redis($redis_log_name);
            $redis_log = false;
            if(!$redis_log){
                $log = new DbLog();
                $log->insertData(
                    [
                        'type' => $pagesb,
                        'is_success'  => $is_success,
                        'send_note' =>  $vin,
                        'receive_note' => $car_package
                    ]
                );
                redis($redis_log_name,1,rand(50,120));
            }

//            redis($redis_name,$car_package,300);
        }
        $car_package = json_decode($car_package,true);
        if (!isset($car_package['result']) || (empty($car_package) || $car_package['result'] != 1 || empty($car_package['rows']))) {
            $can_buy=0;
            $num=0;
            $data =['can_buy'=>$can_buy,'upgrade_type'=>$upgrade_type,'times'=>$times,'can_buy_word'=>$can_buy_word.'~','num'=>$num,'is_sb'=>$is_sb,'left_time'=>0];
            return  $data;
        }
        $list                             = $car_package['rows'][0];
        $is_sb = 1;//有数据就是双保用户
        if($list['oilGroupType']=='B' && $pagesb=='packagesb'){
            $can_buy=0;
            $num=0;
            $data =['can_buy'=>$can_buy,'upgrade_type'=>$upgrade_type,'times'=>$times,'can_buy_word'=>$can_buy_word.'!~','num'=>$num,'is_sb'=>$is_sb,'left_time'=>$list['maintainLeftTimes']];
            return  $data;
        }
//        $num = $list['maintainLeftTimes'] - $list['upgradeMaintainCount'];
        if(isset($list['leftTimes'])){
            $num = $list['leftTimes'] - $list['upgradeMaintainCount'];//产品说新增了 leftTimes 双保就是 leftTimes-upgradeMaintainCount

        }else{
            $num = $list['maintainLeftTimes'] - $list['upgradeMaintainCount'];

        }
        $upgrade_array['frequency']       = $num . '次';
        $upgrade_array['remaining_times'] = $list['maintainLeftTimes'] . '次';
        $can_buy_word = '';
        if($num<3){
            $can_buy=1;
            $times=$num;
            $can_buy_word='无升级次数';
        }
        if ($num < 6 && $num >= 3) {
            $times =3;
        }
        if ($num >= 6) {
            $times=6;
        }
        if ($num >= 8) {
            $times=8;
        }
        if ($num >= 9) {
            $times=9;
        }
        if ($list['maintainOilType'] == 'ASOIL') {
            $upgrade_type = 1;
        }
        if ($list['maintainOilType'] == 'NOIL') {
            $upgrade_type = 0;
        }
        //'left_time'=>$list['maintainLeftTimes'] //改成leftTimes
        $data =['can_buy'=>$can_buy,'upgrade_type'=>$upgrade_type,'times'=>$times,'can_buy_word'=>$can_buy_word,'num'=>$num,'is_sb'=>$is_sb,'left_time'=>$list['maintainLeftTimes']];
        return  $data;
    }

    // 部分退积分需求-扣除积分
    public function usePointByRefund($order, $afs, $point_order)
    {
        $car         = new Carer();
        $point_model = new BuOrderPoint();
        $order_model = new BuOrder();
        $log_model   = new DbLog();

        $return_point     = 0;
        $new_return_point = 0;
        $use_points       = $order['integral'] - $afs['refund_points'];

        if (in_array($order['dlr_code'], DbDlr::$pz1a_arr)) { // ariya分电车卡和油车卡
            $js_arr = json_decode($point_order['send_note'], true);
            if (!empty($js_arr['use_elec_points'])) { // 电车卡支付
                $js_arr['use_elec_points'] = $use_points;
                $return_point              += $js_arr['use_elec_points'];
            } else {
                $js_arr['oli_card_list'][0]['usePvPoint'] = $use_points;
                $new_return_point                         += array_sum(array_column($js_arr['oli_card_list'], 'usePvPoint'));
            }

            if (isset($js_arr['use_elec_points'])) {
                $return_point += $js_arr['use_elec_points'];
            }
            if (isset($js_arr['oli_card_list'])) {
                $return_point += array_sum(array_column($js_arr['oli_card_list'], 'usePvPoint'));
            }

            $modifier       = 'ha-re-pza';
            $point_order_id = $js_arr['order_id'] . '1';
            $request_id     = $this->_getOrderNo($point_order['order_code'], 6);

            $js_arr['order_id']   = $point_order_id;
            $js_arr['request_id'] = $request_id;

            $point_res = $car->usePzPoint($js_arr);
            $log_data  = array(
                'type'         => 'use_point',
                'send_note'    => json_encode_cn($js_arr),
                'receive_note' => json_encode_cn($point_res),
            );

            $point_data = [
                'order_code'       => $point_order['order_code'],
                'point_order_code' => $point_order_id,
                'vin'              => '',
                'ic_card_no'       => '',
                'dlr_code'         => $js_arr['dlr_code'],
                'creator'          => $modifier,
                'point_type'       => 3,
                'all_point'        => $return_point,
                'point'            => $new_return_point,
                'use_state'        => 2,
                'remark'           => $point_order['remark'],
                'send_note'        => json_encode_cn($js_arr),
                'back_json'        => json_encode_cn($point_res),
                'one_id'           => $point_order['one_id'],
                'request_id'       => $request_id,
            ];
            $point_model->insertData($point_data);
            //事件没有通知之前都是1
            $res = $order_model->saveData(['is_cc_ok' => 1, 'last_updated_date' => date('Y-m-d H:i:s')], ['order_code' => $point_order['order_code']]);
            $log_model->insertData($log_data);
        } else {
            $is_cc_ok = 0;
            $data     = array(
                'vin'        => $point_order['vin'],
                'ic_card_no' => $point_order['ic_card_no'],
                'order_id'   => $point_order['point_order_code'] . '1',
                'dlr_code'   => $point_order['dlr_code'],
                'deal_man'   => $point_order['dlr_code'],//处理人
                'point'      => $use_points,
                'dlr_point'  => 0,
                'remark'     => $point_order['remark'],
                'brand_code' => $order['brand'],//品牌
            );

            $point_res = $car->deductPoint($data);
            $log_data  = array(
                'type'         => 'integral',
                'send_note'    => json_encode_cn($data),
                'receive_note' => json_encode_cn($point_res),
            );
            if ($point_res == 'ok') {
                $log_data['is_success'] = 'success';
            } else {
                $log_data['is_success'] = 'fail';
                $is_cc_ok               = 1;
            }
            $point_data = [
                'order_code'       => $point_order['order_code'],
                'point_order_code' => $data['order_id'],
                'vin'              => $data['vin'],
                'ic_card_no'       => $data['ic_card_no'],
                'dlr_code'         => $data['dlr_code'],
                'creator'          => '部分退积分',
                'point_type'       => $point_order['point_type'],
                'all_point'        => $point_order['all_point'],
                'point'            => $data['point'],
                'use_state'        => $is_cc_ok + 1,
                'remark'           => $data['remark'],
                'send_note'        => json_encode($data),
                'back_json'        => json_encode_cn($point_res),
            ];
            $point_model->insertData($point_data);
            if ($point_data['use_state'] == 2) {
                $order_model = new BuOrder();
                $res         = $order_model->saveData(['is_cc_ok' => 1], ['order_code' => $point_data['order_code']]);
                Logger::error('net_order_usepoint', ['r' => $res, 'sql' => $order_model->getLastSql()]);
            }
            $point_model->saveData(['is_enable' => 0], ['point_order_code' => $point_order['point_order_code']]);//把原来的弄成不可用
            $res = $log_model->insertData($log_data);
        }
        return (bool)$res;
    }



    // 23-38节活动判断用户0.1购买商品资格
    public function isHaveDrawCap($commodity_id, $channel_type = '')
    {
        if (empty($channel_type) || $channel_type != 'GWAPP') {
            return false;
        }
        if (
            date('Y-m-d H:i:s') >= config('activity_cap.start_date') &&
            date('Y-m-d H:i:s') <= config('activity_cap.end_date') &&
            in_array($commodity_id, config('activity_cap.commodity_ids'))
        ) {
            $check_draw = Website::create('')->userPrizeCheck(['oneid' => session("net_api-oneid"), 'prize_str_id' => $commodity_id]);
            if (isset($check_draw['code']) && $check_draw['code'] == 1 && $check_draw['data']['prize_has'] == 1) {
                return true;
            }
        }
        return false;
    }
    /**
     * 前置查询用户套餐类权益--
     * @param $user
     * @param $lng
     * @param $lat
     * @param $kilometer
     * @param $dd_dlr_code
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function tc_zg($user,$lng,$lat,$kilometer,$dd_dlr_code=''){
        $dlr_level = 'A';
        $user_info_redis_name = 'userinfobyid' . $user['id']  . $lng . $lat. $kilometer.$dd_dlr_code . $user['vin'];
        $data_json = redis($user_info_redis_name);
        $dlr_name = '';
        if(!$data_json){
            $user_info = $this->getFriendBaseInfo($user, $lng, $lat);
            if (empty($dd_dlr_code) || $dd_dlr_code == 'V0000') {
                $dd_dlr_code = $user_info['dlr_code'];
            }
            $user_vin = $user_info['vin'];
            if (!empty($dd_dlr_code) && $dd_dlr_code != 'V0000') {
                $dlrObj = new DbDlr();
                $dlr_info = $dlrObj->alias("a")->join("t_db_area b", "a.area_id=b.area_id")->where(['a.dlr_code' => $dd_dlr_code, 'a.is_enable' => 1])->field("b.city_type,a.dlr_name,b.brand_city_type,a.dlr_code")->find();
                $dlr_name  = $dlr_info['dlr_name'];
                if (in_array($this->channel_type, ['QCSM', 'QCAPP'])) {
                    $dlr_level = $dlr_info['brand_city_type'];
                } else {
                    $dlr_level = $dlr_info['city_type'];
                }
            }
            $maintain_type = '999';
            $maintain_upgrade_type = 0;
            $maintain_times = 0;
            $xy_can_buy = 0;
            $wn_can_buy = 0;
            $yb_can_sku_code= '';
            $is_sb = 0;
            if ($user_vin) {
                $maintain_type_info = $this->vehicleAge2($user_vin, $kilometer);
                $maintain_type = $maintain_type_info['type'];// 最高8折默认
                $xy_can_buy = $maintain_type_info['can_buy'];
                $maintain_type_info_b = $this->maintion_info($user_vin);
                // $data =['can_buy'=>0,'up grade_type'=>$upgrade_type,'times'=>$times,'can_buy_word'=>'无升级次数'];
                $wn_can_buy = $maintain_type_info_b['can_buy'];

                $maintain_upgrade_type = $maintain_type_info_b['upgrade_type'];
                $maintain_times = $maintain_type_info_b['num'];
                $is_sb = $maintain_type_info_b['is_sb'];

                $yb_can_info = $this->yb_sku_code($user_vin,$kilometer);
                if($yb_can_info){
                    $yb_can_sku_code = $yb_can_info;
                }
            }
            $data = ['maintain_type'=>$maintain_type,'maintain_upgrade_type'=>$maintain_upgrade_type,'maintain_times'=>$maintain_times,'dlr_level'=>$dlr_level,'xy_can_buy'=>$xy_can_buy,'wn_can_buy'=>$wn_can_buy,'user_info'=>$user_info,'dlr_code'=>$dd_dlr_code,'dlr_name'=>$dlr_name,'is_sb'=>$is_sb,'yb_can_sku_code'=>$yb_can_sku_code];
            $data_json = json_encode($data);
            redis($user_info_redis_name, $data_json, 600);
        }
        return json_decode($data_json,true);
    }

    /**
     * 最低现金加积分，传入价格+commodity_id
     * @param $params
     * @return array
     * @return least_money
     * @return point
     *
     */
    public function goods_point_js($params=['commodity_id'=>'','price'=>0,'channel_type'=>'']){
        $point_times = 10;
        //先查商品，再查分类，都没有返回原价+0积分
        //订单数据库记录最少现金
        $goods_pay_model =  new DbCommodityPay();
        $goods_pay_com_model =  new DbCommodityPayCommodity();
        $flat_model = new DbCommodityFlat();
        $flat_where = ['commodity_id'=>$params['commodity_id'],'is_enable'=>1];
        $flat_where[] = ['exp', " (find_in_set('{$params['channel_type']}',up_down_channel_dlr)) "];
        $flat_info =  $flat_model->getOne(['where'=>$flat_where]);
        $res = ['least_money'=>0,'point'=>floor(bcmul($params['price'],$point_times,5)),'pay_com'=>array()];
        if(!$flat_info){
            return $res;
        }
        if($flat_info['pay_style']==3){
            return $res;//纯积分
        }
        if($flat_info['pay_style']==2){
            $res = ['least_money'=>$params['price'],'point'=>0,'pay_com'=>array()];//现金支付时候不显示积分
            return $res;
        }

        $where = ['b.comm_id'=>$params['commodity_id'],'a.is_enable'=>1,'b.is_enable'=>1];
        $where[] = ['exp', " (find_in_set('{$params['channel_type']}',a.channel_type)) "];
        $field = "a.id,b.comm_id,b.comm_type_id,a.pay_rule,a.pay_price,a.channel_type";//comm_id
        //pay_rule 支付规则 1 至少支付N元 2按%比  pay_price金额或者百分比
        $goods_pay_com = $goods_pay_model->getPayComInfo(['where'=>$where,'field'=>$field]);//先查商品在不在
        if(!$goods_pay_com){
            unset($where['b.comm_id']);
            $where['b.comm_type_id'] = $flat_info['comm_type_id'];
            $goods_pay_com = $goods_pay_model->getPayComInfo(['where'=>$where,'field'=>$field]);
        }
        if(!$goods_pay_com){
            return $res;
        }
        $least_money = $params['price'];
        if($goods_pay_com['pay_rule']==1){
            $least_money = min($goods_pay_com['pay_price'],$params['price']);
        }
        if($goods_pay_com['pay_rule']==2){
            $least_money = bcmul($params['price'],bcdiv($goods_pay_com['pay_price'],100,5),5);
        }
        $least_money = round($least_money,1);
        $res = ['least_money'=>$least_money,'point'=>floor(bcmul(bcsub($params['price'],$least_money,5),$point_times,5)),'pay_com'=>$goods_pay_com];
        return $res;



    }

    // 判断用户可领的券
    public function canGetCards($user, $card_ids, $channel_type){
        if (empty($user) || empty($card_ids)) return [];
        $card_list = (new DbCard())->getList(['where' => ['id' => ['in', $card_ids]]]);
        $user_crowds = (new WlzCrowdsLogs())->where(['member_id' => $user['plat_id']])->column('crowd_id');

//        $r = Crm::create('crm')->queryMemberInfo(['unionid' => $user['unionid']]);
        $r = $this->_getCarer($user['bind_unionid'], $user['plat_id'], $user['one_id'], $channel_type);
//        $r = $this->getCarOwner($user);

        $user_level = $r['card_degree_code'] ?? '';
        $can_card_ids = [];
//        print_json($card_list);
        foreach ($card_list as $v){
            if ($v['can_user_type'] == 0 || empty($v['can_user_value'])) {
                $can_card_ids[] = $v['id'];continue;
            }
            $tmp = explode(',', $v['can_user_value']);
            if ($v['can_user_type'] == 1) {
                foreach ($user_crowds as $val){
                    if (in_array($val, $tmp)){
                        $can_card_ids[] = $v['id'];
                        break;
                    }
                }
            }elseif ($v['can_user_type'] == 2){
                if (in_array($user_level, $tmp)){
                    $can_card_ids[] = $v['id'];
                }
            }
        }
        return $can_card_ids;
    }
    //判断最优卡券中商品对应优惠.
    public function card_goods_yh($card_arr){
        if($card_arr){
            $goods_card_yh_res = [];
            $goods_card_yh = [];
            foreach ($card_arr as $v){
                if(!isset($v['goods_res'])){
                    Logger::error('card-no-yh-',json_encode($v));
                    continue;
                }
                foreach ($v['goods_res'] as $vv){
                    if($vv['count']>0){
//                        $goods_card_yh[$vv['cid']][]=bcdiv($vv['yh_money'],$vv['count'],1);
                        $goods_card_yh[$vv['cid']][]=$vv['yh_money'];//改成总的，然后在外面计算的时候也不去除数量了。
                    }
                }
            }
            if($goods_card_yh){
                foreach ($goods_card_yh as $k=>  $c_y_v){
                    $goods_card_yh_res[$k] =array_sum($c_y_v);
                }
            }
            return $goods_card_yh_res;
        }
        return  false;
    }

    public function check_staff($data){
        $EMPLOYEE_NUMBER = $data['emp_no'];
        $MOBILE_NUM = $data['phone'];
//        $url =  config('staff_url');
        $EMPLOYEE_NUMBER = $data['emp_no'];
        $MOBILE_NUM = $data['phone'];
        $p_data = ['EMPLOYEE_NUMBER'=>$EMPLOYEE_NUMBER,'MOBILE_NUM'=>$MOBILE_NUM];
        if(config('app_status')=='develop'){
            $url = 'http://172.26.137.108:8089/WebServiceReconmandBuyCar.asmx/SingInCheck';
        }else{
            $url = 'http://172.25.24.46:8090/WebServiceReconmandBuyCar.asmx/SingInCheck';
        }
//        $url = config('app_status') == 'product' ? 'http://172.25.24.46:8090/WebServiceReconmandBuyCar.asmx/SingInCheck' : 'http://172.26.137.108:8089/WebServiceReconmandBuyCar.asmx/SingInCheck';
//        $result = http_get($url, compact('EMPLOYEE_NUMBER', 'MOBILE_NUM'),false);
        $result = http_get($url, $p_data,false);
        if (empty($result)) return [];
        $str = (string)simplexml_load_string($result);
        $arr = json_decode($str, true);
        return $arr;
    }

    public function addDrawNumOperate($input, $user_id)
    {
        $member_id = (new DbUser())->where(['id' => $user_id])->value('plat_id');

        $data = ['url' => '', 'draw_num' => 0, 'msg' => ''];

        $map = [
            'is_enable'     => 1,
            'set_type'      => $input['set_type'],
            'act_status'    => 2,
            'get_draw_type' => $input['draw_type']
        ];
        if ($input['draw_type'] == 4) {

            $map['get_draw_type'] = ['in', [4, 5]];
            $commodityId          = BuOrderCommodity::where('order_code', $input['order_code'])->value('commodity_id');
            // 查询该订单是否已增加次数
            $where = ['order_code' => $input['order_code'], 'draw_type' => 1, 'is_enable' => 1];
            $re    = DbUserDrawRecord::where($where)->find();
            if (!empty($re)) {
                $draw  = DbDraw::where(['id'=>$re['draw_id']])->find();
                $data['url'] =$draw['draw_url'];
                $re2 = HaoWan::create('hao_wan')->getLotteryNum($member_id, $draw['game_id']);
                if (!$re2->isSuccess()) {
                    return $this->setResponseError($re2->getMessage())->send();
                } else {
                    $draw_num = $re2->getData()['times'];
                }
                $data['draw_num'] = $draw_num;
                return $this->setResponseData($data)->send();
            }
        } else {
            if (empty($input['commodity_id'])) {
                return $this->setResponseError('commodity_id不能为空')->send();
            }
            $commodityId = $input['commodity_id'];
        }

        $map[] = ['exp', "FIND_IN_SET('{$commodityId}', commodity_ids)"];
        $draw  = DbDraw::where($map)->find();
        if (empty($draw)) {
            $data['msg'] = '该行为活动不存在';
            return $this->setResponseData($data)->send();
        }

        // 判断金额
        if ($draw['get_draw_type'] == 5) {
            // 查询订单金额
            $order = BuOrder::where('order_code', $input['order_code'])->find();
            if (empty($order)) {
                $data['msg'] = '订单不存在';
                return $this->setResponseData($data)->send();
            }
            $orderCommodity = BuOrderCommodity::where('order_code', $input['order_code'])->field('actual_price,card_all_dis,count')->select();
            $totalMoney     = 0;
            foreach ($orderCommodity as $item) {
                $totalMoney = bcmul(bcadd($totalMoney, bcsub($item['actual_price'], $item['card_all_dis'], 2), 2), $item['count'], 2);
            }
            if ($totalMoney < $draw['get_draw_set']) {
                $data['msg'] = '抽奖活动支付金额不满足';
                return $this->setResponseData($data)->send();
            }
        }
        // 查询该商品是否已创建
        $map = [
            'user_id'       => $user_id,
            'is_enable'     => 1,
            'draw_id'       => $draw['id'],
            'draw_type'     => 1,
            'commodity_id'  => $commodityId,
            'get_draw_type' => ['in', [1, 2, 3]],
        ];
        $re  = DbUserDrawRecord::where($map)->find();
        if (!empty($re)) {
            $data['msg'] = '同一商品不能参与多次';
            return $this->setResponseData($data)->send();
        }

        try {

            Db::startTrans();
            // 增加获取抽奖次数记录
            $record = [
                'draw_id'       => $draw['id'],
                'user_id'       => $user_id,
                'draw_type'     => DbUserDrawRecord::DRAW_TYPE_ADD,
                'commodity_id'  => $commodityId,
                'order_code'    => $input['order_code'] ?? '',
                'get_draw_type' => $draw['get_draw_type'],
            ];
            if ($draw['get_draw_type'] == 5) {
                $record['status'] = 1;
            }
            DbUserDrawRecord::create($record);

            if ($draw['get_draw_type'] != 5) {
                // 判断活动的累计次数
                $map = [
                    'draw_id'   => $draw['id'],
                    'user_id'   => $user_id,
                    'draw_type' => DbUserDrawRecord::DRAW_TYPE_ADD,
                    'status'    => 0,
                    'is_enable' => 1,
                ];
                $num = DbUserDrawRecord::where($map)->count();
                if ($num < $draw['get_draw_set']) {
                    Db::commit();
                    $data['msg']      = '抽奖活动次数不满足';
                    $re2              = HaoWan::create('hao_wan')->getLotteryNum($member_id, $draw['game_id']);
                    $data['draw_num'] = $re2->getData()['times'];
                    return $this->setResponseData($data)->send();
                } else {
                    // 修改已统计的次数
                    DbUserDrawRecord::where($map)->update(['status' => 1]);
                }
            }

            // 查询用户抽奖数据
            $map      = ['user_id' => $user_id, 'draw_id' => $draw['id'], 'is_enable' => 1];
            $drawInfo = DbUserDraw::where($map)->find();
            if (!empty($drawInfo)) {
                if ($drawInfo['total_num'] >= $draw['draw_number']) {
                    $data['msg'] = '已达最高活动抽奖次数';
                    return $this->setResponseData($data)->send();
                }
                //
                $upd = [
                    'total_num' => $drawInfo['total_num'] + 1,
                ];
                DbUserDraw::where('id', $drawInfo['id'])->update($upd);
            } else {
                // 创建
                $add = [
                    'user_id'   => $user_id,
                    'draw_id'   => $draw['id'],
                    'is_enable' => 1,
                    'total_num' => 1,
                ];
                DbUserDraw::create($add);
            }

            // 调用接口增加次数
            $re1 = HaoWan::create('hao_wan')->addChance($member_id, $draw['game_id']);
            if (!$re1->isSuccess()) {
                return $this->setResponseError($re1->getMessage())->send();
            }
            $re2 = HaoWan::create('hao_wan')->getLotteryNum($member_id, $draw['game_id']);
            if (!$re2->isSuccess()) {
                return $this->setResponseError($re2->getMessage())->send();
            } else {
                $draw_num = $re2->getData()['times'];
            }
            Db::commit();
            $data = ['url' => $draw['draw_url'], 'draw_num' => $draw_num, 'msg' => 'success'];
            return $this->setResponseData($data)->send();
        }
        catch (Exception $e) {
            Db::rollback();
            $msg = $e->getMessage();
            return $this->setResponseError($msg)->send();
        }
    }

    //众筹明细信息
    //all_order==0 只返回众筹明细，不返回已购数量跟现金
    public function _crowdfund_info($crowd_id=0,$commodity_id='',$all_order=1,$user_id=0){
        $crowd_model =  new DbCrowdfund();
        $fileds = "b.crowdfund_id,a.title,a.start_time,a.end_time,a.target,a.target_val,a.alr_crowd,a.purchase_num,a.is_use_ticket,a.ticket_ids,a.brand,a.up_down_channel,a.un_stand_standard,a.refund_set,a.gather_id,a.is_pv_subsidy,a.theme_name,a.act_status,b.plan_status,b.meddle_price,b.meddle_count,b.meddle_people,b.meddle_order,a.is_use_ticket,a.ticket_ids,a.activity_image";
        $where = ['a.is_enable'=>1];
        if($crowd_id){
            $where['a.id'] = $crowd_id;
        }
        if($commodity_id){
            $where['b.commodity_id'] = $commodity_id;
        }
        $_where = sprintf("FIND_IN_SET('%s',a.up_down_channel)", $this->channel_type);
        if(in_array($this->channel_type,['GWSM','GWAPP'])){
            $agree_brand = 1;
            $where['a.brand'] = 1;
        }elseif (in_array($this->channel_type,['QCSM','QCAPP'])){
            $agree_brand = 2;
            $where['a.brand'] = 2;
        }elseif (in_array($this->channel_type,['PZ1ASM','PZ1AAPP'])){
            $agree_brand = 3;
            $where['a.brand'] = 3;
        }else{
            $agree_brand = 0;
            $where['a.brand'] = 0;
        }
        $crowd_info =  $crowd_model->getAllCrowdfund(['where' => $where,'_where'=>$_where,'field'=>$fileds,'order'=>"a.created_date desc"]);
        if($crowd_info){
            $crowd_info_one =  $crowd_info[0];
            $crowd_id = $crowd_info_one['crowdfund_id'];
            if(!$crowd_info_one['purchase_num']){
                $crowd_info_one['purchase_num']=999;
            }
            $stat = $crowd_model->getActStatus($crowd_info_one['start_time'],$crowd_info_one['end_time']);
            $crowd_info_one['act_status'] = $stat;
            //项目成功    `plan_status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '进度状态:0未开始;1进行中-未达标;2进行中-已达标;3项目失败-待处理;4项目成功-自动;5项目成功-人为;6项目失败-自动;7项目失败-人为;',
            if(in_array($crowd_info_one['plan_status'],[4,5,6,7])){
                $crowd_info_one['act_status'] = 3;
            }
            $agree_model = new DbCrowdfundAgreement();
            $agree_info =  $agree_model->getOne(['where'=>['is_enable'=>1,'brand'=>$agree_brand]]);
            if($agree_info){
                $crowd_info_one['crowdfund_agn']= $agree_info['content']; // 众筹协议
            }
            $cr_order_model = new DbCrowdfundOrder();
            if($user_id){
                $user_od_where = ['crowdfund_id'=>$crowd_id, 'commodity_id'=>$commodity_id,'is_enable'=>1,'user_id'=>$user_id,'pay_status'=>1];
                $user_info =  $cr_order_model->getOne(['where'=>$user_od_where,'field'=>"sum(num) all_num,sum(price) sp"]);
                $crowd_info_one['user_number'] = $user_info['all_num'];
            }else{
                $user_od_where = ['crowdfund_id'=>$crowd_id, 'commodity_id'=>$commodity_id,'is_enable'=>1,'pay_status'=>1];
                $user_info =  $cr_order_model->getOne(['where'=>$user_od_where,'field'=>"sum(num) all_num,sum(price) sp"]);
                $crowd_info_one['user_number'] =  $user_info['all_num'];
            }
            //退款设置 1众筹中-允许退款 2众筹成功-允许退款  0-不能退款
            if($crowd_info_one['refund_set']==1){
                $crowd_info_one['refund_text'] = "当您成功支付订单后，该订单可在众筹活动结束前申请退款。如果活动众筹成功，将按照订单顺序进行陆续发货，同时该订单将不可申请退款。如果项目在结束时未能达到众筹目标，我们将在活动结束后安排系统原路退款，敬请放心。如有任何疑问，请随时与我们联系。感谢您的支持与信任！";
            }
            if($crowd_info_one['refund_set']==2){
                $crowd_info_one['refund_text'] = "当您成功支付订单后，该订单不可在众筹活动结束前申请退款。如果活动众筹成功，将按照订单顺序进行陆续发货，该订单在发货前可进行申请退款。如果项目在结束时未能达到众筹目标，我们将在活动结束后安排系统原路退款，敬请放心。如有任何疑问，请随时与我们联系。感谢您的支持与信任！";
            }
            if(!$crowd_info_one['refund_set']){
                $crowd_info_one['refund_text'] = "当您成功支付订单后，该订单不支持申请退款。如果活动众筹成功，将按照订单顺序进行陆续发货，如果项目在结束时未能达到众筹目标，我们将在活动结束后安排系统原路退款，敬请放心。如有任何疑问，请随时与我们联系。感谢您的支持与信任！";
            }

            if(!$all_order){
                return  $crowd_info_one;
            }
            $cr_order_model = new DbCrowdfundOrder();
            $order_field = "crowdfund_id,commodity_id,sum(num) all_num,sum(price) sp,count(distinct(user_id)) pp_count";
            $od_where = ['crowdfund_id'=>$crowd_info_one['crowdfund_id'], 'commodity_id'=>$commodity_id,'is_enable'=>1,'pay_status'=>1];
            $od_info = $cr_order_model->getOne(['where' =>$od_where,'field'=>$order_field,'group'=>"commodity_id"]);
            $crowd_info_one['money'] = $crowd_info_one['meddle_price'];
            $crowd_info_one['sum_num'] = $crowd_info_one['meddle_count'];
            $crowd_info_one['peo_count'] = $crowd_info_one['meddle_people'];
            if($od_info['pp_count']>0){
                $crowd_info_one['money'] += $od_info['sp'];
                $crowd_info_one['peo_count'] += $od_info['pp_count'];
                $crowd_info_one['sum_num'] += $od_info['all_num'];
            }
            return $crowd_info_one;
        }else{
            return  false;
        }
    }

    // 商品活动图/氛围框
    public function goodsActivityImage($goods_ids, $channel_type){
        $kill_img_list  = [];
        $limit_img_list = [];
        $full_img_list  = [];
        $dis_img_list   = [];
        $group_img_list = [];
        $cheap_img_list = [];
        $gift_img_list  = [];
        $pre_img_list   = [];

        $now = date('Y-m-d H:i:s');
        $set_img_list = (new DbCommoditySet())->where(['commodity_id' => ['in', $goods_ids], 'activity_start_time' => ['<=', $now], 'activity_end_time' => ['>', $now], ['exp', "find_in_set('$channel_type', up_down_channel_dlr)"], 'is_enable' => 1, 'activity_image' => ['neq', '']])->column('activity_image', 'commodity_id');

        foreach ($goods_ids as $k => $goods_id){
            if (isset($set_img_list[$goods_id])) unset($goods_ids[$k]);
        }

        // 秒杀
        if (!empty($goods_ids)){
            $kill_img_list = (new DbSeckill())
                ->alias('a')
                ->join('t_db_seckill_commodity b', 'a.id=b.seckill_id')
                ->where(['a.act_status' => 2, 'a.start_time' => ['<=', $now], 'a.end_time' => ['>', $now], ['exp', "find_in_set('$channel_type', up_down_channel_dlr)"], 'a.is_enable' => 1, 'a.activity_image' => ['neq', ''], 'b.commodity_id' => ['in', $goods_ids]])
                ->column('a.activity_image', 'b.commodity_id');

            foreach ($goods_ids as $k => $goods_id){
                if (isset($kill_img_list[$goods_id])) unset($goods_ids[$k]);
            }
        }

        // 限时优惠
        if (!empty($goods_ids)){
            $limit_img_list = (new DbLimitDiscount())
                ->alias('a')
                ->join('t_db_limit_discount_commodity b', 'a.id=b.limit_discount_id')
                ->where(['a.act_status' => 2, 'a.start_time' => ['<=', $now], 'a.end_time' => ['>', $now], ['exp', "find_in_set('$channel_type', up_down_channel_dlr)"], 'a.is_enable' => 1, 'a.activity_image' => ['neq', ''], 'b.commodity_id' => ['in', $goods_ids]])
                ->column('a.activity_image', 'b.commodity_id');

            foreach ($goods_ids as $k => $goods_id){
                if (isset($limit_img_list[$goods_id])) unset($goods_ids[$k]);
            }
        }

        // 满优惠
        if (!empty($goods_ids)){
            $full_img_list = (new DbFullDiscount())
                ->alias('a')
                ->join('t_db_full_discount_comm_dlr b', 'a.id=b.discount_activity_id')
                ->where(['a.act_status' => 2, 'a.start_time' => ['<=', $now], 'a.end_time' => ['>', $now], ['exp', "find_in_set('$channel_type', up_down_channel_dlr)"], 'a.is_enable' => 1, 'a.activity_image' => ['neq', ''], 'b.commodity_id' => ['in', $goods_ids]])
                ->column('a.activity_image', 'b.commodity_id');

            foreach ($goods_ids as $k => $goods_id){
                if (isset($full_img_list[$goods_id])) unset($goods_ids[$k]);
            }
        }

        // N件N折
        if (!empty($goods_ids)){
            $dis_img_list = (new DbNDiscount())
                ->alias('a')
                ->join('t_db_n_discount_commodity b', 'a.id=b.n_id')
                ->where(['a.act_status' => 2, 'a.start_time' => ['<=', $now], 'a.end_time' => ['>', $now], ['exp', "find_in_set('$channel_type', up_down_channel_dlr)"], 'a.is_enable' => 1, 'a.activity_image' => ['neq', ''], 'b.commodity_id' => ['in', $goods_ids]])
                ->column('a.activity_image', 'b.commodity_id');

            foreach ($goods_ids as $k => $goods_id){
                if (isset($dis_img_list[$goods_id])) unset($goods_ids[$k]);
            }
        }

        // 多人拼团
        if (!empty($goods_ids)){
            $group_img_list = (new DbFightGroup())
                ->alias('a')
                ->join('t_db_fight_group_commodity b', 'a.id=b.fight_group_id')
                ->where(['a.act_status' => 2, 'a.start_time' => ['<=', $now], 'a.end_time' => ['>', $now], ['exp', "find_in_set('$channel_type', up_down_channel_dlr)"], 'a.is_enable' => 1, 'a.activity_image' => ['neq', ''], 'b.commodity_id' => ['in', $goods_ids]])
                ->column('a.activity_image', 'b.commodity_id');

            foreach ($goods_ids as $k => $goods_id){
                if (isset($group_img_list[$goods_id])) unset($goods_ids[$k]);
            }
        }

        // 优惠套装
        if (!empty($goods_ids)){
            $cheap_img_list = (new BuCheapSuitIndex())
                ->alias('a')
                ->join('t_bu_cheap_suit_commodity b', 'a.id=b.suit_id')
                ->where(['a.act_status' => 2, 'a.s_time' => ['<=', $now], 'a.e_time' => ['>', $now], ['exp', "find_in_set('$channel_type', up_down_channel_dlr)"], 'a.is_enable' => 1, 'a.activity_image' => ['neq', ''], 'b.commodity_id' => ['in', $goods_ids]])
                ->column('a.activity_image', 'b.commodity_id');

            foreach ($goods_ids as $k => $goods_id){
                if (isset($cheap_img_list[$goods_id])) unset($goods_ids[$k]);
            }
        }

        // 买赠
        if (!empty($goods_ids)){
            $gift_img_list = (new DbGift())
                ->alias('a')
                ->join('t_db_gift_commodity b', 'a.id=b.gift_id')
                ->where(['a.act_status' => 2, 'a.start_time' => ['<=', $now], 'a.end_time' => ['>', $now], ['exp', "find_in_set('$channel_type', up_down_channel_dlr)"], 'a.is_enable' => 1, 'a.activity_image' => ['neq', ''], 'b.commodity_id' => ['in', $goods_ids], 'b.is_gift' => 0])
                ->column('a.activity_image', 'b.commodity_id');

            foreach ($goods_ids as $k => $goods_id){
                if (isset($gift_img_list[$goods_id])) unset($goods_ids[$k]);
            }
        }

        // 预售活动
        if (!empty($goods_ids)){
            $pre_img_list = (new DbPreSale())
                ->alias('a')
                ->join('t_db_pre_sale_commodity b', 'a.id=b.pre_sale_id')
                ->where(['a.act_status' => 2, 'a.front_s_time' => ['<=', $now], 'a.front_e_time' => ['>', $now], ['exp', "find_in_set('$channel_type', up_down_channel_dlr)"], 'a.is_enable' => 1, 'a.activity_image' => ['neq', ''], 'b.commodity_id' => ['in', $goods_ids]])
                ->column('a.activity_image', 'b.commodity_id');

            foreach ($goods_ids as $k => $goods_id){
                if (isset($pre_img_list[$goods_id])) unset($goods_ids[$k]);
            }
        }

        return $set_img_list + $kill_img_list + $limit_img_list + $full_img_list + $dis_img_list + $group_img_list + $cheap_img_list + $gift_img_list + $pre_img_list;

    }
    /**
     * @param $card_ids
     * @param $user
     * @param $channel_type
     * @return array|void
     * 返回可以领取+已领取的卡券
     */
    public function cardCanTag($card_ids,$user,$channel_type){

        if (!empty($card_ids) && $user['id']) {
            $redis_name = sprintf("goods-list-card-can-tag-%s-%s-%s",implode(',',$card_ids),$user['id'],$channel_type);
            $card_ids_redis =  redis($redis_name);
            if($card_ids_redis){
                return $card_ids_redis;
            }
            $user_id = $user['id'];
            $event_model = new BuCardReceiveRecord();
            $card_model =  new DbCard();
            $where      = ['card_id' => ['in',$card_ids], 'user_id' => $user_id, 'is_enable' => 1, 'status' => ['<>', 2]];
            $card_event = $event_model->getList(['where' => $where]);
            $card_info_list =  $card_model->getList([
                'where'=>
                    ['id'=>['in',$card_ids], 'is_enable' => 1]
            ]);
            $card_info_arr = array_column($card_info_list,null,"id");
//            $card_r_arr = array_column($card_event->toArray(),null,"card_id");
//            $card_info_arr = [];
//            foreach($card_info_list as $card){
//                $card_info_arr[$card['id']] = $card;
//            }
            $todate = strtotime('Y-m-d H:i:s');
            $can_card = [];
            foreach ($card_ids as $k=> $v){
                if($card_event && isset($card_info_arr[$v])){
                    $card_r_count= 0;
                    $card_can_use_count =  0;
                    foreach ($card_event as $ev){
                        if($v==$ev['card_id']){
                            $card_r_count++;
                        }
                        if($ev['validity_date_end']>$todate && $ev['validity_date_start']<$todate && $ev==1){
                            $card_can_use_count++;
                        }
                    }
                    $card_info = $card_info_arr[$v];

                    if($card_info['available_count']<=$card_r_count){
                        if(!$card_can_use_count){
                            unset($card_ids[$k]);
                        }
                    }
                    if($card_can_use_count){
                        $can_card[]=$v;
                    }
                }
            }
            $can_get_card = array_diff($card_ids,$can_card);

            $netUserObj = new NetUser();
            $card_ret = $netUserObj->canGetCards($user, $can_get_card, $channel_type);
            $card_list_tmp=[];
            if (!empty($card_ret)) {
                foreach ($card_ids as $item) {
                    if (in_array($item, $card_ret)) {
                        $card_list_tmp[] = $item;
                    }
                }
            }
            $card_ids = array_merge($card_list_tmp,$can_card); //剩余可以领取+可以使用的券
            redis($redis_name,$card_ids,10);
            return $card_ids;
        }
    }

    //将根据类型分组并且只各获取其一

    public function sort_card_list($card_ids){
        if(!$card_ids){
            return [];
        }
        $redis_name = sprintf("goods-list-sort-card-list-%s",implode(',',$card_ids));
        $card =redis($redis_name);
//        $card =[];
        if($card){
            return  $card;
        }
        $card_model =  new DbCard();
        $card_info_list =  $card_model->getList([
            'where'=>
                ['id'=>['in',$card_ids], 'is_enable' => 1],
            'order'=>"IF(card_type = 1, card_quota, NULL) DESC, IF(card_type != 1, card_discount, NULL) ASC,validity_date_end asc ",
        ]);//越早结束越靠前
        $card = [];
        $card_type_arr = [];
//        print_json($card_info_list);
        foreach($card_info_list as $k=> $v){

            if($v['card_type']==1){
                if($v['least_cost']){
                    $card_desc = sprintf("满%s减%s",formatNumber($v['least_cost']),formatNumber($v['card_quota']));
                }else{
                    $card_desc = sprintf("%s元券",formatNumber((float)$v['card_quota']));
                }
            }else{
                $card_desc = sprintf("%s折券",formatNumber((float)$v['card_discount']));
            }
            if($v['is_gift_card']){
                $card_desc = '赠品券';
            }
            $v['tag_desc'] = $card_desc;
            if(!in_array($v['card_type'],$card_type_arr)){
                $card[] = $v;
                $card_type_arr[]=$v['card_type'];
            }
        }
//        print_json($card);
        redis($redis_name,$card,60);
        return $card;
    }

    public function _goods_card_arr($card_id_arr,$goods_set_id_arr,$sub_com_sku=[],$channel_type='',$goods_card_info=[],$car_series_id=''){
        $goods_card_arr = [];
        $cardid_type_goods_arr = [];
        $goods_use_card_sku_arr = [];
        $goods_use_card_id_arr = [];
        $list_goods_use_card_sku_arr = [];
//        $card_goods_model = new DbCommodityCard();
//        $where = ['b.is_enable'=>1,'a.is_enable'=>1];
//        $where['a.commodity_set_id'] = ['in', $goods_set_id_arr];
//        if($card_id_arr){
//            $where['a.card_id'] = ['in' , $card_id_arr];
//        }
//        $goods_card_info = $card_goods_model->alias('a')
//            ->join("t_db_card b", "b.id=a.card_id")
//            ->where($where)
//            ->field("a.commodity_id,a.commodity_set_id,a.card_id,a.group_sub_commodity_id,a.set_sku_ids,a.card_type,a.group_card_type,a.sorts,b.*")
//            ->select();
        if(!$goods_card_info){
            $goods_card_info = $this->get_goods_card($goods_set_id_arr,$card_id_arr,'',$channel_type,'',$car_series_id);
        }

        $card_ids = array_column($goods_card_info,'id');
        $goods_card=[];
//        print_json($goods_card_info);
        if ($goods_card_info) {
            foreach ($goods_card_info as $g_v) {
                $g_v_sku_ids = explode(',', $g_v['set_sku_ids']);
//                if(!$g_v['set_sku_ids'] && isset($sub_com_sku[$g_v['group_sub_commodity_id']])){
//                    $g_v_sku_ids = $sub_com_sku[$g_v['group_sub_commodity_id']];
//                    $g_v['set_sku_ids'] = implode(',',$g_v_sku_ids);
//                }
                $key = $g_v['id'] . '_' . $g_v['commodity_set_id'];
                $key_goods_id = $g_v['commodity_set_id'];
                $key2 = $g_v['commodity_set_id'] . '_' . $g_v['group_sub_commodity_id'];

                if($g_v['set_sku_ids']){
                    $goods_card[$key_goods_id][]=$g_v['id'];
                    if(!isset($goods_card_arr[$key]['sku_id'])){
                        $goods_card_arr[$key]['sku_id'] = $g_v_sku_ids;
                    }else{
                        $goods_card_arr[$key]['sku_id'] = array_merge($goods_card_arr[$key]['sku_id'],$g_v_sku_ids);
                    }
                    if(!isset( $goods_card_arr[$key][$g_v['group_sub_commodity_id']]['sku_id'])){
                        $goods_card_arr[$key][$g_v['group_sub_commodity_id']]['sku_id'] =$g_v_sku_ids;
                    }else{
                        $goods_card_arr[$key][$g_v['group_sub_commodity_id']]['sku_id']=array_merge($goods_card_arr[$key][$g_v['group_sub_commodity_id']]['sku_id'],$g_v_sku_ids);
                    }
                    if($g_v['group_card_type']!=2){
                        $goods_use_card_id_arr[$key2][]= $g_v['id'];
                        if(!isset($goods_use_card_sku_arr[$key2])){
                            $goods_use_card_sku_arr[$key2] = $g_v_sku_ids;

                        }else{
                            $goods_use_card_sku_arr[$key2] = array_merge($goods_use_card_sku_arr[$key2],$g_v_sku_ids);
                        }
                    }

                    if(!isset($list_goods_use_card_sku_arr[$key2])){
                        $list_goods_use_card_sku_arr[$key2] = $g_v_sku_ids;
                    }else{
                        $list_goods_use_card_sku_arr[$key2] = array_merge($list_goods_use_card_sku_arr[$key2],$g_v_sku_ids);
                    }
                    $goods_card_arr[$key]['group_sub_commodity_id'][] = $g_v['group_sub_commodity_id'];
                    $goods_card_arr[$key]['list'][] = $g_v;
                }

                $cardid_type_goods_arr[$g_v['commodity_set_id'] . '_' . $g_v['group_sub_commodity_id'] . '_' . $g_v['group_card_type']][]=$g_v['card_id'];
            }
        }
        return ['cards' => $card_ids, 'goods_card_arr'=>$goods_card_arr,'goods_use_card_sku_arr'=>$goods_use_card_sku_arr,'list_goods_use_card_sku_arr'=>$list_goods_use_card_sku_arr,'goods_use_card_id_arr'=>$goods_use_card_id_arr,'goods_card'=>$goods_card,'goods_card_info'=>$goods_card_info];
    }

    //$goods_set_id_arr传单个上架ID--主商品ID

    /**
     * @param $goods_set_id_arr 传单个上架ID--主商品setID
     * @param $sku_jj  满足的所有setsku
     * @param $goods_id_group 组合商品的子商品ID
     * @param $mz_type 满足的类型，默认1，这边只有1
     * @param $back_list 是否返回完全适配的商品列表
     * @param $sub_com_sku 子商品对应sku--z暂时没用
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function _detail_card($goods_set_id_arr,$sku_jj,$goods_id_group,$mz_type=1,$back_list=0,$card_id_arr=[],$sub_com_sku=[],$group_list=[],$channel_type='',$goods_card_info=[],$car_series_id=''){
        $goods_card = $this->_goods_card_arr($card_id_arr,$goods_set_id_arr,$sub_com_sku,$channel_type,$goods_card_info,$car_series_id);
//        print_json($goods_card);
        $goods_card_arr = $goods_card['goods_card_arr'];
        $goods_card_ids = $goods_card['goods_card'];
        $all_card_s = $goods_card['cards'];
        $goods_use_card_sku_arr = $goods_card['goods_use_card_sku_arr'];
        $goods_use_card_id_arr = $goods_card['goods_use_card_id_arr'];
        $goods_card_info = $goods_card['goods_card_info'];


        $mz_card = $this->_mz_card_group($goods_card['cards'],$goods_card_arr,$goods_set_id_arr,$sku_jj,$goods_id_group,$mz_type,$channel_type);
        //返回完全适配的商品列表
        $card_list=[];
        $back_list_data =  ['card_list'=>[],'mz_card'=>$mz_card,'goods_use_card_sku_arr'=>$goods_use_card_sku_arr,'goods_use_card_id_arr'=>$goods_use_card_id_arr,'all_card_s'=>$all_card_s,'list_goods_use_card_sku_arr'=>$goods_card['list_goods_use_card_sku_arr'],'goods_card'=>$goods_card_ids];
        if($back_list){
            $group_card_mz_id = [];
            foreach($goods_card_arr as $v){
                foreach($v['list'] as $list_v){
                    if($list_v['group_card_type']==2 && in_array($list_v['id'],array_values($mz_card))){
                        $group_card_mz_id[]=$list_v['id'];
                    }
                }
            }
            if(!$group_card_mz_id || !$sku_jj){
                return $back_list_data;
            }

            $card_model =  new DbCard();
            $card_list = $card_model->getList(['where' =>['id'=>['in',$group_card_mz_id]],'order' =>'id desc']);
            $set_sku_model =  new DbCommoditySetSku();


            $sku_spe_list = $set_sku_model->getSkuSpec(['where'=>['a.id'=>['in',$sku_jj]],'field'=>"a.id,c.sp_value_name,d.sp_name,flat.commodity_name,flat.cover_image,b.image"]);
            $sku_c_json_cn_arr =[];
            $sub_goods_info = [];
            if($sku_spe_list){
                foreach ($sku_spe_list as $v){
                    if($v['sp_value_name'] && $v['sp_name']){
                        $sku_c_json_cn_arr[$v['id']]['sp_value_name'][]= $v['sp_name'] . ":" .$v['sp_value_name']." ";
                    }else{
                        $sku_c_json_cn_arr[$v['id']]['sp_value_name'][]= $v['sp_value_name'];
                    }
                    $sku_c_json_cn_arr[$v['id']]['commodity_name']= $v['commodity_name'];
                    $sku_c_json_cn_arr[$v['id']]['image']= !empty($v['image'])?$v['image']:$v['cover_image'];
                    $sku_c_json_cn_arr[$v['id']]['id']= $v['id'];
                }
            }
//            echo json_encode($sku_jj);
//            foreach ($goods_card_arr as $k=> $v){
//                echo json_encode($v['sku_id']);
//            }
//            die();
            foreach ($card_list as $k=> $v){
                $gv_card=[];
                if($v['card_type']==1){
                    if($v['least_cost']){
                        $card_desc = sprintf("满%s减%s",
sprintf("%.2f", $v['least_cost']),$v['card_quota']);
                    }else{
                        $card_desc = sprintf("%s元券",$v['card_quota']);
                    }
                }else{
                    $card_desc = sprintf("%s折",$v['card_discount']);
                }
                $card_list[$k]['card_word'] = $card_desc;
//                print_r($goods_id_group);
                foreach ($goods_id_group as $g_v){
                    if(isset($goods_card_arr[$v['id'] . '_' . $goods_set_id_arr][$g_v])){
                        $sub_sku_ids_str = $goods_card_arr[$v['id'] . '_' . $goods_set_id_arr][$g_v]['sku_id'];
                        $sub_sku_ids = array_values($sub_sku_ids_str);
                        $goods_data = [];
                        $sub_sku_ids_now =  array_values(array_intersect($sub_sku_ids_str,$sku_jj));
//                        echo json_encode($sub_sku_ids_now);
                        if ($sub_sku_ids_now) {
                            $goods_info = $this->getCommodityInfo(0, 0, 0, 0, 0, 3, 0, $this->user, '', $g_v, 0, 0, 0, 1, 2, 1, '', '', $sku_jj, $goods_set_id_arr,1);
                            if(isset($goods_info['sku_list'])){
                                $sku_list = $goods_info['sku_list'];
                                foreach ($sku_list as $id_v) {
                                    if (isset($sku_c_json_cn_arr[$id_v['sku_id']]) && in_array($id_v['sku_id'],$sub_sku_ids_str)) {
                                        $id_v['sp_value_name'] = implode(',', $sku_c_json_cn_arr[$id_v['sku_id']]['sp_value_name']);
                                        $id_v['commodity_name'] = $sku_c_json_cn_arr[$id_v['sku_id']]['commodity_name'];
                                        $id_v['commodity_id'] = $goods_info['commodity_data']['id'];
                                        $id_v['set_sku_id'] = $id_v['sku_id'];
                                        $id_v['price'] = $id_v['old_price'];//102说用这个，原价
                                        $id_v['sp_value_list'] = implode(',',$id_v['sp_value_arr']);
                                        $id_v['group_sub_commodity_id'] = $g_v;
                                        $goods_data[] = $id_v;
                                    }
                                }
                                if($goods_data){
                                    $gv_card[] = $goods_data;
                                }
                            }
                        }
                    }
                }

                //递归  [A,B]、[a] 和 [E,F] 合并成类似于 [{A,a,E},{A,a,F},{B,a,E},{B,a,F}] 的格式
                $new = $this->combineArrays($gv_card);
                if($gv_card){
                    $card_list[$k]['list']= $new;
                }else{
                    unset($card_list[$k]);
                }
            }
        }
        return ['card_list'=>array_values($card_list),'mz_card'=>$mz_card,'goods_use_card_sku_arr'=>$goods_use_card_sku_arr,'goods_use_card_id_arr'=>$goods_use_card_id_arr,'all_card_s'=>$all_card_s,'list_goods_use_card_sku_arr'=>$goods_card['list_goods_use_card_sku_arr'],'goods_card'=>$goods_card_ids];
    }

    //处理打散数组
    private function combineArrays($arrays) {
        $result = [];
        $numArrays = count($arrays);

        // 初始化一个数组来保存当前的组合
        $currentCombination = [];


        // 调用递归辅助函数开始生成组合
        $this->generateCombinations($result, $arrays, $currentCombination, 0,$numArrays);

        return $result;
    }

    // 递归辅助函数，用于生成组合
    private function generateCombinations(&$result, &$arrays, &$currentCombination, $index,$numArrays) {
        // 如果已经处理完所有数组
        if ($index == $numArrays) {
            // 将当前组合添加到结果数组中
            $result[] = ['goods_list'=>$currentCombination];
            return;
        }

        // 遍历当前数组的所有元素
        foreach ($arrays[$index] as $element) {
            // 将当前元素添加到当前组合中
            $currentCombination[] = $element;

            // 递归调用以处理下一个数组
            $this->generateCombinations($result, $arrays, $currentCombination, $index + 1,$numArrays);

            // 回溯，移除当前元素以便尝试下一个元素
            array_pop($currentCombination);
        }
    }



    /**
     * @param $goods_un_card_arr 商品关联卡ID
     * @param $goods_card_arr commodity_card处理后数组
     * @param $commodity_set_id 商品上架ID
     * @param $sku_jj  适配skuId
     * @param $goods_id_group 子商品数组
     * @param $mz_type $mz_type  1组合商品 2普通商品
     * @return void  匹配卡券ID
     */
    public function _mz_card_group($goods_un_card_arr,$goods_card_arr,$commodity_set_id,$sku_jj,$goods_id_group=[],$mz_type=1,$channel_type){
        $goods_model =  new DbCommodity();
        $card_model =  new DbCard();
        //卡券领取在领取接口重写了，这里可以不管
//        $where = ['id'=>['in',$goods_un_card_arr],'validity_date_start'=>[['ELT',date('Y-m-d')],['exp','is null'],'or'],'validity_date_end'=>[['EGT',date('Y-m-d')],['exp','is null'],'or']];
//        $where[]= ['exp', " (find_in_set('{$channel_type}',up_down_channel_dlr)) "];
//        $card_id_arr =  $card_model->getColumn(['where'=>$where,'column'=>'id']);
//        $goods_un_card_arr =$card_id_arr;
//        print_json($card_model->getLastSql());
        if($mz_type==1){
//            $goods_un_card_arr = array_values(array_unique($goods_un_card_arr));

            //todo 增加1L机油过滤
            $goods_where_1l = [
                'id'=>['in',$goods_id_group],
                'machine_oil_type'=>1
                ];
            $goods_list = $goods_model->getList_old(['where'=>$goods_where_1l]);
            $one_oil_goods = [];
            foreach ($goods_list as $v){
                $one_oil_goods[]=$v['id'];
                foreach ($goods_id_group as $kk=>$vv){
                    if($v['id']==$vv){
                        unset($goods_id_group[$kk]);
                    }
                }
            }

            foreach($goods_un_card_arr as $gn_k=>$gn_v) {
                $key = $gn_v . '_' . $commodity_set_id;
                $goods_card_one = isset($goods_card_arr[$key]['list'][0])?$goods_card_arr[$key]['list'][0]:[];

                if($goods_card_one){
                    //卡券对应子商品信息
                    $g_sub_goods_id=$g_all_goods_id = array_unique($goods_card_arr[$key]['group_sub_commodity_id']);
                    $g_all_goods_id =  array_diff($g_all_goods_id,$one_oil_goods);
                    //新版本之后才有数据
                    if($g_sub_goods_id){
                        //需要判断子商品下的sku是否有已满足的
                        foreach($g_sub_goods_id as $g_sub_k=>  $g_sub_v){
                            //卡券对应子商品所有匹配sku
                            $g_sub_v_sku_str =  $goods_card_arr[$key][$g_sub_v]['sku_id'];
                            $g_sub_v_sku= array_values($g_sub_v_sku_str);
                            $jj_g_sub_sku = array_intersect($sku_jj,$g_sub_v_sku);
                            if(!$jj_g_sub_sku){
                                unset($g_sub_goods_id[$g_sub_k]);
                            }
                            if(in_array($g_sub_v,$one_oil_goods)){
                                unset($g_sub_goods_id[$g_sub_k]);
                            }
                            if($gn_v==16470689397638144){
//                                var_dump($jj_g_sub_sku);
//                                var_dump($sku_jj);
//                                var_dump($g_sub_goods_id);
//                                var_dump($goods_id_group);
//                                var_dump(array_intersect($g_sub_goods_id,$goods_id_group));
//                                var_dump(array_unique($g_all_goods_id));
//                                var_dump($g_sub_goods_id);
                            }
                        }

                        if($goods_card_one['group_card_type']==2){
                            if(count(array_intersect($g_sub_goods_id,$goods_id_group))!=count(array_unique($g_all_goods_id))){
                                unset($goods_un_card_arr[$gn_k]);
                            }
                        }else{
                            if(!array_intersect($g_sub_goods_id,$goods_id_group) || !$g_sub_goods_id){
                                unset($goods_un_card_arr[$gn_k]);
                            }
                        }
                    }
                }
//                $list[$k]['card_list'] = $goods_un_card_arr;
            }
//            dd($goods_un_card_arr);
            return $goods_un_card_arr;

        }
        if($mz_type==2){
            foreach($goods_un_card_arr as $gn_k=>$gn_v) {
                $goods_card_one = isset($goods_card_arr[$gn_v . '_' . $commodity_set_id]['list'][0])?$goods_card_arr[$gn_v . '_' . $commodity_set_id]['list'][0]:[];
                if($goods_card_one){
                    $one_goods_card_sku_arr = $goods_card_arr[$gn_v . '_' . $commodity_set_id]['sku_id'];
                    if(!array_intersect($sku_jj,$one_goods_card_sku_arr)){
                        unset($goods_un_card_arr[$gn_k]);
                    }
//                    $list[$k]['card_list'] = $goods_un_card_arr;
                }

            }
            return $goods_un_card_arr;
        }

    }

    /**
     * @param $vv g_s_sid 卡券关联表关联skuid;g_s_goods_id 关联表子商品id
     * @return int
     */
    public function _cl_can_card($vv,$real_sku_id){
        //组合商品，并且设置了sku，进行过滤
        $in_one_sku_card=1;
//        print_json($vv,$real_sku_id);
        if($vv['sku_c_json'] && $vv['g_s_sid']){
            // && $vv['group_card_type']==2
            $g_s_sid_arr = explode(',',$vv['g_s_sid']);
            $g_s_goods_id_arr = explode(',',$vv['g_s_goods_id']);
            $g_s_goods_id_count =  count(array_unique($g_s_goods_id_arr));
            $sku_id_key = $real_sku_id;
            $sku_inter = array_intersect($sku_id_key,$g_s_sid_arr);
            //全部满足的需要  交集sku数量==配置的子商品数
            if($vv['group_card_type']==2){
                if(count($sku_inter)!=$g_s_goods_id_count){
                    $in_one_sku_card=0;
                }
            }else{
                if(!$sku_inter){
                    $in_one_sku_card=0;
                }
            }
        }
        return $in_one_sku_card;
    }

    //判断返回可用券  可领+可用
    public function _checkGroupCardByGoodsId($sku_json,$user,$channel_type){
        //can_use_card 可用券类型，1可用券，2其他规格可用，0无券
        $sku_jj =  array_column($sku_json,"sku_id");
        $sub_goods_id_arr =  array_column($sku_json,"group_sub_commodity_id");

        $set_sku_model =  new DbCommoditySetSku();
        $set_sku =  $set_sku_model->getOne(['where'=>['id'=>['in',$sku_jj]]]);
        //直接通过goods_list获取匹配的卡券ids;  这是为了判断整个商品是否有券
        $net_goods =  new NetGoods();
        $g_list = $net_goods->goodsList(['pageSize' => 1000, 'commodity_ids' => $set_sku['commodity_id'],'use_gift_card'=>1], $user, $channel_type);
        $goods_arr_card=[];
        if(isset($g_list['msg']['data'])){
            foreach ($g_list['msg']['data'] as $g_v){
                $goods_arr_card = $g_v['card_list']??[];
            }
        }
//        print_json($goods_arr_card);
//        $can_user_card_goods_redis_name = $user['id'].'can_user_card_goods_redis_name-';
//        $can_user_card_goods_redis =  redis($can_user_card_goods_redis_name.$set_sku['commodity_id']);
        $can_use_card = 0;
//        var_dump($goods_arr_card);
//        $goods_arr_card=1;
        if($goods_arr_card){
            $can_use_card = 2;
//            $card_list = $this->user_can_card($set_sku['commodity_set_id'],$user,$channel_type,$sku_jj);//用户可领+可用卡卡券 普通商品第一层过滤
//            $card_id_arr = [];
//            foreach ($card_list as $v){
//                if(($v['available_quantity'] && $v['is_can_receive']) || $v['can_use_card']>0){
//                    $card_id_arr[]=$v['id'];
//                }
//            }
            $detail_card = $this->_detail_card($set_sku['commodity_set_id'],$sku_jj,$sub_goods_id_arr,1,0,$goods_arr_card,[],[],$channel_type);
            if($detail_card['all_card_s']){
                if($detail_card['mz_card']){
                    $can_use_card = 1;
                }
            }

//            print_json($card_id_arr);
//            if($card_id_arr){
//                $detail_card = $this->_detail_card($set_sku['commodity_set_id'],$sku_jj,$sub_goods_id_arr,1,0,$card_id_arr,[],[],$channel_type);
//                if($detail_card['all_card_s']){
//                    if($detail_card['mz_card']){
//                        $can_use_card = 1;
//                    }
//                }
//            }
        }


        return ['can_use_card'=>$can_use_card];
    }

    //用户可领+可用卡卡券
    public function user_can_card($commodity_set_id,$user,$channel_type,$sku_jj=[]){
        $card_list = $this->_goods_can_get_card($commodity_set_id, $user['id'], $channel_type,0,$sku_jj,0);
        if (!empty($card_list)) {
            $card_ids = [];
            $card_no_c_ids = [];
            foreach ($card_list as $item) {
                if(($item['available_quantity'] && $item['is_can_receive']==1) || $item['can_use_card']>0){
                    if($item['can_user_type']) {
                        $card_ids[] = $item['id'];
                    }else{
                        $card_no_c_ids[]=$item['id'];
                    }
                }

            }
            $card_ret = [];
            //登录才进入判断人群，没有登录以及不需要判断人群的在  $card_no_c_ids 里 合并了人群跟非人群的就是总的
            if($user['id']){
                $netUserObj = new NetUser();
                $card_ret = $netUserObj->canGetCards($user, $card_ids, $this->channel_type);
            }
            $card_all_ret = array_merge($card_ret,$card_no_c_ids);
            $card_list_tmp=[];
            if (!empty($card_all_ret)) {
                foreach ($card_list as $item) {
                    if (in_array($item['id'], $card_all_ret)) {
                        $card_list_tmp[] = $item;
                    }
                }
            }
            $card_list = $card_list_tmp;
        }
        return $card_list;
    }

    /**
     * 商品关联了哪些卡券+分类
     */
    public function get_goods_card($commodity_set_ids,$card_ids=[],$sub_goods_id='',$channel_type='',$dd_dlr_code='',$car_series_id=''){
//        print_json($commodity_set_ids);
        $flat_model =  new DbCommodityFlat();
        $to_date  = date('Y-m-d');
        $where = ['b.is_enable'=>1,'a.is_enable'=>1,'b.receive_start_date'=>['<=',$to_date],'b.validity_date_end'=>['>=',$to_date]]; //
        $where['flat.commodity_set_id'] = ['in', $commodity_set_ids];
        $where[] = ['exp',sprintf("FIND_IN_SET('%s',b.up_down_channel_dlr)",$channel_type)];
//        $car_series_id = session('car_s_id' );//应该从前端传，比如购物车，比如订单确认
        if($card_ids){
            if(is_array($card_ids)){
                $card_ids = array_unique($card_ids);
            }
            $where['a.card_id'] = ['in' , $card_ids];
        }
//        $where[] = ['exp',sprintf(" FIND_IN_SET('%s',b.car_series_id_str) or b.car_series_id_str='' or b.car_series_id_str is null ",$car_series_id)];
        if($sub_goods_id){
//            $goods_card_where['b.group_sub_commodity_id'] = $sub_goods_id;
            $where[] = ['exp',sprintf("a.group_sub_commodity_id=%s or a.group_sub_commodity_id=''",$sub_goods_id)];
        }
        if($dd_dlr_code){
            //apply_dlr_code
            $where[] = ['exp',sprintf(" FIND_IN_SET('%s',b.apply_dlr_code) or b.apply_dlr_code='' or b.apply_dlr_code is null ",$dd_dlr_code)];

        }
//        else{
//            $where[] = ['exp',"b.apply_dlr_code='' or b.apply_dlr_code is null "];
//
//        }
        //在外面已经做了过滤
//        if($car_series_id){
//            $where[] = ['exp',sprintf(" FIND_IN_SET('%s',b.car_series_id_str) or b.car_series_id_str='' or b.car_series_id_str is null ",$car_series_id)];
//        }
//        SELECT
//    flat.commodity_set_id,    flat.commodity_id,    b.id card_id,    a.group_sub_commodity_id sub_goods_id,    a.set_sku_ids,    a.group_card_type,    a.class_id
//FROM
//    t_db_commodity_flat flat
//    JOIN t_db_commodity_card a ON (( FIND_IN_SET( a.class_id,flat.comm_type_id_str )) OR a.commodity_set_id = flat.commodity_set_id )
//    JOIN t_db_card b ON b.id = a.card_id
//WHERE
//    flat.commodity_set_id = 7319

        $goods_card_info =  $flat_model->alias('flat')
            ->join('t_db_commodity_card a','a.commodity_set_id = flat.commodity_set_id') //FIND_IN_SET( a.class_id, flat.comm_type_id_str ) OR--
            ->join("t_db_card b", "  b.id = a.card_id and ((( b.car_config_code IS NOT NULL || b.car_config_code <> '' ) AND flat.dd_commodity_type > 0 ) || ( b.car_config_code IS NULL || b.car_config_code = '' ))")
            ->join("t_db_activity act",sprintf(" b.activity_id = act.activity_id and FIND_IN_SET( '%s', act.up_down_channel_dlr ) and (act.select_obj=1 || (act.select_obj in (2,3) and flat.dd_commodity_type>0))",$channel_type),'left')//备件类才关联车型+vin
            ->join("t_db_activity_card act_card", "b.activity_id = act_card.activity_id AND b.id = act_card.card_id", 'left') // 新增：关联活动卡券表获取激活场景
            ->where($where)
            ->field("flat.commodity_set_id,flat.commodity_id,a.group_sub_commodity_id sub_goods_id,a.set_sku_ids ,a.group_card_type ,a.class_id,b.*,a.card_id ,act.receive_coupon_points,a.group_sub_commodity_id,b.card_name name,flat.dd_commodity_type,act.push_type,act.select_obj,act_card.coupon_activate_scene_list")
            ->order("b.created_date desc")
//            ->group('b.id')
            ->select();
        //,a.commodity_id 不group by
        //->group('b.id,a.commodity_id') 暂时用回来
//        print_json($flat_model->getLastSql());
        if($goods_card_info){
            $goods_card_info = collection($goods_card_info)->toArray();
            foreach ($goods_card_info as $k=>$v){
                $goods_card_info[$k]['id'] = (string)$v['id'];
                $goods_card_info[$k]['card_id'] = (string)$v['card_id'];
                $goods_card_info[$k]['is_can_receive']=0;

                if($v['receive_coupon_points']){
                    $receive_coupon_points =  explode(',',$v['receive_coupon_points']);
                    if(in_array($this->goods_card_point,$receive_coupon_points)){
                        $goods_card_info[$k]['is_can_receive']=1;
                    }
                }
                $card_time_word = sprintf("%s ~ %s",$this->dataFYmdpoint($v['validity_date_start']),$this->dataFYmdpoint($v['validity_date_end']));
                if($v['date_type']==2){
                    if($v['fixed_begin_term']>0){
                        $card_time_word=sprintf("领取后%s天生效，有效天数%s天",$v['fixed_begin_term'],$v['fixed_term']);
                    }else{
                        $card_time_word=sprintf("领取后有效天数%s天",$v['fixed_term']);
                    }
                }
                $goods_card_info[$k]['card_time_word']=$goods_card_info[$k]['card_time_word1']=$card_time_word;

            }
        }
        return $goods_card_info;
    }

    /**
     * @param $commodity_set_ids
     * @param $user
     * @param $channel_type
     * @param $card_ids
     * @param $sub_goods_id
     * $use_get -- 是否领取卡券列表
     * @return array[]|false[]
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     * 可领取--活动中心+已领取数据  -- 还没有做sku过滤，只关联了商品
     */
    public function card_get_use($commodity_set_ids,$goods_info=[],$user,$channel_type,$card_ids=[],$sub_goods_id='',$use_get=1,$query_card_list=[],$dd_dlr_code='',$from='',$componentType='',$status = 99 ,$use_gift_card=0){//
        //赠品使用赠品券 $use_gift_card 预估价的时候要传1过来那么未冻结的不可用
        $data =$data1= ['get_card_list'=>[],'all_card'=>[],'card_rules'=>[],'goods_card_rule'=>[],'not_article_work'=>'','push_type_err'=>''];
        if(!$commodity_set_ids && !$query_card_list){
            return  $data;
        }

        $redis_name = md5(json_encode([
            'set_id'=>$commodity_set_ids,
            'user'=>$user,
            'card_ids'=>$card_ids,
            'query_card_list'=>$query_card_list,
            'dd_dlr_code'=>$dd_dlr_code,
            'sub_goods_id'=>$sub_goods_id,
        ]));
//        $data = \redis($redis_name);
        $data = [];

        if(!$data){
            $to_day = date('Y-m-d');
            if(empty($query_card_list)){
                $card_rules = $this->get_card_rule($commodity_set_ids,$card_ids,$sub_goods_id,[],$channel_type,$dd_dlr_code,$user);
//        $user = session("net-api-user-info");
                $goods_card_info =  $card_rules['all_card_list'];
            }else{
                $card_rules = [];
                $goods_card_info = $query_card_list;
                foreach($goods_card_info as $kk=>$goods_card_item){
                    $card_time_word = sprintf("%s ~ %s",$this->dataFYmdpoint($goods_card_item['validity_date_start']),$this->dataFYmdpoint($goods_card_item['validity_date_end']));
                    if($goods_card_item['date_type']==2){
                        if($goods_card_item['fixed_begin_term']>0){
                            $card_time_word=sprintf("领取后%s天生效，有效天数%s天",$goods_card_item['fixed_begin_term'],$goods_card_item['fixed_term']);
                        }else{
                            $card_time_word=sprintf("领取后有效天数%s天",$goods_card_item['fixed_term']);
                        }
                    }
                    $goods_card_info[$kk]['card_time_word']=$goods_card_info[$kk]['card_time_word1']=$card_time_word;
                }

            }
            if(!$goods_card_info){
                return  $data1;
            }

            $all_act_ids =  [];
            $rel_card_ids  =[];
            $mall_card = [];

            foreach ($goods_card_info as $k=> $v){
           // echo $v['card_id'];echo "<br/>";
                if($v['activity_id'] && !in_array($v['card_id'],$mall_card) && $v['activity_id']!='-1'){
                    $all_act_ids[]=$v['activity_id'];
                    $rel_card_ids[] = $v['quick_win_card_id'];
                    $mall_card[] = $v['card_id'];

                }
            }

            $netActivityCenter = new NetActivityCenter();
            $act_data = ['activityIdList'=>array_values($all_act_ids),'couponIdList'=>array_values($rel_card_ids),'channel_type'=>$channel_type,'oneid'=>$user['one_id']??'','goods_set_id'=>$commodity_set_ids,'from_mall'=>$from,'mall_card'=>$mall_card];

            $get_list = $netActivityCenter->getMallActivityCouponList($user,$act_data,$componentType);
            $coupon_arr = [];
            $user_car_s_id_arr=[];
            $vin_car_arr=[];
            if($get_list){
//            2.使用卡券的时候当前车型必须是匹配的车型，不然就在不可用列表中
                $user_car_s_id_arr = $get_list[0]['car_s_id_arr'];
                $user_d_vin = $get_list[0]['user_vin'];
                $user_bind_car = $get_list[0]['user_bind_car'];
                $vin_car_arr = $get_list[0]['vin_car_arr'];
                foreach ($get_list as $g_v){
                    $vin_coupon_arr=[];
                    if(isset($g_v['receiveDataList']) && $g_v['receiveDataList']){
                        foreach ($g_v['receiveDataList'] as $v_v){
                            $vin_coupon_arr[]=[
                                'vin'=>$v_v['vin'],
                                'can_num'=>$v_v['canReceive'],
                                'have_get'=>$v_v['received'],
                            ];
                        }
                        if($vin_coupon_arr){
                            $can_num = min(array_column($vin_coupon_arr,'can_num'));
                            $coupon_arr[$g_v['couponId']]=[
                                'can_num' => $can_num,
                                'vin_list'=>$vin_coupon_arr
                            ];
                        }
                    }
                }
            }

//        $get_card_ids =  array_column($get_list,'card_id');
            $card_list = $this->_checkUserCardV1($goods_card_info, 1, $user['id'],$channel_type,$use_gift_card);//有领取就带上can_use_card
            $not_article_work='';
            $can_get_card_list=[];
            $push_type_err = [];

            if ($card_list) {
                $not_article_work1 = '';
                $all_change_car = 1;
                $not_a_vin = [];
                $have_card_id_arr = [];

                $all_art_work = '';
                $all_art_work_vin = [];

                foreach ($card_list as $k => $v) {
                    if(isset($v['receive_vin'])){
                        if($v['receive_vin']){
                            if(isset($v['dd_commodity_type'])){
                                if(!$v['dd_commodity_type']){
                                    unset($card_list[$k]);
                                    continue;
                                }
                            }
                        }
                    }

                    if($v['car_series_id_str']){
                        if(isset($v['dd_commodity_type'])){
                            if(!$v['dd_commodity_type']){
                                unset($card_list[$k]);
                                continue;
                            }
                        }
                        if(!$user_car_s_id_arr){
                            unset($card_list[$k]);
                            continue;
                        }
                        //领券，首页，专题，都按照车型列表，
                        if(!empty($query_card_list) || in_array($from,['cart_list'])){
                            if(!array_intersect($user_car_s_id_arr,explode(',',$v['car_series_id_str']))){
                                unset($card_list[$k]);
                                continue;
                            }
                        }else{
                            //商品相关的就按照当前车型
                            if(!in_array($user['car_series_id'],explode(',',$v['car_series_id_str']))){
                                unset($card_list[$k]);
                                continue;
                            }
                        }

                    }

                    $card_list[$k]['id'] = (string)$v['id'];
                    $card_list[$k]['card_id'] = $card_list[$k]['id'];
                    $coupon_one = $coupon_arr[$v['quick_win_card_id']]??[];

                    $change_car=1;
                    $one_hava_get = 0;
                    $one_can_get = 0;
                    $one_all_can_get =0;
                    $article = '';
                    $article1 = '本券适用您车辆:';
                    $not_bind_article = '';
                    $one_card_vin = [];

                    if($coupon_one){
                        foreach ($coupon_one['vin_list'] as $c_o_v){
                            if($c_o_v['vin']){
                                $one_card_vin[]=$c_o_v['vin'];
                                if($user_d_vin == $c_o_v['vin']){
                                    $change_car=0;
                                    $all_change_car=0;//只要一个匹配就不显示了
                                    $one_hava_get = $c_o_v['have_get'];
                                    $one_can_get = $c_o_v['can_num'];
                                    $article=sprintf("本券适用您车辆:%s %s",$user_bind_car,$user_d_vin);
                                }
                                if($user_d_vin != $c_o_v['vin']){
                                    $art_work=sprintf(" %s %s ，",$vin_car_arr[$c_o_v['vin']]??'',$c_o_v['vin']);
                                    $article1.=$art_work;
                                    $all_art_work.=$art_work;
                                    $all_art_work_vin[]=$c_o_v['vin'];
                                    if(!in_array($c_o_v['vin'],$not_a_vin)){
                                        $not_article_work1.=$article1.', ';
                                        $not_a_vin[]=$c_o_v['vin'];
                                    }
                                    $one_all_can_get+=$c_o_v['can_num'];

                                }
                            }else{
                                $one_can_get = $c_o_v['can_num'];
                                $one_hava_get = $c_o_v['have_get'];
                                $change_car=0;
//                                $all_change_car=0;//只要一个匹配就不显示了

                            }
                        }
                        if($change_car==1){
                            $article = $article1.sprintf(" 当前默认车辆为:%s %s，请切换车辆领券 ",$user_bind_car,$user_d_vin);
                            $not_bind_article = $article1.'您可切换默认车后再领取';
                        }
                    }


                    $card_list[$k]['change_car'] = $change_car;
                    if($v['have_get']){
                        $one_hava_get = $v['have_get'];
                    }
                    //新增已领取显示  || $one_hava_get
                    //
                    if($coupon_one || $v['can_use_card'] || $one_hava_get){
                        $card_list[$k]['have_get']  = $one_hava_get;
                        $card_list[$k]['available_quantity']  = $change_car==1?$one_all_can_get:$one_can_get;//改成从接口拿,不匹配取总数，匹配取他自己的数
                        // 券限领1张，如果在卡券中心发了券，不是通过活动中心接口领券，活动中心还是会返回这个券是可领取。
                        //活动中心改了

//                        if(isset($v['available_quantity1'])){
//                            if($card_list[$k]['available_quantity']>$v['available_quantity1']){
//                                $card_list[$k]['available_quantity'] = $v['available_quantity1'];
//                            }
//
//                        }
                        $card_list[$k]['is_received']  = $one_hava_get>0?1:0;
                        $card_list[$k]['vin_list'] =   $coupon_one['vin_list']??[];
                        $card_list[$k]['vin'] =  $one_card_vin;
                        $card_list[$k]['article'] =   $article;
//                        if($v['id']=='23137988654433280'){
//                            print_json($v['available_quantity'],$v);
//                        }
                        if( $v['available_quantity']>0){
                            $card_list[$k]['card_time_word'] =   $v['card_time_word1'];
                        }
                        $card_list[$k]['not_bind_article'] =   $not_bind_article;
//                        print_json($v);
                        //$card_list[$k]['available_quantity']>0 || $one_hava_get>0 可领或者有领才会展示 人群包配置人但是领取限制是vin会出现两者都是0
//                        if($v['id']=='24323102107272192'){
//                            echo 'is_can_receive:=>'.$v['is_can_receive']."<br/>";
//
//                            echo 'available_quantity:=>'.$card_list[$k]['available_quantity']."<br/>";
//                            echo 'one_hava_get:=>'.$one_hava_get."<br/>";
//                            echo 'to_day:=>'.$to_day."<br/>";
//                            echo 'receive_start_date:=>'.$v['receive_start_date']."<br/>";
//                            echo 'receive_end_date:=>'.$v['receive_end_date']."<br/>";
//                            var_dump($have_card_id_arr);echo "<br/>";
//                        }
                        // status = 0表示全部 1表可领 2 已领  表示商城领券页用 默认 99 抢光券只能出现在全部里
//                        print_json($v,$card_list[$k]['available_quantity'],$card_list[$k]['available_count']);
                        //|| ($use_gift_card && $v['is_gift_card'] )
                        //已领取了就优先显示了
                        if(empty($v['is_can_receive'])){
                            $card_list[$k]['available_quantity'] = 0;
                        }

                        if($one_hava_get>0 || ($v['is_can_receive']==1  && !in_array($v['id'],$have_card_id_arr) &&
                            ($card_list[$k]['available_quantity']>0  || ($status == 0 && $card_list[$k]['available_count']==0) ) &&
                            $to_day>=$v['receive_start_date'] && $to_day<=$v['receive_end_date'])){
                            $have_card_id_arr[]=$v['id'];
                            $can_get_card_list[]= $card_list[$k];//这里才是可领取的

                        }

                    }

                    if(isset($v['select_obj'])){
                        if(in_array($v['select_obj'],[2,3]) && !$coupon_one && $v['push_type'] && !$vin_car_arr ){
                            $push_type_err[]=$v['push_type'];
                        }
                    }

                    if($can_get_card_list && $status < 2){
                        foreach ($can_get_card_list as $knb=>$can_get_card_item ){
                            if($can_get_card_list[$knb]['available_count'] > 0 && $can_get_card_list[$knb]['available_quantity']==0){
                                unset($can_get_card_list[$knb]);
                             }
                        }
                    }

                    $card_list[$k]['can_get'] =0;
                    $card_list[$k]['can_card'] =0;
//                    if($v['id']==21344022781002752){
////                        print_json($v,$change_car,$have_card_id_arr);
//
//                    }

                    //可领取+已领取并且可以用
                    // $v['available_count']>0   已抢光
                    //&&  !$change_car  可切换要显示
                    //&&  !$change_car
                    //card_list-- 可用+可领


                    if(($v['is_can_receive']==1  &&  $v['available_quantity']>0 && in_array($v['id'],$have_card_id_arr) )|| $v['can_use_card']>0 ){
                        if(!$change_car || $v['can_use_card']>0){
                            $card_list[$k]['can_card'] =1;
                        }
                        $card_list[$k]['card_date'] = $v['validity_date_start'] . '~' . $v['validity_date_end'];
                        if($v['available_quantity']>0 && $v['available_count']>0  && in_array($v['id'],$have_card_id_arr) && !$change_car){
                            $card_list[$k]['can_get'] =1;
                        }
                    }else{
                        unset($card_list[$k]);
                    }
                }
                if($all_change_car  && $all_art_work_vin){

                    $all_art_work='';
                    if($all_art_work_vin){
                        foreach (array_unique($all_art_work_vin) as $v){
                            $all_art_work.=sprintf(" %s %s ,",$vin_car_arr[$v]??'',$v);
                        }
                    }
                    $not_article_work = sprintf("您的其他车辆:%s 有可领优惠券，可切换车辆领取使用 ",trim($all_art_work,','));
                }


            }
            // 下面的 $goods_card_rule  包含了可用+可领卡券 需要切换车型的不能算入进去
            if($card_list){
                foreach ($card_list as $cc_k=>$cc_v) {
                    if($cc_v['can_card']!=1 && $cc_v['can_get']!=1){
                        unset($card_list[$cc_k]);
                    }
                }
            }


            $goods_card_rule = [];
//            print_json($card_list);
            if($card_list && empty($query_card_list)){
                foreach ($card_list as $goods_card) {
                    $goods_card_rule[$goods_card['commodity_set_id']][] =$goods_card;
                }
            }

            //商品详情往下传递 commodity_card_list  在detail里再判断可用卡券列表
            //get_card_list 是活动给的列表 商城用  all_card 做领券+已领取，判断价格之类的
//            print_json($can_get_card_list,2222);

            $data = [
                'get_card_list'=>$can_get_card_list,
                'all_card'=>$card_list,
                'card_rules'=>$card_rules,
                'goods_card_rule'=>$goods_card_rule,
                'not_article_work'=>$not_article_work,
                'push_type_err'=>$push_type_err
            ];
//            \redis($redis_name,$data,1);
        }

        return $data;
    }

    //可领取+已领取 之后判断sku是否适配
    //用了 goods_card_rule 就没有问题
    public function card_list_ok($card_rules,$goods_info,$card_list,$commodity_set_id=[]){
//        print_json($card_rules[$commodity_set_id]);
        if(!isset($card_rules[$commodity_set_id])){
            return  [];
        }
        $all_card_ids = $this->get_matching_card_ids($card_rules[$commodity_set_id],$goods_info);

        foreach ($card_list as $k=> $v){
            if(!in_array( $v['card_id'],$all_card_ids)){
                unset($card_list[$k]);
            }
        }
        return $card_list;
    }

    /**
     * @param $commodity_set_ids
     * @param $card_ids
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     * 获取卡券匹配规则
     */
    public function get_card_rule($commodity_set_ids,$card_ids=[],$sub_goods_id='',$goods_card_info=[],$channel_type='',$dd_dlr_code='',$user=[]){
        if(!$goods_card_info){
            $goods_card_info =  $this->get_goods_card($commodity_set_ids,$card_ids,$sub_goods_id,$channel_type,$dd_dlr_code,$user['car_series_id']??'');
        }
        $card_rule = [];
        if($goods_card_info){
            foreach ($goods_card_info as $k=> $v){
                if($v['set_sku_ids']){
                    $v['sku_id'] =  explode(',',$v['set_sku_ids']);
                }else{
                    $v['sku_id']='';
                }
                $goods_card_info[$k]['set_sku_arr'] = $v['sku_id'];
                $card_rule[$v['commodity_set_id']][] = $v;
//                $card_rule[] = $v;
//                if(isset($card_rule[$v['commodity_set_id']])){
//                    $card_rule[$v['commodity_set_id']]=  array_merge($card_rule[$v['commodity_set_id']],$v);
//                }else{
//                    $card_rule[$v['commodity_set_id']] = $v;
//                }
            }
        }
//        print_json($card_rule);
        return ['card_rule'=>$card_rule,'all_card_list'=>$goods_card_info];
    }

    /**
     * @param $card_rule
     * @param $goods_info
     * @return array
     * 商品-关联卡券都转化格式，判断返回商品关联卡券ID--sku纬度关联
     */
    public function get_matching_card_ids($card_rule, $goods_info) {
//        $card_rule = [
//            ['goods_id' => 5993, 'card_id' => 16813997978321920, 'sub_goods_id' => 5239, 'sku_id' => [149586, 149587, 149598, 149600, 149604, 149608,149609], 'group_card_type' => 2],
//            ['goods_id' => 5993, 'card_id' => 16813997978321920, 'sub_goods_id' => 5197, 'sku_id' => [], 'group_card_type' => 2],
//            ['goods_id' => 5993, 'card_id' => 16813522086298624, 'sub_goods_id' => 5197, 'sku_id' => [149503, 149504, 149505], 'group_card_type' => 1],
//            ['goods_id' => 5993, 'card_id' => 16813997978321920, 'sub_goods_id' => 5240, 'sku_id' => [36], 'group_card_type' => 2]
//        ];
//
//        $goods_info = [
//            ['goods_id' => 5993, 'sub_goods_id' => 5239, 'sku_id' => [149610, 149609], 'oil_type' => 0],
//            ['goods_id' => 5993, 'sub_goods_id' => 5197, 'sku_id' => [149501, 149503], 'oil_type' => 0],
//            ['goods_id' => 5993, 'sub_goods_id' => 5198, 'sku_id' => [33], 'oil_type' => 0],
//            ['goods_id' => 5993, 'sub_goods_id' => '', 'sku_id' => [34], 'oil_type' => 0],
//            ['goods_id' => 5993, 'sub_goods_id' => 5240, 'sku_id' => [35], 'oil_type' => 1]
//        ];
        // 将card_rule数组按card_id和group_card_type分组
        $grouped_rules = [];
        foreach ($card_rule as $rule) {
//            try{
//                $card_id = $rule['id'];
//            }catch (Exception $e){
//                print_json($rule);
//
//            }
            $card_id = $rule['id'];
            $group_card_type = $rule['group_card_type'];
            $rule['sku_id'] = $rule['set_sku_arr'];

            if (!isset($grouped_rules[$card_id])) {
                $grouped_rules[$card_id] = ['type' => $group_card_type, 'rules' => []];
            }
            $grouped_rules[$card_id]['rules'][] = $rule;
        }
        $matching_card_ids = [];

        // 遍历每个卡片规则组
        foreach ($grouped_rules as $card_id => $group) {
            $group_card_type = $group['type'];
            $rules = $group['rules'];

            if ($group_card_type == 1) {
                // group_card_type == 1 时，任意sub_goods_id匹配即可
                $found = false;

                foreach ($rules as $rule) {
                    $sub_goods_id = $rule['sub_goods_id'];
                    foreach ($goods_info as $info) {
                        // 检查sub_goods_id匹配或为空的情况
                        if ($info['sub_goods_id'] == $sub_goods_id || $info['sub_goods_id'] == '' || !$sub_goods_id) {
                            if (empty($rule['sku_id']) || !empty(array_intersect($rule['sku_id'], $info['sku_id']))) {
                                $matching_card_ids[] = $card_id;
                                $found = true;
                                break; // 跳出内层循环
                            }
                        }
                    }
                    if ($found) break; // 跳出外层循环
                }
            } elseif ($group_card_type == 2) {
                // group_card_type == 2 时，需要所有sub_goods_id都匹配
                $intersect_count = 0;
                foreach ($rules as $rule) {
                    $sub_goods_id = $rule['sub_goods_id'];
                    $rule_matched = false;
                    foreach ($goods_info as $info) {
                        // 如果 oil_type == 1，则不需要比对
                        if ($info['oil_type'] == 1 && $info['sub_goods_id'] == $sub_goods_id) {
                            $intersect_count++;
                            $rule_matched = true;
                            break;
                        }
                        // 检查sub_goods_id匹配
                        if ($info['sub_goods_id'] == $sub_goods_id) {
                            if (empty($rule['sku_id']) || !empty(array_intersect($rule['sku_id'], $info['sku_id']))) {
                                $intersect_count++;
                                $rule_matched = true;
                                break;
                            }
                        }
                    }
                    if (!$rule_matched && $sub_goods_id == '') {
                        // 处理 goods_info 中的 sub_goods_id 为空且 sku_id 匹配的情况
                        foreach ($goods_info as $info) {
                            if ($info['oil_type'] != 1 && !empty(array_intersect($rule['sku_id'], $info['sku_id']))) {
                                $intersect_count++;
                                break;
                            }
                        }
                    }
                }
                if ($intersect_count == count($rules)) {
                    $matching_card_ids[] = $card_id;
                }
            }
        }

        return $matching_card_ids;
    }


    //卡券+活动互斥组合
    public function act_card_hc($new_cards)
    {
// 初始化act数组
//        $cards = [
//            ['card_id' => '1', 'card_value' => '10', 'can_use' => '', 'can_no_use' => 'all', 'act_id' => '91', 'can_no_with' => '92,94'],
//            ['card_id' => '2', 'card_value' => '20', 'can_use' => '3,4', 'can_no_use' => '', 'act_id' => '91', 'can_no_with' => '92,94'],
//            ['card_id' => '3', 'card_value' => '30', 'can_use' => '2,4', 'can_no_use' => '', 'act_id' => '91', 'can_no_with' => '92,94'],
//            ['card_id' => '4', 'card_value' => '40', 'can_use' => '2,3,9', 'can_no_use' => '', 'act_id' => '92', 'can_no_with' => '91'],
//            ['card_id' => '5', 'card_value' => '50', 'can_use' => '', 'can_no_use' => '6,8', 'act_id' => '93', 'can_no_with' => ''],
//            ['card_id' => '6', 'card_value' => '60', 'can_use' => '', 'can_no_use' => '5,7', 'act_id' => '93', 'can_no_with' => ''],
//            ['card_id' => '7', 'card_value' => '70', 'can_use' => '', 'can_no_use' => '6', 'act_id' => '94', 'can_no_with' => '91'],
//            ['card_id' => '8', 'card_value' => '80', 'can_use' => '', 'can_no_use' => '5', 'act_id' => '95', 'can_no_with' => 'all'],
//            ['card_id' => '9', 'card_value' => '90', 'can_use' => '4', 'can_no_use' => '', 'act_id' => '95', 'can_no_with' => 'all']
//        ];
        $cards=[];
        $cards_arr =[];
        $can_no_use_arr = [];
        $can_use_arr = [];
        $can_no_with_arr = [];
//        print_json($new_cards);
        foreach ($new_cards as $v){
            $v['card_value'] =  $v['value'];
            $v['card_id'] =  $v['id'];
            if($v['can_use']!=='all'){
                if($v['can_use']){
                    $v['can_use'] = explode(',', $v['can_use']);
                    foreach ($v['can_use'] as $vv){
                        $can_use_arr[$vv][]=$v['act_card_id'];
//                        if(isset($can_use_arr[$vv])){
//                            try{
//                                $can_use_arr[$vv][]=$v['act_card_id'];
//
//                            }catch (\Exception $e){
//                                print_json($can_use_arr[$vv],$v['act_card_id']);
//                            }
//                        }else{
//                            $can_use_arr[$vv]=$v['act_card_id'];
//                        }
                    }
                }else{
                    $v['can_use'] = [];
                }
            }

            if($v['can_no_use']!=='all'){
                if($v['can_no_use']){
                    $v['can_no_use'] = explode(',', $v['can_no_use']);

                    foreach ($v['can_no_use'] as $vv){
                        $can_no_use_arr[$vv][]=$v['act_card_id'];
//                        if(isset($can_no_use_arr[$vv])){
//                            $can_no_use_arr[$vv][]=$v['act_card_id'];
//                        }else{
//                            $can_no_use_arr[$vv]=$v['act_card_id'];
//                        }
                    }
                }else{
                    $v['can_no_use'] = [];
                }
            }

            if($v['can_no_with']!=='all'){
                if($v['can_no_with']){
                    $v['can_no_with'] = explode(',', $v['can_no_with']);
                    foreach ($v['can_no_with'] as $vv){
                        $can_no_with_arr[$vv][]=$v['act_id'];
//                        if(isset($can_no_with_arr[$vv])){
//                            $can_no_with_arr[$vv][]=$v['act_id'];
//                        }else{
//                            $can_no_with_arr[$vv]=$v['act_id'];
//                        }
                    }
                }else{
                    $v['can_no_with'] = [];
                }
            }
            if(!isset($v['card_code'])){
                $v['card_code'] = $v['id'];
            }

            $v['no_rel_value']= $v['value'];
            if($v['is_gift_card']==1){
                $v['no_rel_value'] = 999999999;//如果是买赠券--价格最高
            }

            $cards[]=[
                'id'=>$v['id'],
                'card_id'=>$v['id'],
                'can_use'=>$v['can_use'],
                'can_no_use'=>$v['can_no_use'],
                'can_no_with'=>$v['can_no_with'],//不可用活动
                'value'=>$v['value'],
                'card_value'=>$v['value'],
                'no_rel_value'=>$v['no_rel_value'],//配置卡券==gift时候给特殊值
//                'no_rel_value'=>$v['value'],
                'act_id'=>$v['act_id'],
                'act_card_id'=>$v['act_card_id'],
                'card_code'=>$v['card_code'],
            ];
            $cards_arr[$v['card_code']] = $v;
        }


        foreach ($cards as &$new_v){

            if(isset($can_use_arr[$new_v['act_card_id']])){
                if($new_v['can_use'] && $new_v['can_use']!=='all'){
                    $new_v['can_use'] =array_unique(array_merge($new_v['can_use'],$can_use_arr[$new_v['act_card_id']]));
                }elseif(!$new_v['can_use']){
                    $new_v['can_use']=array_unique(array_merge($new_v['can_use'],$can_use_arr[$new_v['act_card_id']]));
                }
            }

            if(isset($can_no_use_arr[$new_v['act_card_id']])){
                if($new_v['can_no_use'] && $new_v['can_no_use']!=='all'){
                    $new_v['can_no_use'] =array_unique(array_merge($new_v['can_no_use'],$can_no_use_arr[$new_v['act_card_id']]));
                }elseif(!$new_v['can_no_use']){
                    $new_v['can_no_use'] =array_unique(array_merge($new_v['can_no_use'],$can_no_use_arr[$new_v['act_card_id']]));
                }
            }

            if(isset($can_no_with_arr[$new_v['act_id']])){
                if($new_v['can_no_with'] && $new_v['can_no_with']!=='all'){
                    $new_v['can_no_with'] =array_unique(array_merge($new_v['can_no_with'],$can_no_with_arr[$new_v['act_id']]));
                }elseif(!$new_v['can_no_with']){
                    $new_v['can_no_with'] =array_unique(array_merge($new_v['can_no_with'],$can_no_with_arr[$new_v['act_id']]));
                }
            }

        }

        // 预处理卡券数组
        foreach ($cards as &$card) {
//            $card['can_use'] = $card['can_use'] === 'all' ? 'all' : ($card['can_use']?explode(',', $card['can_use']) : []);
//            $card['can_no_use'] = $card['can_no_use'] === 'all' ? 'all' : ($card['can_no_use'] ? explode(',', $card['can_no_use']) : []);
//            $card['can_no_with'] = $card['can_no_with'] === 'all' ? 'all' : ($card['can_no_with'] ? explode(',', $card['can_no_with']) : []);



            $card['card_value'] =  $card['value'];
            $card['card_id'] =  $card['id'];


            if($card['can_use']!='all' && $card['can_use']){
                $card['can_use']=array_merge($card['can_use'],[(string)$card['act_card_id']]);
            }


        }
        Logger::error('act_card_hc:',json_encode($cards));
//        print_json($cards);
// 递归函数来生成所有可能的组合

// 生成所有有效组合
        $validCombinations = [];
        $this->generateCombinationsRecursively($cards, 0, [], [], $validCombinations);
// 计算每个组合的总价值并排序
        $sortedCombinations = [];
//        die(333);
//        print_json($validCombinations);
        foreach ($validCombinations as $combo) {
            $comboIds = array_map(function($card) { return json_decode($card, true)['card_code']; }, $combo);
            $totalValue = array_sum(array_map(function($card) { return json_decode($card, true)['card_value']; }, $combo));
            $notRelValue = array_sum(array_map(function($card) { return json_decode($card, true)['no_rel_value']; }, $combo));
            $combo_arr = [];
            foreach ($comboIds as $co_v){
//                $one_co_v =  json_decode($co_v,true);
//                $combo_arr[]= $cards_arr[$one_co_v['card_code']];
                $combo_arr[]= $cards_arr[$co_v];
            }

            $sortedCombinations[] = ['combo' => $combo_arr, 'total_value' => $totalValue,'not_rel_value'=>$notRelValue];
        }
//        foreach ($validCombinations as $comboIds) {
//            $comboIds = array_map(function($card) { return json_decode($card, true)['card_id']; }, $combo);
//            $totalValue = array_sum(array_map(function($card) { return json_decode($card, true)['card_value']; }, $combo));
//
//            $sortedCombinations[] = ['combo' => $comboIds, 'total_value' => $totalValue];
//
//
//            $totalValue = array_sum(array_map(function($id) use ($cards) {
//                foreach ($cards as $card) {
//                    if ($card['act_card_id'] == $id) return $card['card_value'];
//                }
//                return 0;
//            }, $comboIds));
//            $combo_arr = [];
//            foreach ($comboIds as $co_v){
//                $one_co_v =  json_decode($co_v,true);
//                $combo_arr[]= $cards_arr[$one_co_v['card_code']];
//            }
//
//
//            $sortedCombinations[] = ['combo' => $combo_arr, 'total_value' => $totalValue];
//        }

        usort($sortedCombinations, function($a, $b) {
            return $b['not_rel_value'] - $a['not_rel_value'];
//            return $b['total_value'] - $a['total_value'];
        });
//        print_json($sortedCombinations);
// 输出结果
        return ['best'=>$sortedCombinations[0],'list'=>$sortedCombinations];
//        echo "Total combinations: " . count($sortedCombinations) . "\n";
//        foreach ($sortedCombinations as $comboData) {
//            $comboIds = array_map(function($card) { return $card['card_id']; }, $comboData['combo']);
//            echo "Combination: " . implode(", ", $comboIds) . " | Total Value: " . $comboData['total_value'] . "\n";
//        }
    }

    private function generateCombinationsRecursively($cards, $start = 0, $currentCombo = [], $currentActs = [], &$result = []) {
//        print_json($cards);
        for ($i = $start; $i < count($cards); $i++) {
            $newCombo = array_merge($currentCombo, [$cards[$i]]);
            $newActs = array_merge($currentActs, [$cards[$i]['act_id']]);

            // 检查组合是否有效
            if ($this->isValidCombination($newCombo, $newActs)) {
//                $comboIds = array_map(function($card) { return $card['act_card_id']; }, $newCombo);
//                sort($comboIds);
//                $comboKey = implode(',', $comboIds);

                // 获取组合的标识（使用完整的卡片数据，而不是仅基于 card_id）
                $comboKey = array_map(function($card) { return json_encode($card); }, $newCombo);
//            echo $i.'---';
//            echo  json_encode($comboKey);
//            echo "\r\n";
                // 检查当前组合的所有子集是否已存在
                $isSubset = false;
                foreach ($result as $existingCombo) {

                    if (array_diff($comboKey, $existingCombo) === []) {
                        //暂时去掉这里，不然 比如  321的时候21就不能成组了，那么这个时候选了2，1也不能用了

//                        print_json($comboKey,$existingCombo);
//                        $isSubset = true;
//                        break;
                    }
                }

                if (!$isSubset) {
                    // 移除任何已存在的组合是新组合的子集
                    foreach ($result as $key => $existingCombo) {
//                        echo $i.'---';
//                        echo  json_encode($comboKey);
//                        echo "\r\n";
                        if (array_diff($existingCombo, $comboKey) === []) {
                            unset($result[$key]);
                        }
                    }

                    // 添加新组合
                    $result[] = $comboKey;

                    // 递归生成更大的组合

                    // 递归生成更大的组合
                    $this->generateCombinationsRecursively($cards, $i + 1, $newCombo, $newActs, $result);
                }
            }
        }
    }

// 检查组合是否有效
    private function isValidCombination($combo, $currentActs) {
        $comboIds = array_map(function($card) { return $card['act_card_id']; }, $combo);

        foreach ($combo as $card) {
            // 检查不可共用的卡券
            if ($card['can_no_use'] === 'all') {
                if (count($combo) > 1) {
                    return false;
                }
            } else {
                foreach ($card['can_no_use'] as $noUseId) {
                    if (in_array($noUseId, $comboIds)) {
                        return false;
                    }
                }
            }

            // 检查必须共用的卡券
            if (!empty($card['can_use']) && $card['can_use']!=='all') {
                foreach ($comboIds as $id) {
                    if ($id !== $card['card_id'] && !in_array($id, $card['can_use'])) {
                        return false;
                    }
                }
            }

            // 检查act层次的约束
            if ($card['can_no_with'] === 'all') {
                if (count($currentActs) > 1) {
                    return false;
                }
            } else {
                foreach ($card['can_no_with'] as $noWithActId) {
                    if (in_array($noWithActId, $currentActs)) {
                        return false;
                    }
                }
            }
        }

        return true;
    }


    public function dataFYmdpoint($date_time){
        return date('Y.m.d', strtotime($date_time));
    }

    /**
     * @param $goods_dis 优惠金额
     * @param $dlr_price 网点价
     * @param $price 单价
     * @param $card_price 卡券面值
     * @param $set_rule_type 1固定金额2百分比
     * @param $set_rule_value 金额
     * @param $sett_standard 1-商城商品单价 2-实际优惠金额 3-卡券面值 4-网点价
     * @param $count 数量
     * @return array
     */
    public function act_card_set_rule($goods_dis,$dlr_price,$price,$card_price,$set_rule_type,$set_rule_value,$sett_standard,$count)
    {
        if($sett_standard==1){
            $dis_value = $price;
        }elseif ($sett_standard==2){
            $dis_value = $goods_dis;
        }elseif ($sett_standard==3){
            $dis_value = $card_price;
        }elseif ($sett_standard==4){
            $dis_value = $dlr_price;
        }else{
            $dis_value = $goods_dis;
        }

        if ($set_rule_type == 1) {
            $settlement_value = $set_rule_value;
            $rule_arr =  [1=>$set_rule_value];
        } else {
            $settlement_value = round(($dis_value) * ($set_rule_value / 100), 2);
            $rule_arr =  [2=>$set_rule_value];

        }
        $sett_value   = bcmul($settlement_value,$count,2);

        $re_data = [
            'rule'=>json_encode($rule_arr),
            'sett_value'=>$sett_value
        ];
        return $re_data;


    }


    /**
     * 获取活动关联主品-sku_code,备件分类code
     * @param $act_id
     * @param $sku_code
     * @param $sku_class_code
     * @return bool|\PDOStatement|string|\think\Collection
     */
    public function gift_card_rule($act_id,$sku_code='',$sku_class_code='')
    {
        $act_sku_model =  new DbActivityAssSku();
        $where = ['activity_id'=>['in',$act_id]];
        if($sku_code){
            $where['sku_code'] = ['in',$sku_code];
        }
        if($sku_class_code){
            $where['sku_class_code'] = ['in',$sku_class_code];
        }
        $gift_card_sku_arr = [];
        $gift_card_sku_class_arr = [];
        $gift_card_rule_list =  $act_sku_model->getList(['where'=>$where,'field'=>"activity_id,sku_code,sku_class_code,sku_type"]);
//        print_json($gift_card_rule_list,$act_sku_model->getLastSql());
        if($gift_card_rule_list){

            foreach ($gift_card_rule_list as $v){
                if($v['sku_code']){
                    $gift_card_sku_arr[]=$v['sku_code'];
                }
                if($v['sku_class_code']){
                    $gift_card_sku_class_arr[]=$v['sku_class_code'];

                }
            }

        }
        $data  = ['list'=>$gift_card_rule_list,'gift_card_sku_arr'=>$gift_card_sku_arr,'gift_card_sku_class_arr'=>$gift_card_sku_class_arr];
        return $data;
    }




    /**
     * //购物车使用了赠品券的赠品数量
     * //如果卡券状态不是未激活 要取消那个卡券
     * @param $card_id
     * @param $user
     * @return int|mixed
     */
    public function cart_gift_card($card_id,$user,$no_cart_id= [])
    {
        $cart_model =  new BuShoppingCart();
        $where = ['gift_card_id'=>$card_id,'user_id'=>$user['id'],'is_enable'=>1,'brand'=>$user['brand']];
        if($no_cart_id){
            $where['id'] =['not in',$no_cart_id];
        }
        $list =  $cart_model->getList(['where'=>$where]);
        $gift_count = 0;
        if($list){
            foreach ($list as $v){
                if($v['gift_c_json']){
                    $gift_arr = json_decode($v['gift_c_json'],true);
                    foreach ($gift_arr as $r_v){
                        $gift_count+=$r_v['count'];
                    }
                }

            }
        }
        return $gift_count;
    }

    public function gift_card_goods_list()
    {
        $com_card_model  =  new DbCommodityCard();

    }

    public function getUserGiftCardList($user,$vinList=[],$brandId=1)
    {
        $time       = date('Y-m-d H:i:s');
        $card_r_model =  new BuCardReceiveRecord();
        //按照产品的说法，这里要判断卡券跟活动都必须是在有效期内
        $card_r_where = [
            'b.is_gift_card'=>1,'a.activity_id'=>['>',0],
            'a.validity_date_start'=>['<=',$time],
            'a.validity_date_end'=>['>=',$time],
            'act.activity_time_start'=>['<=',$time],
            'act.activity_time_end'=>['>=',$time],
            'a.status'=>7,
            'b.brand_id'=>$brandId,
        ];

        if($vinList){
            $vin_list_str = implode("','",$vinList);
//                or a.user_id='%s'
            $card_r_where[]=['exp',sprintf("(a.receive_vin in ('%s') and (a.user_id='%s' or a.user_id=0)) || ((a.receive_vin='' || a.receive_vin is null) and a.user_id='%s')",$vin_list_str,$user['id'],$user['id'])];
        }else{
            $card_r_where[]=['exp',sprintf("((a.receive_vin='' || a.receive_vin is null) and a.user_id='%s')",$user['id'])];

//                $card_r_where['a.user_id'] =  $user_id;
        }
        $card_r_filed = "a.card_code,a.card_id,a.status,a.is_enable,a.activity_id,a.user_id,a.receive_vin";
        $card_r =  $card_r_model->alias('a')->join('t_db_card b ','a.card_id=b.id')
            ->join('t_db_activity act ','act.activity_id = a.activity_id  and act.activity_status_flag=1')
            ->where($card_r_where)->field($card_r_filed)->select();
        if($card_r){
            $user['card_r_gift'] = $card_r;
            $user['card_r_gift_wjh'] = [];
            foreach ($card_r as $card_r_v){
                $user['card_r_gift_wjh'][]=$card_r_v;
                $user['card_r_gift_card_code'][$card_r_v['card_id']][]=$card_r_v['card_code'];
                if($card_r_v['receive_vin']){
                    $user['card_r_gift_vin_user'][$card_r_v['receive_vin']][]=$card_r_v;
                }else{
                    $user['card_r_gift_vin_user'][$card_r_v['user_id']][]=$card_r_v;
                }
            }

        }
        return $user;
    }

    /**
     * 判断是否显示切换车辆按钮
     * @param array $card_info 卡券信息
     * @param array $user 用户信息
     * @return bool
     */
    private function shouldShowSwitchVehicle($card_info, $user)
    {
        // 如果券的VIN与用户当前默认车VIN不一致，显示切换车辆按钮
        $current_vin = $user['vin'] ?? '';
        $card_vin = $card_info['receive_vin'] ?? '';

        return !empty($card_vin) && !empty($current_vin) && $card_vin !== $current_vin;
    }

    /**
     * 解析激活场景配置
     * @param string $scene_list 激活场景列表
     * @return array
     */
    private function parseActivationScenes($scene_list)
    {
        if (empty($scene_list)) {
            return [];
        }

        $scenes = explode(',', $scene_list);
        $result = [];

        foreach ($scenes as $scene) {
            $scene = trim($scene);
            if (empty($scene)) {
                continue;
            }

            $scene_info = [
                'scene_code' => $scene,
                'scene_name' => $this->getSceneName($scene),
                'jump_type' => $this->getSceneJumpType($scene)
            ];

            $result[] = $scene_info;
        }

        return $result;
    }

    /**
     * 获取场景名称
     * @param string $scene_code 场景代码
     * @return string
     */
    private function getSceneName($scene_code)
    {
        $scene_names = [
            'KeHuZaiShangChengXiaDan' => '订单支付',
            'ShangChengHeXiao' => '订单核销',
            // 可以根据需要添加更多场景
        ];

        return $scene_names[$scene_code] ?? '立即购买';
    }

    /**
     * 获取场景跳转类型
     * @param string $scene_code 场景代码
     * @return string
     */
    private function getSceneJumpType($scene_code)
    {
        if (strpos($scene_code, 'KeHuZaiShangChengXiaDan') !== false) {
            return 'order_payment';
        } elseif (strpos($scene_code, 'ShangChengHeXiao') !== false) {
            return 'order_consume';
        } else {
            return 'immediate_purchase';
        }
    }

}


