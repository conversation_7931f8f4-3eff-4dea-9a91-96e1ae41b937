<?php

/**
 * 车生活售后单信息
 * @author: zxtdcyy
 * @time: 2021-06-10
 */

namespace app\common\model\db;

use app\common\model\bu\BuOrderCommodity;
use app\common\model\bu\BuOrderSettlement;
use app\common\model\Common;
use think\Db;


/**
 * Class DbAfterSaleOrders
 * @package app\common\model\db
 */
class DbAfterSaleOrders extends Common
{
    /**
     * @var string
     */
    protected $table = 't_db_after_sale_orders';
    /**
     * @var string
     */
    protected $pk = 'id';

    static public $after_sale_status = [
        1  => '退款待审核',
        2  => '已取消',
        3  => '退款拒绝',
        4  => '退款中',
        5  => '退款失败',
        6  => '已退款',
        7  => '退货待审核',
        8  => '退货拒绝',
        9  => '退货待收',
        10 => '换货待审核',
        11 => '换货拒绝',
        12 => '换货待收',
        13 => '换货发货',
        14 => '换货收货',
    ];

    static public $afss_type = [
        'only_refund' => [1 => 1, 2 => 2, 3 => 3, 4 => 4, 5 => 5, 6 => 6],
        'back_pro'    => [7 => 7, 8 => 8, 9 => 9],
        'chg_pro'     => [10 => 10, 11 => 11, 12 => 12, 13 => 13, 14 => 14],
        'after_jd' =>[1=>'1-2',2=>'0',3=>'1-9',4=>'5',5=>'1-9',6=>'8',7=>'2-2',8=>'2-9',9=>'2-3',10=>'3-2',11=>'3-9',12=>'3-3',13=>'3-5',14=>'3-8'],//1-提交申请，0-取消，1-0退款拒绝，1-5退货审核中，1-9退款失败，1-8已退款（退货完成），1-2退款待审核，1-0退货拒绝，1-4换货待收，2-2换货审核中,2-0换货拒绝，2-4换货待收，2-5已发货，2-6换货完成  少一个退货审核等待提供物流
    ];
//退款：
//提交申请，审核中1-2，退款中1-5，退款完成1-8
//提交申请，已取消1-0  0
//提交申请，审核中，退款拒绝1-9
//
//退货：
//提交申请，审核中2-2，提交物流信息2-3，退货待收2-4=9+物流，退款中2-5，退款完成2-8
//提交申请，已取消2-0  0
//提交申请，审核中，已拒绝2-9
//
//换货：
//提交申请，审核中3-2，提交物流信息3-3 ，换货待收3-4=12+物流，已发货3-5，换货完成3-8
//提交申请，已取消3-0 0
//提交申请，审核中，已拒绝3-9
//1:退款待审核,2:已取消,3:退款拒绝,4:退款中,5:退款失败,6:已退款,7:退货待审核,8:退货拒绝,9:退货待收,10:换货待审核,11:换货拒绝,12:换货待收,13:换货发货,14:换货收货

    //对接营销平台
    static public $afss_status_ref = [
        1 => 100, 2 => 600, 3 => 700, 6 => 400,
        7 => 100, 8 => 700, 10 => 100, 11 => 700, 14 => 500
    ];

    const ONLY_REFUND = 1;
    const BACK_PRO    = 2;
    const CHG_PRO     = 3;

    static public $afss_type_ref = [
        1 => 3,#cpt confirm
        2 => 0,
        3 => 1,
    ];

    static public $afs_type = [
        1 => '仅退款',
        2 => '退货',
        3 => '换货',
    ];

    public function getLists($params)
    {
        if (!isset($params['pagesize'])) {
            $params['pagesize'] = $this->_pageSzie;
        }
        $params = $this->_checkParams($params);
        return $this->alias('a')
            ->join("t_bu_order b", "a.order_id=b.id")
            ->join("t_db_user c", "c.id=b.user_id")
            ->join('t_bu_order_more_card_point g', 'g.order_code = b.order_code and g.point_type=1 and g.is_enable=1', 'left')
            ->join('t_bu_order_more_card_point h', 'h.order_code = b.order_code and h.point_type=2 and h.is_enable=1', 'left')
            ->with(['afterOrderCommodities.afterJdWarehouse'])
            ->join('t_db_jd_warehouse jdwr','jdwr.id=a.jd_warehouse_send_id and jdwr.is_enable=1 ','left')
            ->field($params['field'])
            ->where($params['where'])
            ->order($params['order'])
            ->group($params['group'])
            ->paginate($params['pagesize'], false, array('query' => $params['query']));
    }

    public function getOrderLimit($params)
    {
        $params = $this->_checkParams($params);

        $list = $this->alias('a')
            ->field($params['field'])
            ->join("t_bu_order b", "a.order_id=b.id")
            ->join('t_bu_order_commodity oc', 'oc.order_code=b.order_code')
            ->join("t_db_user c", "c.id=b.user_id")
            ->join('t_db_commodity dc', 'oc.commodity_id=dc.id')
            ->join('t_db_commodity_set_sku d', 'oc.sku_id = d.id')
//            ->join('t_db_after_sale_orders f', 'f.order_id = b.id')
            ->join('t_bu_order_more_card_point g', 'g.order_code = b.order_code and g.point_type=1 and g.is_enable=1', 'left')
            ->join('t_bu_order_more_card_point h', 'h.order_code = b.order_code and h.point_type=2 and h.is_enable=1', 'left')
            ->with(['afterOrderCommodities.afterJdWarehouse'])
            ->join('t_db_jd_warehouse jdwr','jdwr.id=a.jd_warehouse_send_id and jdwr.is_enable=1 ','left')
            ->where($params['where'])
            ->where($params['where_or'])
            ->order($params['order'])
            ->group($params['group'])
            ->select();

        return $list;
    }

    /**
     * 售后订单列表
     * @param $params
     */
    public function getAfterOrderList($params){

        $list = $this->alias('a')
            ->field($params['field'])
            ->join('t_bu_order b', 'a.order_id=b.id')
            ->where($params['where'])
            ->order($params['order'])
            ->paginate($params['pagesize'], false, array('query' => $params['query']))
            ->toArray();
        $order_settlement_model = new BuOrderSettlement();
        foreach ($list['data'] as $k => $v){
            // 手机号脱敏
            $list['data'][$k]['phone'] = $v['phone'] ? substr_replace($v['phone'],'****',3,4) : '';
            if (empty($v['dd_dlr_code'])) {
                $column = $order_settlement_model->getColumn(['where' => ['intention_account_id' => $v['dlr_code']], 'column' => 'cashier_settlement_no']);
            }
            $list['data'][$k]['cashier_settlement_no'] = $column[0] ?? '-';
        }

        $params['where']['a.afs_status'] = 1; // 待审核
        $wait_count = $this
            ->alias('a')
            ->join('t_bu_order b', 'a.order_id=b.id')
            ->where($params['where'])
            ->count();
        $params['where']['a.afs_status'] = 4; // 待退款
        $in_count = $this
            ->alias('a')
            ->join('t_bu_order b', 'a.order_id=b.id')
            ->where($params['where'])
            ->count();

        return ['total'=>$list['total'],'per_page'=>$list['per_page'],'current_page'=>$list['current_page'],'after_sale_list'=>$list['data'],'wait_count'=>$wait_count,'in_count'=>$in_count];
    }

    /**
     * 售后订单详情
     * @param $params
     */
    public function getAfterOrderDetail($params){
        $info = $this
            ->alias('a')
            ->join('t_bu_order b', 'a.order_id=b.id')
            ->field($params['field'])
            ->where($params['where'])
            ->find();
        return $info;
    }

    /**
     * 修改售后订单
     * @param $params
     */
    public function updateAfterOrder($params){
        $re = $this
            ->where($params['where'])
            ->update($params['data']);
        return $re;
    }


    /**
     * 订单商品 订单号关联 1:N
     * @return \think\model\relation\HasMany
     */
    public function orderCommodities()
    {
        return $this->hasMany(BuOrderCommodity::class, 'order_code', 'order_code');
    }


    /**
     * 售后订单商品表
     * @return \think\model\relation\HasMany
     */
    public function afterOrderCommodities()
    {
        return $this->hasMany(DbAfterSaleOrderCommodity::class, 'afs_id', 'id');
    }
}
