<?php

namespace app\common\port\connectors;

use app\common\port\interfaces\RequestOptions;
use G<PERSON><PERSON><PERSON>ttp\Client;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Middleware;
use think\Debug;
use think\Log;
use tool\Logger;
use function GuzzleHttp\Psr7\str;

abstract class Connect
{
    protected $key;

    protected $config;

    protected $client;

    protected $access_token;

    protected $response_status_code;

    protected function createToken(): array
    {
        return [];
    }

    /**
     * @param string $key
     * @param $parameters
     * @return static
     */
    public static function create(string $key, $parameters = [])
    {

        $config          = config("port." . $key);
        $client          = new Client((array)$config['client']);
        $connect         = new static(...array_merge($parameters, [$config, $client, $key]));
        $connect->key    = $key;
        $connect->config = $config;
        $connect->client = $client;
        return $connect;
    }

    public function token()
    {
        if ($this->access_token) {
            return $this->access_token;
        }

        $key                = 'port_' . $this->key . '_token';
        $this->access_token = redis($key);

        if (empty($this->access_token)) {
            $data               = $this->createToken();
            $this->access_token = $data['token'];
            $rs                 = redis($key, $data['token'], $data['timeout']);
            if (!$rs) {
                return $this->token();
            }
        }

        return $this->access_token;
    }

    public function refreshToken()
    {
        $this->access_token = null;
        redis('port_' . $this->key . '_token', null);
    }

    protected function get($uri, $params = [], $options = [], $extra = [])
    {
        return $this->http('get', $uri, array_merge(['query' => $params], $options), $extra);
    }

    protected function put($uri, $params = [], $options = [])
    {
        return $this->http('put', $uri, array_merge(['query' => $params], $options));
    }

    protected function putJson($uri, $params = [], $options = [])
    {
        return $this->http('put', $uri, array_merge(['json' => $params], $options));
    }

    protected function delete($uri, $params = [], $options = [])
    {
        return $this->http('delete', $uri, array_merge(['query' => $params], $options));
    }

    protected function post($uri, $params = [], $options = [], $extra = [])
    {
        return $this->http('post', $uri, array_merge(['query' => $params], $options), $extra);
    }

    protected function postJson($uri, $params = [], $options = [], $extra = [])
    {
        return $this->http('post', $uri, array_merge(['json' => $params], $options), $extra);
    }

    public function send(string $method, string $url, $data, array $options = [])
    {
        $method      = strtoupper($method);
        $contentType = $options['headers']['Content-Type'] ?? ($options['headers']['content-type'] ?? '');

        if (in_array($method, ['GET', 'DELETE']) && !empty($data)) {
            $options['query'] = $data;
        } elseif (in_array($method, ['POST', 'PUT', 'PATCH'])) {
            // 根据不同的请求头类型，封装请求体
            switch (strtolower($contentType)) {
                case 'application/json':
                    $options['json'] = $data;
                    break;
                case 'application/x-www-form-urlencoded':
                    $options['form_params'] = $data;
                    break;
                case 'multipart/form-data':
                    // 修复存在头部无法覆盖问题: multipart/form-data; boundary=eefcf607cca91bfc3d6b28480bcec8de051d6a24
                    unset($options['headers']['Content-Type']);
                    unset($options['headers']['content-type']);
                    $options['multipart'] = $data;
                    break;
                default:
                    $options['body'] = $data;
                    break;
            }

        }

        return $this->http($method, $url, $options);
    }

    /**
     * @param $method
     * @param $uri
     * @param array $options
     * @param array $extra
     * @return mixed|string
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    protected function http($method, $uri, $options = [], $extra = [])
    {

        $retry = 1;
        beginning:
        if ($this instanceof RequestOptions) {
            $options = call_user_func([$this, 'requestOptions'], $options, $uri);
        }

        $requestBody = null;
        $request     = null;

        $redirect_cbb = true;
        if (!empty($options['auth_type']) || empty(config('market_transfer'))) {
            $redirect_cbb = false;
        }

        $cbb_config = config('port.cbb');
        $json       = [];

        try {

            if (empty($redirect_cbb)) {

                $tapMiddleware = Middleware::tap(function ($r, $options) use (&$request, &$requestBody) {
                    $requestBody = $r;
                    $request     = ['method'  => $r->getMethod(),
                                    'url'     => (string)$r->getUri(),
                                    'body'    => json_decode($r->getBody()->getContents(), true),
                                    'headers' => $r->getHeaders(),
                                    'options' => array_intersect_key($options, array_flip(['timeout', 'synchronous', 'allow_redirects', 'verify', 'cookies', 'decode_content']))];
                });
                if (!empty($this->config['mark_uuid'])) {
                    $rand_str_id = get_rand_str(32);
                    if (strpos($uri, '?') !== false) {
                        $uri = $uri . '&digital_trace_uuid=' . $rand_str_id;
                    } else {
                        $uri = $uri . '?digital_trace_uuid=' . $rand_str_id;
                    }
                }

                $options['headers']['x-feat'] = $cbb_config['x-feat'];
                $json = [
                    'url'          => $uri,
                    'method'       => $method,
                    "request_body" => json_encode($options)
                ];
                $api_start_at = microtime(true);
                try {
                    $response     = $this->client->request($method, $uri, array_merge($options, ['handler' => $tapMiddleware($this->client->getConfig('handler'))]));

                    $context = [
                        'ms_start' => $api_start_at,
                        'ms_end'   => microtime(true),
                        'ms_js'   => number_format(microtime(true)-$api_start_at, 10,  '.',  ''),
                        'url'      => $uri,
                        'host'      => $this->client->getConfig('base_uri')->getHost(),
                        'method'   => $method,
                        'body'   => $json['request_body']
                    ];

                    $response->getBody()->rewind();
                    Logger::debug('api execute_time:', $context);
                    trace($context, 'debug');
                }catch (\Exception $e) {
                    $response =[];
                    trace(['js' => $json, 'msg' => $e->getMessage(),'host'      => $this->client->getConfig('base_uri')->getHost(), 'ms_js' => number_format(microtime(true) - $api_start_at, 10)], 'debug');
                    Logger::error('conn-request-error',['js'=>$json,'msg'=>$e->getMessage(),'host'      => $this->client->getConfig('base_uri')->getHost(),'ms_js'   => number_format(microtime(true)-$api_start_at, 10,  '.',  '')]);
                    return  [];
                }
            } else {
                $bear_token = getCbbToken();

                //固定请求cbb java open
                $transfer_url = $cbb_config['client']['base_uri'] . $cbb_config['client']['transfer'];

                $url = '';
                if (in_array($method, ['get', 'delete'])) {
                    $req_arr = [];
                    foreach ($options['query'] as $k => $v) {
                        if (is_bool($v)) {
                            $v = empty($v) ? 'false' : 'true';
                        }
                        $req_arr[] = $k . '=' . $v;
                    }

                    if (!empty($req_arr)) {
                        $url = '?' . implode('&', $req_arr);
                    }
                } else {

                    if (!empty($options['query'])) {
                        $options = $options['query'];
                    } else if (!empty($options['json'])) {
                        $options = $options['json'];
                    } else {
                        $options = $options;
                    }
                }

                #追加req_uuid用于标识,便于追踪
                if (!empty($this->config['mark_uuid'])) {
                    $rand_str_id = get_rand_str(32);
                    if (strpos($url, '?') !== false) {
                        $url = $url . '&digital_trace_uuid=' . $rand_str_id;
                    } else {
                        $url = '?digital_trace_uuid=' . $rand_str_id;
                    }
                }

                $json = [
                    'url'             => $this->config['client']['base_uri'] . $uri . $url,
                    'method'          => $method,
                    'request_headers' => [
                        "x-feat" => $cbb_config['x-feat'],
                    ],
                    "request_body"    => json_encode($options)
                ];

                $cbbClient = new Client((array)$cbb_config['client']);

                $api_start_at = microtime(true);
                $response     = $cbbClient->request('post', $transfer_url, [
                    'json'    => $json,
                    'headers' => [
                        'Authorization' => 'Bearer ' . $bear_token,
                        'x-feat'        => $cbb_config['x-feat']
                    ]
                ]);

                Logger::debug('api execute_time:', [
                    'ms_start' => $api_start_at,
                    'ms_end' => microtime(),
                    'ms_js'   => number_format(microtime(true)-$api_start_at, 10,  '.',  ''),
                    'url' => $uri . $url,
                    'method' => $method,
                    'request_options' => json_encode_cn($options),
                    'status_code' => $response->getStatusCode(),
                    'response_body' => $response->getBody()->read(250)
                ]);
                $response->getBody()->rewind();
            }

            if (!empty($extra['return_type']) && ($extra['return_type'] == 'html')) {
                $results = $response->getBody()->getContents();
            } else {
                $results = json_decode($response->getBody()->getContents(), true);
            }

            if (json_last_error() != JSON_ERROR_NONE) {
                $results = $response->getBody()->getContents();
            }

            if ($this instanceof RequestSuccess) {
                $response = call_user_func([$this, 'requestSuccess'], $results, $requestBody, $response);
                if ($response) {
                    return $response;
                }
            }
            $this->response_status_code = $response->getStatusCode();
            return $results;
        } catch (RequestException $e) {
            Log::error('error_req_json:' . json_encode($json));
            Log::error('error_req_1:' . $e->getMessage());
            $this->response_status_code = $e->getCode();
            if ($e->getCode() == 401) { //401刷新token重新发起一次请求
                if ($retry) {
                    $this->refreshToken();
                    $retry--;
                    goto beginning;
                } else {
                    Log::error('token无效' . $e->getMessage());
                }
            }

            //解析返回主体是否有错误信息
            if ($e->getResponse()) {
                $results = json_decode((string)$e->getResponse()->getBody(), true);

                if ($e->getCode() == 400 || $e->getCode() == 40000) {
                    if (!empty($results['error']) && ($results['error'] == 'access_denied') && $retry) {
                        $this->refreshToken();
                        $retry--;
                        goto beginning;
                    } else {
                        return $results;
                    }
                } else {
                    if (json_last_error() == JSON_ERROR_NONE) {
                        return $results;
                    }
                }
            }
            throw $e;
        } catch (\Exception $e) {
            Log::error('error_req_2:' . $e->getMessage());
            $this->response_status_code = $e->getCode();
            throw $e;
        }
    }

    protected function return($error, $msg = '', $data = [])
    {
        return [
            'code' => $error,
            'msg'  => $msg,
            'data' => $data,
        ];
    }

    public function getStatusCode()
    {
        return $this->response_status_code;
    }

    protected function log($msg, $data, $type = 'error')
    {
        Logger::$type($msg, $data);
    }

}
