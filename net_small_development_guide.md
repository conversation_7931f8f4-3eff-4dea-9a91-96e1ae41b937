# Net Small 模块开发指南

## 项目概述

`application/net_small` 是一个基于 ThinkPHP 5 框架的小程序商城后端模块，主要负责处理商品、订单、用户、支付等核心业务逻辑。

## 目录结构

```
application/net_small/
├── behavior/           # 行为类（钩子处理）
├── command/           # 命令行任务（定时任务、队列任务）
│   └── migrate/       # 数据迁移任务
└── controller/        # 控制器层
```

## 核心架构模式

### 1. 控制器继承体系

所有控制器都继承自 `Common` 基类：

```php
class Goods extends Common
{
    use ResponseTrait;
    // ...
}
```

#### Common 基类功能
- **用户认证**: JWT token 验证
- **参数解密**: unionid、member_id、openid 等参数解密
- **会话管理**: 用户信息存储到 session
- **品牌识别**: 根据 channel_type 识别品牌
- **统一初始化**: `_initialize()` 方法处理通用逻辑

### 2. 响应处理模式

使用 `ResponseTrait` 统一处理 API 响应：

```php
// 成功响应
return $this->setResponseSuccess($data)->send();

// 错误响应
return $this->setResponseError('错误信息', 400)->send();

// 带状态码的错误响应
return $this->setResponseError('请登录!', 401)->send();
```

### 3. 参数验证模式

使用场景化验证器：

```php
public function someAction(SomeValidate $validate)
{
    $requestData = $this->request->only(array_keys($validate->getRuleKey()));
    $result = $validate->scene("scene_name")->check($requestData);
    
    if (empty($result)) {
        return $this->setResponseError($validate->getError())->send();
    }
    
    // 业务逻辑处理
}
```

## 编码规范与习惯

### 1. 类命名规范

- **控制器**: 大驼峰命名，如 `Goods.php`、`OrderAfterSale.php`
- **行为类**: 大驼峰 + Behavior 后缀，如 `GrowthValueBehavior.php`
- **命令类**: 大驼峰命名，如 `OrderNotice.php`

### 2. 方法命名规范

- **公共方法**: 小驼峰命名，如 `getGoodsList()`
- **私有方法**: 小驼峰命名，如 `sj_ccs()`
- **静态方法**: 小驼峰命名，如 `doIt()`

### 3. 变量命名规范

- **类属性**: 下划线命名，如 `$order_model`、`$user_id`
- **局部变量**: 下划线命名，如 `$request_data`、`$order_list`
- **常量**: 大写下划线，如 `$point_times = 10`

### 4. 数组定义习惯

```php
// 会员等级定义
private $car_vip = [
    '会员金卡', '会员金卡(VIP)', '会员金卡（VIP）', 
    '会员银卡', '会员银卡VIP', '会员银卡（VIP）', 
    '员工卡', '铂金卡', '黑卡'
];

// 商品ID数组
private $jk_goods = [2780, 2782, 2784, 2786, 2788, 2790];
```

### 5. 数据库操作模式

```php
// 模型初始化
$this->order_model = new BuOrder();

// 查询构建
$where = ['order_status' => ['not in', [1,3,5,8,15,18]]];
$list = $order_model->alias('a')
    ->join('t_bu_order_commodity b', 'a.order_code=b.order_code')
    ->where($where)
    ->field('SUM(count) sc, commodity_set_id')
    ->group('b.commodity_set_id')
    ->select();
```

## 核心功能模块

### 1. 用户认证与授权

#### Token 验证
```php
public function goCheckToken()
{
    $token = Request::instance()->header('access-token');
    $key = md5(config('nissan_en_key'));
    $info = JWT::decode($token, $key, ['HS256']);
    return !empty($info) && $info->exp > time();
}
```

#### 用户信息获取
```php
protected function checkToken()
{
    // 从加密参数中获取用户信息
    $this->unionid = nissanDecrypt($common_data['unique_sm']);
    $this->memberid = nissanDecrypt($common_data['member_id']);
    $this->openid = nissanDecrypt($common_data['open_sm']);
}
```

### 2. 缓存与锁机制

#### Redis 锁
```php
$rr_name = 'user-get-card-redis:' . $this->user_id;
$rock_res = getRedisLock($rr_name, 3);
if (!$rock_res) {
    return $this->setResponseError('请勿频繁操作!', 414)->send();
}
```

#### 缓存清理
```php
// 通过行为类清理缓存
Hook::exec('app\\net_small\\behavior\\CacheClear', 'run', $params);
```

### 3. 队列任务处理

#### 队列推送
```php
$gv_data = ['data' => $params, 'created_at' => time()];
Queue::push('app\common\queue\GrowthValue', json_encode($gv_data), config('queue_type.growth'));
```

#### 命令行任务
```php
class Order extends Base
{
    public static function doIt()
    {
        trace('cron order value start');
        static::loadConfig();
        // 业务逻辑处理
        trace('cron order value end');
    }
}
```

### 4. 行为钩子系统

#### 行为类定义
```php
class GrowthValueBehavior
{
    public function run(&$params)
    {
        // 加载配置
        Config::load(ROOT_PATH . 'config/net_small/config.php');
        
        // 处理业务逻辑
        $gv_data = ['data' => $params, 'created_at' => time()];
        Queue::push('app\common\queue\GrowthValue', json_encode($gv_data));
        
        return true;
    }
}
```

#### 行为调用
```php
Hook::exec('app\\net_small\\behavior\\PushInfoBehavior', 'run', $detail_param);
```

## API 文档注解规范

使用 `hg\apidoc\annotation` 进行 API 文档注解：

```php
/**
 * @title 商品列表
 * @description 获取商品列表接口
 * @param name:page type:int require:0 default:1 other: desc:页码
 * @param name:limit type:int require:0 default:10 other: desc:每页数量
 * @return error: 0 成功 1 错误
 * @return msg: 提示信息
 * @return data: 返回数据
 * <AUTHOR>
 * @url /net-small/goods/list
 * @method GET
 */
```

## 错误处理模式

### 1. 异常捕获
```php
try {
    // 业务逻辑
} catch (\Exception $e) {
    Log::error("order error : " . $e->getMessage());
    return $this->setResponseError('系统错误')->send();
}
```

### 2. 参数验证失败
```php
if (empty($result)) {
    return $this->setResponseError($validate->getError())->send();
}
```

### 3. 业务逻辑错误
```php
if (!$this->user_id) {
    return $this->setResponseError('请登录!', 401)->send();
}
```

## 配置管理

### 动态配置加载
```php
Config::load(ROOT_PATH . 'config/net_small/config.php');
Config::load(ROOT_PATH . 'config/net_small/' . config('app_status') . '.php');
```

### 环境区分
```php
if (config('app_status') == "develop") {
    $ids = [2940, 2941];
} else {
    $ids = [3610, 3611];
}
```

## 日志记录规范

### 错误日志
```php
Log::error("order notice error : " . $e->getMessage());
```

### 调试日志
```php
Logger::error('user_input-test:', ['m' => $common_data, 'gversion' => request()->header('gversion')]);
```

### 追踪日志
```php
trace('cron order value start');
// 业务逻辑
trace('cron order value end');
```

## 开发建议

### 1. 代码组织
- 控制器只处理请求响应，业务逻辑放到 service 层
- 复杂查询封装到 model 中
- 使用行为类处理横切关注点

### 2. 性能优化
- 合理使用 Redis 缓存
- 数据库查询使用索引
- 大数据量处理使用队列

### 3. 安全考虑
- 所有用户输入都要验证
- 敏感操作使用 Redis 锁
- API 接口都要进行身份验证

### 4. 可维护性
- 统一的错误处理机制
- 完善的日志记录
- 清晰的代码注释和文档

## 常用工具函数

```php
// 获取缓存用户信息
$user = getCacheUser($user_id);

// 获取 Redis 锁
$lock = getRedisLock($key, $expire_time);

// 参数解密
$unionid = nissanDecrypt($encrypted_data);

// Redis 操作
redis($key, $value);
```

## 服务层架构 (net_service)

### 1. 服务层目录结构

```
application/common/net_service/
├── aggregation/           # 聚合服务层（领域服务）
│   ├── CardService.php
│   ├── CommodityService.php
│   ├── OrderService.php
│   └── ...
├── Common.php            # 服务层基类
├── NetGoods.php          # 商品服务
├── NetOrder.php          # 订单服务
├── NetUser.php           # 用户服务
├── NetCard.php           # 卡券服务
├── PayCenter.php         # 支付中心
├── Seckill.php           # 秒杀服务
├── SendSms.php           # 短信服务
├── WxAppPay.php          # 微信支付服务
└── ...
```

### 2. 服务层继承体系

所有服务类都继承自 `Common` 基类：

```php
class NetGoods extends Common
{
    protected $sku_model;
    protected $sp_model;
    protected $com_model;
    
    public function __construct()
    {
        parent::__construct();
        $this->sku_model = new DbCommoditySku();
        $this->sp_model = new DbSpecValue();
        $this->com_model = new DbCommodity();
    }
}
```

#### 服务层基类功能
- **统一响应格式**: 使用 `ResponseTrait` 处理返回数据
- **模型初始化**: 在构造函数中初始化相关模型
- **公共方法**: 提供通用的业务逻辑方法
- **缓存管理**: 统一的缓存策略

### 3. 服务层编码模式

#### 方法命名规范
```php
// 获取数据类方法
public function getCommodityInfo($id, $channel_type)

// 处理业务类方法  
public function goodsList($requestData, $user, $channel_type)

// 私有辅助方法
private function sj_ccs()

// 静态工具方法
public static function buildOrderNo($prefix)
```

#### 参数传递模式
```php
public function goodsList($requestData, $user, $channel_type, $where = [])
{
    $this->user = $user;
    $this->channel_type = $channel_type;
    $this->user_id = $user['id'];
    
    // 业务逻辑处理
    return $this->re_msg($data);
}
```

#### 返回数据格式
```php
// 成功返回
return $this->re_msg($data, 200);

// 错误返回  
return $this->re_msg('错误信息', 400);

// 使用 ResponseTrait
return $this->setResponseSuccess($data);
return $this->setResponseError('错误信息', 400);
```

### 4. 核心服务类分析

#### NetGoods (商品服务)
```php
class NetGoods extends Common
{
    // 商品相关常量定义
    private $car_vip = ['会员金卡', '会员银卡', '员工卡'];
    private $jk_goods = [2780, 2782, 2784];
    
    // 核心业务方法
    public function goodsList($requestData, $user, $channel_type)
    public function detail($requestData, $user, $channel_type)
    public function getCommodityInfo($id, $channel_type)
    
    // 价格计算相关
    public function getCommoditySegmentDiscount($commodityId, $brand_id)
}
```

#### NetOrder (订单服务)
```php
class NetOrder extends Common
{
    // 订单状态常量
    private $point_times = 10;
    private $car_vip = ['会员金卡', '会员银卡'];
    
    // 核心业务方法
    public function fill($requestData, $user, $channel_type)
    public function confirm($requestData, $user, $channel_type)
    public function go_pay($requestData, $user, $channel_type)
    public function order_list($requestData, $user, $channel_type)
    
    // 订单状态管理
    public function orderChange($order_code)
    public function orderSave($order_code, $pay_id, $source)
}
```

#### NetUser (用户服务)
```php
class NetUser extends Common
{
    // 核心业务方法
    public function get_card($card_ids, $act_code, $source, $user)
    public function car_center($user, $channel_type, $requestData)
    public function card_center_page($user, $channel_type, $requestData)
    
    // 用户权限验证
    private function canGetCards($user, $card_arr, $channel_type)
}
```

#### PayCenter (支付中心)
```php
class PayCenter extends Common
{
    // 支付方式处理
    public function un_order_ly($user, $split_orders, $order, $channel_type)
    public function pay_status($order_code)
    
    // 支付渠道适配
    private function getPayChannel($channel_type)
}
```

### 5. 服务层设计模式

#### 工厂模式
```php
// 根据渠道类型创建不同的支付服务
private function getPayChannel($channel_type)
{
    switch ($channel_type) {
        case 'GWSM':
        case 'QCSM':
            return 'payment';
        case 'PZ1ASM':
        case 'PZ1AAPP':
            return 'payment_pz';
        default:
            return 'payment';
    }
}
```

#### 策略模式
```php
// 不同会员等级的积分策略
private function getPointStrategy($user)
{
    if (in_array($user['card_degree'], $this->car_vip)) {
        return new VipPointStrategy();
    }
    return new NormalPointStrategy();
}
```

#### 模板方法模式
```php
// 订单处理模板
public function processOrder($order_data)
{
    $this->validateOrder($order_data);
    $this->calculatePrice($order_data);
    $this->saveOrder($order_data);
    $this->sendNotification($order_data);
}
```

### 6. 缓存策略

#### 缓存命名规范
```php
// 用户缓存
$user_cache_key = 'cmn_usr_che1_' . $member_id;

// 商品缓存  
$goods_cache_key = 'goods_detail_' . $goods_id . '_' . $channel_type;

// 分类缓存
$class_cache_key = config('cache_prefix.catalog') . $channel_type;
```

#### 缓存使用模式
```php
$cache_key = 'some_cache_key';
$data = redis($cache_key);
if (!$data) {
    $data = $this->getDataFromDatabase();
    redis($cache_key, $data, mt_rand(300, 600));
}
return $data;
```

### 7. 第三方服务集成

#### 支付服务集成
```php
// 微信支付
$wxPay = new WxAppPay();
$result = $wxPay->unifiedorder($openid, $out_trade_no, $attach, $body, $total_fee);

// 支付中心
$payment = Payment::create('payment');
$result = $payment->unifiedOrder($params);
```

#### 短信服务集成
```php
$sms = new SendSms();
$result = $sms->send_sms($type, [
    'phone' => $phone,
    'commodity_name' => $commodity_name,
    'type' => 1
]);
```

### 8. 错误处理与日志

#### 统一错误处理
```php
try {
    // 业务逻辑
    $result = $this->processBusinessLogic();
    return $this->re_msg($result);
} catch (\Exception $e) {
    Logger::error('业务处理错误', [
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ]);
    return $this->re_msg('系统错误', 500);
}
```

#### 业务日志记录
```php
Logger::error('订单处理日志', [
    'order_code' => $order_code,
    'user_id' => $user_id,
    'action' => 'order_create',
    'result' => $result
]);
```

### 9. 服务层最佳实践

#### 单一职责原则
- 每个服务类只负责一个业务领域
- 方法功能单一，职责明确

#### 依赖注入
```php
public function __construct()
{
    parent::__construct();
    $this->orderModel = new BuOrder();
    $this->commodityModel = new DbCommodity();
}
```

#### 接口隔离
```php
// 定义服务接口
interface PaymentServiceInterface
{
    public function createOrder($params);
    public function queryOrder($order_id);
    public function refundOrder($order_id, $amount);
}
```

#### 配置外部化
```php
// 从配置文件读取业务参数
private function getBusinessConfig()
{
    return [
        'point_times' => config('business.point_times', 10),
        'vip_levels' => config('business.vip_levels', []),
    ];
}
```

这个开发指南总结了 `net_small` 模块的核心架构、编码规范和最佳实践，包括控制器层和服务层的完整分析，可以帮助开发者快速理解和参与项目开发。