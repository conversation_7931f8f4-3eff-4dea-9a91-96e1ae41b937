-- 备注示例：时间日期 + 需求功能

CREATE TABLE `supplier_order_push_log` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
  `order_detail_no` VARCHAR(64) NOT NULL COMMENT '订单编码',
  `total_amount` INT NOT NULL DEFAULT 0 COMMENT '订单总额(分)',
  `delivery_fee` INT NOT NULL DEFAULT 0 COMMENT '运费(分)',
  `actual_payment_amount` INT NOT NULL DEFAULT 0 COMMENT '订单实付现金(分)',
  `order_status` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '订单状态',
  `receipt_address` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '收货地址',
  `remark` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '备注',
  `created_date` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '下单时间',
  `coupon_discount_amount` INT NOT NULL DEFAULT 0 COMMENT '卡券优惠总额(分)',
  `payment_method` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '支付方式',
  `order_paid_time` DATETIME DEFAULT NULL COMMENT '支付时间',
  `total_payment_integral` INT NOT NULL DEFAULT 0 COMMENT '订单实付积分',
  `user_name` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '用户姓名',
  `user_mobile` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '用户手机号',
  `register_mobile` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '注册手机号',
  `delivery_callback_url` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '回调地址',
  `split_time` DATETIME DEFAULT NULL COMMENT '结算时间',
  `split_status` TINYINT NOT NULL DEFAULT 0 COMMENT '结算状态',
  `activity_discount_amount` INT NOT NULL DEFAULT 0 COMMENT '活动优惠总额(分)',
  `detail` TEXT COMMENT '商品明细',
  `push_status` TINYINT NOT NULL DEFAULT 0 COMMENT '传送状态 0-失败 1-成功',
  `audit_status` TINYINT NOT NULL DEFAULT 0 COMMENT '第三方审单状态',
  `push_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '传送时间',
  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;